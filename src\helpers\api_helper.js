import axios from "axios";
import accessToken from "./jwt-token-access/accessToken";
import toastr from "toastr";
import { ReasonsEnum, ReasonsTranslation } from "../constant/constants";

toastr.options = {
  positionClass: "toast-top-right",
  timeOut: 5000,
  extendedTimeOut: 1000,
  closeButton: true,
  showEasing: "swing",
  hideEasing: "linear",
  showMethod: "fadeIn",
  hideMethod: "fadeOut",
  hideDuration: 1000,
};

//pass new generated access token here
const token = accessToken;

//apply base url for axios
const API_URL = "";

const axiosApi = axios.create({
  baseURL: API_URL,
});

axiosApi.defaults.headers.common["Authorization"] = token;

axiosApi.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(error)
);

export async function get(url, config = {}) {
  return await axiosApi
    .get(url, { ...config })
    .then((response) => response.data);
}

export async function post(url, data, config = {}) {
  return axiosApi
    .post(url, { ...data }, { ...config })
    .then((response) => response.data);
}

export async function put(url, data, config = {}) {
  return axiosApi
    .put(url, { ...data }, { ...config })
    .then((response) => response.data);
}

export async function del(url, config = {}) {
  return await axiosApi
    .delete(url, { ...config })
    .then((response) => response.data);
}

export const appendIfNotNull = (key, value, formData) => {
  if (value !== null && value !== undefined && value !== "") {
    formData.append(key, value);
  }
};

export function formatISOToCustomDate(isoString) {
  const date = new Date(isoString);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

export const handleBackendErrors = ({ error, setError }) => {
  if (error?.response) {
    if (error.response.data.errors) {
      Object.keys(error.response.data.errors).forEach((field) => {
        // Handle specific field name mappings (client_id, bill_type_id, delegate_id)
        const fieldName = field.endsWith("_id") ? field : field;

        setError(fieldName, {
          type: "manual",
          message: error.response.data.errors[field][0],
        });
      });
      const errorData = error?.response?.data?.errors;
      // Convert errors object to a list of messages (if needed)
      const errorList =
        errorData &&
        Object.keys(errorData).map((key) => ({
          field: key,
          message: errorData[key].join(", "),
        }));

      errorList.map((item) => {
        toastr.error(item?.message);
      });
    } else {
      toastr.error(error?.response?.data?.message);
    }
  }
};

export const validateDateTime = (value) => {
  // Attempt to parse the value as a valid date-time
  const parsedDate = new Date(value);
  return !isNaN(parsedDate.getTime()); // Valid if not NaN
};

export function getReasonsKeyByValue(value) {
  return Object.keys(ReasonsEnum).find((key) => ReasonsEnum[key] === value);
}

export function getValueByKey(key) {
  return ReasonsEnum[key];
}

// Helper function to format the date to 'YYYY-MM-DD'
export const formatDate = (date) => {
  if (date instanceof Date && !isNaN(date)) {
    return date?.toISOString().split("T")[0];
  } else {
    console.error("Invalid Date object");
    return "";
  }
};

// Calculate today's date and the end date (two days later)
export const today = new Date();
export const endDate = new Date(today);
endDate.setDate(today.getDate() + 2);

// Helper function to format the date to 'YYYY-MM-DDTHH:mm' (local time)
export const formatDateLocalTime = (date) => {
  if (!(date instanceof Date) || isNaN(date)) {
    console.error("Invalid Date object");
    return;
  }

  // Get local date/time components
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export const customEndDate = ({ numberOfDay }) => {
  const current = new Date(today);
  return current.setDate(today.getDate() + 2);
};

export const getTodayDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

export const getTranslatedReason = ({ value, t }) => {
  const reasonKey = getReasonsKeyByValue(value);
  const number = getValueByKey(reasonKey);
  return ReasonsTranslation[number];
};

export const hasPermission = (permissionName) => {
  const userStore = localStorage.getItem("authUser");
  const parsedStore = userStore ? JSON.parse(userStore) : null;
  // const permissions = parsedStore?.user?.permissions[0]?.permissions || [];
  const permissions = parsedStore?.user?.permission || [];

  return permissions.some((perm) => perm.name.includes(permissionName));
};

export function truncateText({ text, maxLength, maxLengthPercent = 0.2 }) {
  if (typeof text !== "string") return "";

  let finalMaxLength = maxLength;
  if (
    typeof maxLengthPercent === "number" &&
    maxLengthPercent > 0 &&
    typeof window !== "undefined"
  ) {
    const screenWidth = window.innerWidth;
    const avgCharWidth = 10;
    finalMaxLength = Math.floor(
      (screenWidth * maxLengthPercent) / avgCharWidth
    );
  }

  if (text.length <= finalMaxLength) return text;
  return text.slice(0, finalMaxLength) + "...";
}
