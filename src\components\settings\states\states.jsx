import { Link } from "react-router-dom";
import <PERSON><PERSON><PERSON>oader from "react-spinners/ClipLoader";
import TableContainer from "../../Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import { statesQueries } from "../../../apis/states/query";
import {
  Button,
  Col,
  Label,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Row,
} from "reactstrap";
import toastr from "toastr";
import { useForm } from "react-hook-form";
import { handleBackendErrors } from "../../../helpers/api_helper";
import { statesAPis } from "../../../apis/states/api";
import { useTranslation } from "react-i18next";
import { Can } from "../../permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const States = () => {
  const [selectedId, setSelectedId] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteMdal, setOpendeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const {
    data: states,
    isLoading: isLoadingStates,
    refetch,
  } = statesQueries.useGetAll({});
  const { data: state, isLoading: isLoadingState } = statesQueries.useGet({
    id: selectedId,
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    watch,
    register,
    setError,
  } = useForm({ defaultValues: { name: "" } });

  const handelCLoseModal = () => {
    setOpendeleteModal(false);
    setSelectedId(0);
    setOpenModal(false);
    reset({ name: "" });
  };
  const { t } = useTranslation();
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-start">
            <Can permission={"setting.update"}>
              <Link
                className="text-primary"
                onClick={() => {
                  handelAdd();
                  setSelectedId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"setting.destroy"}>
              <Link
                onClick={() => {
                  setSelectedId(cellProps.id);
                  setOpendeleteModal(true);
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      states?.result?.length > 0
        ? states.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: index + 1,
              name: item?.name || "----",
            }))
            .reverse()
        : [],
    [states?.result]
  );

  useEffect(() => {
    if (selectedId && state?.result) {
      reset({
        name: state.result.name,
      });
    }
  }, [state?.result]);

  const handelAdd = () => {
    setOpenModal(true);
  };

  const addFun = async (data) => {
    try {
      const response = await statesAPis.add({ payload: { name: data.name } });
      toastr.success(response.message);
      handelCLoseModal();
      refetch();
      reset();
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };
  const UpdateFun = async (data) => {
    try {
      const response = await statesAPis.update({
        payload: { name: data.name },
        id: selectedId,
      });
      toastr.success(response.message);
      refetch();
      reset();
      handelCLoseModal();
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await statesAPis.deleteFu({
        id: selectedId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      reset();
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div>
      {isLoadingStates ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <TableContainer
          hideSHowGFilter={false}
          columns={columns || []}
          data={rowData || []}
          isPagination={false}
          hidePagination
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("common.states")}
          handleOrderClicks={handelAdd}
          iscustomPageSize={true}
          isBordered={true}
          customPageSize={10}
          customComponent={<div>{t("common.states")}</div>}
          className="custom-header-css table align-middle table-nowrap"
          tableClassName="table-centered align-middle table-nowrap mb-0"
          theadClassName="text-muted table-light"
          canPermission="setting.store"
        />
      )}

      <Modal isOpen={openModal} toggle={handelCLoseModal} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {selectedId ? t("common.update_states") : t("common.add_states")}
        </ModalHeader>
        <ModalBody>
          <Row>
            <form
              onSubmit={
                selectedId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
              }
            >
              {isLoadingState ? (
                <div className="container-loading">
                  <ClipLoader color="#ddd" size={50} />
                </div>
              ) : (
                <>
                  <Row>
                    <Col xs={12}>
                      <div className="mb-2">
                        <Label
                          className="form-label"
                          htmlFor="title-in-english"
                        >
                          {t("common.name_of_state")}
                        </Label>
                        <input
                          {...register("name", { required: true })}
                          placeholder="----"
                          type="text"
                          className={`form-control ${
                            errors.name ? "is-invalid" : ""
                          }`}
                          id="title-in-english"
                        />
                        {errors.name && (
                          <div className="invalid-feedback">
                            {errors.name.message}
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </>
              )}
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <Button type="button" color="light" onClick={handelCLoseModal}>
                  {t("common.close")}
                </Button>
                {/* {!isShow && ( */}
                <Button
                  color="primary"
                  className="waves-effect waves-light primary-button"
                  type="submit"
                  disabled={isSubmitting || !watch("name")}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectedId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
                {/* )} */}
              </div>
            </form>
          </Row>
        </ModalBody>
      </Modal>
      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("common.states")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button type="button" color="light" onClick={handelCLoseModal}>
              {t("common.close")}
            </Button>
            <Button onClick={DeleteFun} type="button" color="danger">
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};

export default States;
