// import React, { useState } from "react";
// import { <PERSON><PERSON>, <PERSON>, Mo<PERSON>ody } from "reactstrap";
// import ImageUploading from "react-images-uploading";
// import { useTranslation } from "react-i18next";
// import { AuthAPis } from "../../apis/auth/api";
// import toastr from "toastr";
// import { handleBackendErrors } from "../../helpers/api_helper";

// const ImageUpload = ({
//   images = [],
//   onChange,
//   maxNumber = 1,
//   isMultiple = false,
//   isDisabled = false,
//   onError,
//   className = "",
//   label,
// }) => {
//   const { t } = useTranslation();
//   const [isImageLoading, setIsImageLoading] = useState(false);
//   const [previewImage, setPreviewImage] = useState(null);

//   const getFileIcon = (fileType) => {
//     if (fileType?.includes("image")) return "ri-image-line";
//     if (fileType?.includes("pdf")) return "ri-file-pdf-line";
//     if (fileType?.includes("excel") || fileType?.includes("spreadsheet"))
//       return "ri-file-excel-line";
//     if (fileType?.includes("word") || fileType?.includes("document"))
//       return "ri-file-word-line";
//     return "ri-file-line";
//   };

//   const getFileType = (fileType) => {
//     if (fileType?.includes("image")) return "Image";
//     if (fileType?.includes("pdf")) return "PDF";
//     if (fileType?.includes("excel") || fileType?.includes("spreadsheet"))
//       return "Excel";
//     if (fileType?.includes("word") || fileType?.includes("document"))
//       return "Word";
//     return "File";
//   };

//   const handleImageDelete = async (imageIds) => {
//     try {
//       setIsImageLoading(true);
//       const response = await AuthAPis.deleteImage({
//         ids: Array.isArray(imageIds) ? imageIds : [imageIds],
//       });
//       toastr.success(response.message);
//       // Remove the deleted images from the images array
//       const updatedImages = images.filter((img) => !imageIds.includes(img.id));
//       onChange(updatedImages);
//     } catch (error) {
//       if (onError) {
//         onError(error);
//       } else {
//         handleBackendErrors({ error });
//       }
//     } finally {
//       setIsImageLoading(false);
//     }
//   };

//   const handleDeleteAll = async () => {
//     if (images.length === 0) return;

//     try {
//       setIsImageLoading(true);
//       // Get all image IDs
//       const imageIds = images.map((img) => img.id).filter((id) => id !== null);

//       if (imageIds.length > 0) {
//         const response = await AuthAPis.deleteImage({ ids: imageIds });
//         toastr.success(response.message);
//       }

//       // Clear all images
//       onChange([]);
//     } catch (error) {
//       if (onError) {
//         onError(error);
//       } else {
//         handleBackendErrors({ error });
//       }
//     } finally {
//       setIsImageLoading(false);
//     }
//   };

//   const handleImageChange = (imageList) => {
//     // Transform the imageList to include file property for new uploads
//     const transformedImages = imageList.map((img) => ({
//       ...img,
//       file: img.file || null,
//       id: img.id || null,
//     }));
//     onChange(transformedImages);
//   };

//   // Check if we've reached the maximum number of images
//   const hasReachedMaxImages = images.length >= maxNumber;

//   return (
//     <div className={`image-upload-container ${className}`}>
//       <div className="d-flex justify-content-between align-items-center mb-3">
//         <label className="form-label">{label}</label>
//         {images.length > 1 && (
//           <Button
//             color="danger"
//             className="btn-sm waves-effect waves-light"
//             onClick={handleDeleteAll}
//             disabled={isImageLoading || isDisabled}
//           >
//             <i className="ri-delete-bin-line me-1"></i>
//             {t("common.delete_all")}
//           </Button>
//         )}
//       </div>
//       <ImageUploading
//         multiple={isMultiple}
//         value={images}
//         onChange={handleImageChange}
//         maxNumber={maxNumber}
//         dataURLKey="data_url"
//         acceptType={["jpg", "jpeg", "png", "pdf", "doc", "docx", "xls", "xlsx"]}
//         onError={(err) => {
//           console.error("Upload Error: ", err);
//           toastr.error("صيغة الملف غير مدعومة أو حجم الملف كبير جدًا");
//         }}
//         disabled={isDisabled || hasReachedMaxImages}
//       >
//         {({
//           imageList,
//           onImageUpload,
//           onImageRemoveAll,
//           onImageUpdate,
//           onImageRemove,
//           isDragging,
//           dragProps,
//         }) => (
//           <div className="upload__image-wrapper">
//             {!isDisabled &&
//               ((isMultiple && !hasReachedMaxImages) ||
//                 (!isMultiple && imageList.length === 0)) && (
//                 <div
//                   className="upload-area"
//                   style={{
//                     border: isDragging
//                       ? "2px dashed #2196f3"
//                       : "2px dashed #ddd",
//                     borderRadius: "10px",
//                     padding: "20px",
//                     textAlign: "center",
//                     cursor: isDisabled ? "not-allowed" : "pointer",
//                     opacity: isDisabled ? 0.7 : 1,
//                   }}
//                   onClick={!isDisabled ? onImageUpload : undefined}
//                   {...dragProps}
//                 >
//                   <i className="ri-upload-cloud-2-line display-4 text-muted"></i>
//                   <h4 className="mt-2 mb-0">
//                     {t("common.drop_files_here_or_click_to_upload")}
//                   </h4>
//                   <p className="text-muted mt-2">
//                     {t("common.supported_formats")}: JPG, PNG, PDF, DOC, DOCX,
//                     XLS, XLSX
//                   </p>
//                 </div>
//               )}
//             {imageList.length > 0 && (
//               <div
//                 className="image-preview-container"
//                 style={{
//                   display: "flex",
//                   gap: "1.5rem",
//                   marginTop: "1rem",
//                   flexWrap: "wrap",
//                 }}
//               >
//                 {imageList.map((image, index) => (
//                   <div
//                     key={index}
//                     className="image-preview-item"
//                     style={{
//                       position: "relative",
//                       borderRadius: 10,
//                       overflow: "hidden",
//                       boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
//                       width: "200px",
//                       background: "#f8f9fa",
//                       padding: "10px",
//                     }}
//                   >
//                     {image.file?.type?.includes("image") ? (
//                       <img
//                         src={image["data_url"]}
//                         alt=""
//                         style={{
//                           width: "100%",
//                           height: "120px",
//                           objectFit: "cover",
//                           borderRadius: 10,
//                           cursor: "pointer",
//                         }}
//                         onClick={() => setPreviewImage(image["data_url"])}
//                       />
//                     ) : (
//                       <div
//                         style={{
//                           width: "100%",
//                           height: "120px",
//                           display: "flex",
//                           flexDirection: "column",
//                           alignItems: "center",
//                           justifyContent: "center",
//                           background: "#fff",
//                           borderRadius: 10,
//                           border: "1px solid #dee2e6",
//                         }}
//                       >
//                         <i
//                           className={`${getFileIcon(
//                             image.file?.type
//                           )} display-4 text-primary`}
//                         ></i>
//                         <span className="mt-2 text-muted">
//                           {getFileType(image.file?.type)}
//                         </span>
//                       </div>
//                     )}
//                     <div className="mt-2 text-center">
//                       <small className="text-muted d-block text-truncate">
//                         {image.file?.name || "File"}
//                       </small>
//                     </div>
//                     {(isMultiple || index === 0) && (
//                       <div
//                         style={{
//                           position: "absolute",
//                           top: 10,
//                           right: 10,
//                           display: "flex",
//                           gap: "0.5rem",
//                           background: "rgba(255,255,255,0.9)",
//                           padding: "5px",
//                           borderRadius: "5px",
//                         }}
//                       >
//                         <Button
//                           color="primary"
//                           className="btn-sm waves-effect waves-light"
//                           onClick={() => onImageUpdate(index)}
//                           style={{
//                             padding: "0.15rem 0.3rem",
//                             display: "flex",
//                             alignItems: "center",
//                             justifyContent: "center",
//                           }}
//                           disabled={isImageLoading || isDisabled}
//                           title={t("common.edit_image")}
//                         >
//                           <i
//                             className="ri-edit-line"
//                             style={{ fontSize: 10 }}
//                           ></i>
//                         </Button>
//                         <Button
//                           color="danger"
//                           className="btn-sm waves-effect waves-light"
//                           onClick={() => {
//                             if (image.id) {
//                               handleImageDelete([image.id]);
//                             } else {
//                               onImageRemove(index);
//                             }
//                           }}
//                           style={{
//                             padding: "0.15rem 0.3rem",
//                             display: "flex",
//                             alignItems: "center",
//                             justifyContent: "center",
//                           }}
//                           disabled={isImageLoading || isDisabled}
//                           title={t("common.delete_image")}
//                         >
//                           <i
//                             className="ri-delete-bin-line"
//                             style={{ fontSize: 10 }}
//                           ></i>
//                         </Button>
//                       </div>
//                     )}
//                   </div>
//                 ))}
//               </div>
//             )}
//           </div>
//         )}
//       </ImageUploading>

//       {/* Image Preview Modal */}
//       <Modal
//         isOpen={!!previewImage}
//         toggle={() => setPreviewImage(null)}
//         centered
//         size="lg"
//       >
//         <ModalBody className="p-0">
//           {previewImage && (
//             <img
//               src={previewImage}
//               alt={t("common.image_preview")}
//               style={{
//                 width: "100%",
//                 height: "auto",
//                 maxHeight: "80vh",
//                 objectFit: "contain",
//               }}
//             />
//           )}
//         </ModalBody>
//       </Modal>
//     </div>
//   );
// };

// export default ImageUpload;

import React, { useState } from "react";
import { Button, Modal, ModalBody } from "reactstrap";
import ImageUploading from "react-images-uploading";
import { useTranslation } from "react-i18next";
import { AuthAPis } from "../../apis/auth/api";
import toastr from "toastr";
import { handleBackendErrors } from "../../helpers/api_helper";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import { IoCloudUploadOutline } from "react-icons/io5";

const ImageUpload = ({
  images = [],
  onChange,
  maxNumber = 1,
  isMultiple = false,
  isDisabled = false,
  onError,
  className = "",
  label,
}) => {
  const { t } = useTranslation();
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);

  const handleImageDelete = async (imageIds) => {
    try {
      setIsImageLoading(true);
      const response = await AuthAPis.deleteImage({
        ids: Array.isArray(imageIds) ? imageIds : [imageIds],
      });
      toastr.success(response.message);
      // Remove the deleted images from the images array
      const updatedImages = images.filter((img) => !imageIds.includes(img.id));
      onChange(updatedImages);
    } catch (error) {
      if (onError) {
        onError(error);
      } else {
        handleBackendErrors({ error });
      }
    } finally {
      setIsImageLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    if (images.length === 0) return;

    try {
      setIsImageLoading(true);
      // Get all image IDs
      const imageIds = images.map((img) => img.id).filter((id) => id !== null);

      if (imageIds.length > 0) {
        const response = await AuthAPis.deleteImage({ ids: imageIds });
        toastr.success(response.message);
      }

      // Clear all images
      onChange([]);
    } catch (error) {
      if (onError) {
        onError(error);
      } else {
        handleBackendErrors({ error });
      }
    } finally {
      setIsImageLoading(false);
    }
  };

  const handleImageChange = (imageList) => {
    // Transform the imageList to include file property for new uploads
    const transformedImages = imageList.map((img) => ({
      ...img,
      file: img.file || null,
      id: img.id || null,
    }));
    onChange(transformedImages);
  };

  // Check if we've reached the maximum number of images
  const hasReachedMaxImages = images.length >= maxNumber;

  return (
    <div className={`image-upload-container ${className}`}>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <label className="form-label">{label}</label>
        {images.length > 1 && (
          <Button
            color="danger"
            className="btn-sm waves-effect waves-light"
            onClick={handleDeleteAll}
            disabled={isImageLoading || isDisabled}
          >
            {/* <i className="ri-delete-bin-line me-1"></i> */}
            <MdDeleteSweep size={14} />

            {t("common.delete_all")}
          </Button>
        )}
      </div>
      <ImageUploading
        multiple={isMultiple}
        value={images}
        onChange={handleImageChange}
        maxNumber={maxNumber}
        dataURLKey="data_url"
        acceptType={["jpg", "jpeg", "png"]}
        disabled={isDisabled || hasReachedMaxImages}
      >
        {({
          imageList,
          onImageUpload,
          onImageRemoveAll,
          onImageUpdate,
          onImageRemove,
          isDragging,
          dragProps,
        }) => (
          <div className="upload__image-wrapper">
            {/* {!hasReachedMaxImages && ( */}
            <div
              className="upload-area"
              style={{
                border: isDragging ? "2px dashed #2196f3" : "2px dashed #ddd",
                borderRadius: "10px",
                padding: "20px",
                textAlign: "center",
                cursor: isDisabled ? "not-allowed" : "pointer",
                opacity: isDisabled ? 0.7 : 1,
              }}
              onClick={!isDisabled ? onImageUpload : undefined}
              {...dragProps}
            >
              {/* <i className="ri-upload-cloud-2-line display-4 text-muted"></i> */}
              <IoCloudUploadOutline size={30} />

              <h4 className="mt-2 mb-0">
                {t("common.drop_files_here_or_click_to_upload")}
              </h4>
            </div>
            {/* // )} */}
            {/* {hasReachedMaxImages && !isMultiple && (
              <div className="alert alert-warning mt-2">
                {t("common.please_delete_existing_image")}
              </div>
            )} */}
            {imageList.length > 0 && (
              <div
                className="image-preview-container"
                style={{
                  display: "flex",
                  gap: "1.5rem",
                  marginTop: "1rem",
                }}
              >
                {imageList.map((image, index) => (
                  <div
                    key={index}
                    className="image-preview-item"
                    style={{
                      position: "relative",
                      borderRadius: 10,
                      overflow: "hidden",
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    <img
                      src={image["data_url"]}
                      alt=""
                      style={{
                        width: "150px",
                        height: "100px",
                        objectFit: "cover",
                        borderRadius: 10,
                        cursor: "pointer",
                      }}
                      onClick={() => setPreviewImage(image["data_url"])}
                    />
                    {/* Show action buttons only if multiple images are allowed or if it's the first image */}
                    {(isMultiple || index === 0) && (
                      <div
                        style={{
                          position: "absolute",
                          top: 10,
                          right: 10,
                          display: "flex",
                          gap: "0.5rem",
                          background: "rgba(255,255,255,0.9)",
                          padding: "5px",
                          borderRadius: "5px",
                        }}
                      >
                        <Button
                          color="primary"
                          className="btn-sm waves-effect waves-light"
                          onClick={() => onImageUpdate(index)}
                          style={{
                            padding: "0.15rem 0.3rem",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          disabled={isImageLoading || isDisabled}
                          title={t("common.edit_image")}
                        >
                          {/* <i
                            className="ri-edit-line"
                            style={{ fontSize: 10 }}
                          ></i> */}
                          <FaPenToSquare size={12} />
                        </Button>
                        <Button
                          color="danger"
                          className="btn-sm waves-effect waves-light"
                          onClick={() => {
                            if (image.id) {
                              handleImageDelete([image.id]);
                            } else {
                              onImageRemove(index);
                            }
                          }}
                          style={{
                            padding: "0.15rem 0.3rem",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          disabled={isImageLoading || isDisabled}
                          title={t("common.delete_image")}
                        >
                          {/* <i
                            className="ri-delete-bin-line"
                            style={{ fontSize: 10 }}
                          ></i> */}
                          <MdDeleteSweep size={14} />
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </ImageUploading>

      {/* Image Preview Modal */}
      <Modal
        isOpen={!!previewImage}
        toggle={() => setPreviewImage(null)}
        centered
        size="lg"
      >
        <ModalBody className="p-0">
          {previewImage && (
            <img
              src={previewImage}
              alt={t("common.image_preview")}
              style={{
                width: "100%",
                height: "auto",
                maxHeight: "80vh",
                objectFit: "contain",
              }}
            />
          )}
        </ModalBody>
      </Modal>
    </div>
  );
};

export default ImageUpload;
