import React from "react";
// import { Redirect } from "react-router-dom";

// Authentication related pages
import Login from "../pages/Authentication/Login";
import Logout from "../pages/Authentication/Logout";
import Register from "../pages/Authentication/Register";
import ForgetPwd from "../pages/Authentication/ForgetPassword";
import AuthLockScreen from "../pages/Authentication/AuthLockScreen";

// Dashboard
import Dashboard from "../pages/Dashboard/index";

// Pages Calendar
import Calendar from "../pages/Calendar/Calendar";

// Pages Component
import Chat from "../pages/Chat/Chat";

//Ecommerce Pages
import EcommerceProducts from "../pages/Ecommerce/EcommerceProducts/index";
import EcommerceProductDetail from "../pages/Ecommerce/EcommerceProducts/EcommerceProductDetail";
import EcommerceOrders from "../pages/Ecommerce/EcommerceOrders/index";
import EcommerceCustomers from "../pages/Ecommerce/EcommerceCustomers/index";
import EcommerceCart from "../pages/Ecommerce/EcommerceCart";
import EcommerceCheckout from "../pages/Ecommerce/EcommerceCheckout";
import EcommerceShops from "../pages/Ecommerce/EcommerceShops/index";
import EcommerceAddProduct from "../pages/Ecommerce/EcommerceAddProduct";

//Email
import EmailInbox from "../pages/Email/email-inbox";
import EmailRead from "../pages/Email/email-read";

// Charts
import ChartApex from "../pages/Charts/Apexcharts";
import ChartjsChart from "../pages/Charts/ChartjsChart";
import SparklineChart from "../pages/Charts/SparklineChart";
import ChartsKnob from "../pages/Charts/jquery-knob";

// Maps
import MapsGoogle from "../pages/Maps/MapsGoogle";
import MapsVector from "../pages/Maps/MapsVector";

//Icons
import RemixIcons from "../pages/Icons/RemixIcons";
import MaterialDesign from "../pages/Icons/MaterialDesign";
import DripiIcons from "../pages/Icons/DripiIcons";
import FontAwesome from "../pages/Icons/FontAwesome";

//Utility
import StarterPage from "../pages/Utility/StarterPage";
import Maintenance from "../pages/Utility/Maintenance";
import CommingSoon from "../pages/Utility/CommingSoon";
import Timeline from "../pages/Utility/Timeline";
import FAQs from "../pages/Utility/FAQs";
import Pricing from "../pages/Utility/Pricing";
import Error404 from "../pages/Utility/Error404";
import Error500 from "../pages/Utility/Error500";

// Forms
import FormElements from "../pages/Forms/FormElements";
import FormAdvanced from "../pages/Forms/FormAdvanced";
import FormEditors from "../pages/Forms/FormEditors";
import FormValidations from "../pages/Forms/FormValidations";
import FormMask from "../pages/Forms/FormMask";
import FormUpload from "../pages/Forms/FormUpload";
import FormWizard from "../pages/Forms/FormWizard";
import FormXeditable from "../pages/Forms/FormXeditable";

//Ui
import UiAlert from "../pages/Ui/UiAlert";
import UiButtons from "../pages/Ui/UiButtons";
import UiCards from "../pages/Ui/UiCards";
import UiCarousel from "../pages/Ui/UiCarousel";
import UiDropdown from "../pages/Ui/UiDropdown";
import UiGeneral from "../pages/Ui/UiGeneral";
import UiGrid from "../pages/Ui/UiGrid";
import UiImages from "../pages/Ui/UiImages";
import UiLightbox from "../pages/Ui/UiLightbox";
import UiModal from "../pages/Ui/UiModal";
import UiProgressbar from "../pages/Ui/UiProgressbar";
import UiTabsAccordions from "../pages/Ui/UiTabsAccordions";
import UiTypography from "../pages/Ui/UiTypography";
import UiVideo from "../pages/Ui/UiVideo";
import UiSessionTimeout from "../pages/Ui/UiSessionTimeout";
import UiRating from "../pages/Ui/UiRating";
import UiRangeSlider from "../pages/Ui/UiRangeSlider";
import UiNotifications from "../pages/Ui/ui-notifications";
import UIRoundSlider from "../pages/Ui/UIRoundSlider";

//Tables
import BasicTables from "../pages/Tables/BasicTables";
import DatatableTables from "../pages/Tables/DatatableTables";
import ResponsiveTables from "../pages/Tables/ResponsiveTables";
import EditableTables from "../pages/Tables/EditableTables";

// Inner Authentication
import Login1 from "../pages/AuthenticationInner/Login";
import Register1 from "../pages/AuthenticationInner/Register";
import ForgetPwd1 from "../pages/AuthenticationInner/ForgetPassword";

import Users from "../pages/users";
import Roles from "../pages/roles";
import ActionRoles from "../pages/roles/action-roles";
import Cars from "../pages/cars";
import ContractType from "../pages/types/contract-type";
import Bonds from "../pages/types/bonds";
import ActionBonds from "../pages/types/action-bonds-types";
import ProductsUnits from "../pages/types/products-units";
import TasksTypes from "../pages/types/tasks-types";
import ProductTypesAction from "../pages/types/action-product-types";
import ProductTypes from "../pages/types/product-types";
import Clients from "../pages/clients/clients";
import ClientProducts from "../pages/clients/client-product";
import ActionClientProducts from "../pages/clients/action-client-product";
import ActionClients from "../pages/clients/action-clients";
import ClientsGroups from "../pages/clients-groups/clients-groups";
import ActionClientsGroups from "../pages/clients-groups/action-clients-groups";
import ActionCars from "../pages/cars/action-cars";
import BillTypes from "../pages/types/bill-types";
import CarLogsType from "../pages/types/car-logs-type";
import ActionProducts from "../pages/product/action-product";
import Products from "../pages/product/products";
import Reasons from "../pages/types/reasons";
import ActionReasons from "../pages/types/action-reasons";
import ApprovepaleList from "../pages/approve-list";
import Delegate from "../pages/delegate/delegate";
import StoreOperation from "../pages/store-operation/store-operation";
import ActionDelegate from "../pages/delegate/action-delegate";
import StoreRequest from "../pages/store-request/store-request";
import ActionStoreRequest from "../pages/store-request/action-store-request";
import StoreTransaction from "../pages/store-transaction/store-transaction";
import ActionTasks from "../pages/tasks/action-tasks";
import Tasks from "../pages/tasks/tasks";
import Bills from "../pages/bills/bill";
import ActionBills from "../pages/bills/action-bill";
import ActionPickUpBills from "../pages/bills/pick-up-bils";
import ActionCOntractBill from "../pages/bills/contract-bill";
import ActionSampleBill from "../pages/bills/sample-bill";
import FollowUpBills from "../pages/bills/follow-up-bills";
import Contracts from "../pages/contract/contract";
import ActionContract from "../pages/contract/action-contract";
import BondsList from "../pages/bonds/bonds";
import CatogsList from "../pages/car_logs/car_logs";
import ActionBondsList from "../pages/bonds/action-bonds";
import ActionCarLogs from "../pages/car_logs/action_car_logs";
import OfferPrice from "../pages/offer-price/offer-price";
import ActionOfferPrice from "../pages/offer-price/action-offer";
import Settings from "../pages/settings";
import DelegateReports from "../pages/delegate-reports";
import ClientInfo from "../pages/reports/client-info";
import Sales from "../pages/reports/sales";
import BondsReports from "../pages/reports/bonds";
import TotalReports from "../pages/reports/total";
import ClientsVisits from "../pages/reports/clients-visits";
import ContractsReport from "../pages/reports/contract";
import CarExpensesReports from "../pages/reports/car-report";
import ShowDelegateReport from "../pages/delegate-reports/show-delegate-reports";
import ActionVisit from "../pages/visits/action-visit";
import BillReason from "../pages/types/bill-reasons";
import { Navigate } from "react-router-dom";
import Visits from "../pages/visits";
import TaskReason from "../pages/tasks-reasons";
import ActionSalesBills from "../pages/bills/action-sales-bills";

const authProtectedRoutes = [
  // Ui
  { path: "/ui-alerts", component: UiAlert },
  { path: "/ui-buttons", component: UiButtons },
  { path: "/ui-cards", component: UiCards },
  { path: "/ui-carousel", component: UiCarousel },
  { path: "/ui-dropdowns", component: UiDropdown },
  { path: "/ui-general", component: UiGeneral },
  { path: "/ui-grid", component: UiGrid },
  { path: "/ui-images", component: UiImages },
  { path: "/ui-lightbox", component: UiLightbox },
  { path: "/ui-modals", component: UiModal },
  { path: "/ui-progressbars", component: UiProgressbar },
  { path: "/ui-tabs-accordions", component: UiTabsAccordions },
  { path: "/ui-typography", component: UiTypography },
  { path: "/ui-video", component: UiVideo },
  { path: "/ui-session-timeout", component: UiSessionTimeout },
  { path: "/ui-rating", component: UiRating },
  { path: "/ui-rangeslider", component: UiRangeSlider },
  { path: "/ui-notifications", component: UiNotifications },
  { path: "/ui-roundslider", component: UIRoundSlider },

  // Forms
  { path: "/form-elements", component: FormElements },
  { path: "/form-advanced", component: FormAdvanced },
  { path: "/form-editors", component: FormEditors },
  { path: "/form-mask", component: FormMask },
  { path: "/form-file-upload", component: FormUpload },
  { path: "/form-wizard", component: FormWizard },
  { path: "/form-validation", component: FormValidations },
  { path: "/form-xeditable", component: FormXeditable },

  //Utility
  { path: "/starter", component: StarterPage },
  { path: "/timeline", component: Timeline },
  { path: "/faqs", component: FAQs },
  { path: "/pricing", component: Pricing },

  //Icons
  { path: "/icons-remix", component: RemixIcons },
  { path: "/material-design", component: MaterialDesign },
  { path: "/dripicons", component: DripiIcons },
  { path: "/font-awesome-5", component: FontAwesome },

  // Maps
  { path: "/google-maps", component: MapsGoogle },
  { path: "/vector-maps", component: MapsVector },

  //Charts
  { path: "/apex-charts", component: ChartApex },
  { path: "/chartjs", component: ChartjsChart },
  { path: "/charts-sparkline", component: SparklineChart },
  { path: "/charts-knob", component: ChartsKnob },

  //Email
  { path: "/email-inbox", component: EmailInbox },
  { path: "/email-read", component: EmailRead },

  //Ecommerce

  { path: "/ecommerce-products", component: EcommerceProducts },
  { path: "/ecommerce-product-detail/:id", component: EcommerceProductDetail },
  { path: "/ecommerce-orders", component: EcommerceOrders },
  { path: "/ecommerce-customers", component: EcommerceCustomers },
  { path: "/ecommerce-cart", component: EcommerceCart },
  { path: "/ecommerce-checkout", component: EcommerceCheckout },
  { path: "/ecommerce-shops", component: EcommerceShops },
  { path: "/ecommerce-add-product", component: EcommerceAddProduct },

  //chat
  { path: "/chat", component: Chat },

  //calendar
  { path: "/calendar", component: Calendar },

  { path: "/dashboard", component: Dashboard },

  // real project
  { path: "/users", component: Users },
  { path: "/roles", component: Roles },
  { path: "/action-roles/:id?", component: ActionRoles },
  { path: "/cars", component: Cars },
  { path: "/contract-type", component: ContractType },
  { path: "/action-bonds-types/:id?", component: ActionBonds },
  { path: "/bonds", component: Bonds },
  { path: "/products-units", component: ProductsUnits },
  { path: "/tasks-types", component: TasksTypes },
  { path: "/product-types-action/:id?", component: ProductTypesAction },
  { path: "/product-types", component: ProductTypes },
  { path: "/action-clients/:id?", component: ActionClients },
  { path: "/clients", component: Clients },
  { path: "/action-clients-groups/:id?", component: ActionClientsGroups },
  { path: "/clients-groups", component: ClientsGroups },
  { path: "/action-cars/:id?", component: ActionCars },
  { path: "/bill-types", component: BillTypes },
  { path: "/car-logs-type", component: CarLogsType },
  { path: "/action-products", component: ActionProducts },
  { path: "/products", component: Products },
  { path: "/reasons", component: Reasons },
  { path: "/action-reasons", component: ActionReasons },
  { path: "/approve-list", component: ApprovepaleList },
  { path: "/action-delegate", component: ActionDelegate },
  { path: "/delegate", component: Delegate },
  { path: "/store-operation", component: StoreOperation },
  { path: "/store-request", component: StoreRequest },
  { path: "/store-transaction", component: StoreTransaction },
  { path: "/action-store-request", component: ActionStoreRequest },
  { path: "/tasks", component: Tasks },
  { path: "/action-tasks", component: ActionTasks },
  { path: "/bills", component: Bills },
  { path: "/action-bills", component: ActionBills },
  { path: "/action-bills-pick-up", component: ActionPickUpBills },
  { path: "/action-bills-sample", component: ActionSampleBill },
  { path: "/action-bills-contract", component: ActionCOntractBill },
  { path: "/action-sales-bills", component: ActionSalesBills },
  { path: "/follow-up-bills", component: FollowUpBills },
  { path: "/action-contract", component: ActionContract },
  { path: "/contract", component: Contracts },
  { path: "/bonds-list", component: BondsList },
  { path: "/sales", component: Sales },
  { path: "/action-bonds", component: ActionBondsList },
  { path: "/car_logs", component: CatogsList },
  { path: "/action_car_logs", component: ActionCarLogs },
  { path: "/offer-price", component: OfferPrice },
  { path: "/action-offer-price", component: ActionOfferPrice },
  { path: "/settings", component: Settings },
  { path: "/delegate-reports", component: DelegateReports },
  { path: "/show-delegate-reports", component: ShowDelegateReport },
  { path: "/car_expenses", component: CarExpensesReports },
  { path: "/client-info", component: ClientInfo },
  { path: "/bonds-report", component: BondsReports },
  { path: "/total-report", component: TotalReports },
  { path: "/client-visit", component: ClientsVisits },
  { path: "/contract-report", component: ContractsReport },
  { path: "/visits", component: Visits },
  { path: "/action-visit", component: ActionVisit },
  { path: "/bill-reasons", component: BillReason },
  { path: "/client-product", component: ClientProducts },
  { path: "/action-client-product", component: ActionClientProducts },
  { path: "/tas-reasons", component: TaskReason },
  // this route should be at the end of all other routes
  { path: "/", exact: true, component: () => <Navigate to="/login" /> },
];

const publicRoutes = [
  { path: "/logout", component: Logout },
  { path: "/login", component: Login },
  { path: "/forgot-password", component: ForgetPwd },
  { path: "/register", component: Register },
  { path: "/lock-screen", component: AuthLockScreen },

  // Authentication Inner
  { path: "/auth-login", component: Login1 },
  { path: "/auth-register", component: Register1 },
  { path: "/auth-recoverpw", component: ForgetPwd1 },

  { path: "/maintenance", component: Maintenance },
  { path: "/comingsoon", component: CommingSoon },
  { path: "/404", component: Error404 },
  { path: "/500", component: Error500 },

  // Tables
  { path: "/basic-tables", component: BasicTables },
  { path: "/datatable-table", component: DatatableTables },
  { path: "/responsive-table", component: ResponsiveTables },
  { path: "/editable-table", component: EditableTables },
];

export { authProtectedRoutes, publicRoutes };
