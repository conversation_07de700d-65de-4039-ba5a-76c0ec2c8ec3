import { useQuery } from "@tanstack/react-query";
import { ContractTypesAPis } from "./api";

const useGetAllContractTypes = ({ limit, page }) => {
  const queryResult = useQuery({
    queryKey: ["all-contract-types", limit, page],
    queryFn: () => ContractTypesAPis.getAllContractTypes({ limit, page }),
  });
  return queryResult;
};

const useGetContractType = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["contract-type", id],
    queryFn: () =>
      id > 0
        ? ContractTypesAPis.getContractTypes({ id })
        : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  console.log("queryResult", queryResult.data);

  return queryResult;
};

export const ContractQueries = {
  useGetAllContractTypes,
  useGetContractType,
};
