import { useTranslation } from "react-i18next";
import { RolesQueries } from "../../apis/roles/query";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import { RolesAPis } from "../../apis/roles/api";
import { handleBackendErrors } from "../../helpers/api_helper";
import { Link, useNavigate } from "react-router-dom";
import { Can } from "../../components/permissions-way/can";
import toastr from "toastr";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

import "./roles.scss";

const ROles = () => {
  const [pagination, setPagination] = useState(1);
  const {
    data: Roles,
    isLoading: isLoadingRoles,
    refetch,
  } = RolesQueries.useGetAllRoles({ limit: 10, page: pagination });
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const { t } = useTranslation();
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const DeleteFun = async (data) => {
    try {
      setIsDeleting(true);
      const response = await RolesAPis.deleteRole({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const navigate = useNavigate();

  const handelAddRoles = () => {
    navigate("/action-roles");
  };

  const breadcrumbItems = [{ title: t("common.roles"), link: "/Roles" }];

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("roles.users_count"),
      accessor: "users_count",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          cellProps.is_default === 0 && (
            <div className="d-flex align-items-center gap-2">
              <Can permission={"role.update"}>
                <Link
                  to={`/action-roles?id=${cellProps.id}`}
                  className="text-primary"
                >
                  {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              </Can>
              {(cellProps.users_count < 1 ||
                cellProps.users_count === "---") && (
                <Can permission={"role.destroy"}>
                  <Link
                    onClick={() => {
                      handelOpenModal();
                      handelSelectId(cellProps.id);
                    }}
                    to="#"
                    className="text-danger"
                  >
                    {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                    <MdDeleteSweep size={18} />
                  </Link>
                </Can>
              )}
              <Can permission={"role.show"}>
                <Link
                  to={`/action-roles?id=${cellProps.id}?Show=true`}
                  className="text-success"
                >
                  {/* <i className=" ri-information-fill font-size-16"></i> */}
                  <FaInfoCircle size={14} />
                </Link>
              </Can>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      Roles?.result?.length > 0
        ? Roles.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (pagination - 1) * 10 + index + 1, // 10 is your page size
              title: item.title,
              users_count: item.users_count || "---",
              is_default: item.is_default,
            }))
            .reverse()
        : [],
    [Roles?.result, t]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("common.roles")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("common.roles")}
          handleOrderClicks={handelAddRoles}
          canPermission="role.store"
        />
        <Card>
          <CardBody>
            <TableContainer
              columns={columns || []}
              data={rowData || []}
              isLoading={isLoadingRoles}
              setPage={setPagination}
              pageCount={Roles?.meta?.last_page || 1}
              currentPage={pagination}
            />
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("common.roles")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
    </div>
  );
};
export default ROles;
