import ApiInstance from "../../../constant/api-instance";
import { TASKS_ROUTES } from "./route";

const getAll = async ({ limit, page }) => {
  const { data } = await ApiInstance.get(TASKS_ROUTES.TASKS_TYPES, {
    params: {
      limit,
      page,
    },
  });
  return data;
};
const getTask = async ({ id }) => {
  const { data } = await ApiInstance.get(`${TASKS_ROUTES.TASKS_TYPES}/${id}`);
  return data;
};
const active = async ({ id }) => {
  const { data } = await ApiInstance.get(`${TASKS_ROUTES.ACTIVE}/${id}`);
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(`${TASKS_ROUTES.IN_ACTIVE}/${id}`);
  return data;
};
const add = async ({
  title,
  description,
  def_duo_days,
  is_default,
  is_settings,
  show_in_customer_dashboard,
}) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.TASKS_TYPES}`, {
    title,
    description,
    def_duo_days,
    is_default: 0,
    is_settings: 0,
    show_in_customer_dashboard,
  });
  return data;
};
const update = async ({
  title,
  description,
  def_duo_days,
  id,
  is_default,
  is_settings,
  show_in_customer_dashboard,
}) => {
  const { data } = await ApiInstance.put(`${TASKS_ROUTES.TASKS_TYPES}/${id}`, {
    title,
    description,
    def_duo_days,
    is_default,
    is_settings,
    show_in_customer_dashboard,
  });
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${TASKS_ROUTES.TASKS_TYPES}/${id}`
  );
  return data;
};
export const TasksAPis = {
  deleteFu,
  update,
  add,
  getTask,
  active,
  inActive,
  getAll,
};
