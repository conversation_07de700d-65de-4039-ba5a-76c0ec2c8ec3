import { useTranslation } from "react-i18next";
import ClipLoader from "react-spinners/ClipLoader";
import { <PERSON><PERSON>, Card, CardBody, Row } from "reactstrap";

const ActionSection = ({
  isLoading,
  handleSubmit,
  selectId,
  UpdateFun,
  addFun,
  children,
  handelCancel,
  isShow,
  isSubmitting,
  customText,
}) => {
  const { t } = useTranslation();
  return (
    <Card style={{ height: "78vh", overflowY: "auto" }}>
      <CardBody>
        <Row>
          <form
            onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
          >
            {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              children
            )}
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCancel}
              >
                {t("common.close")}
              </Button>
              {!isShow && (
                <Button
                  color="primary"
                  className="waves-effect waves-light primary-button btn-sm"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {customText ? (
                    customText
                  ) : isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
              )}
            </div>
          </form>
        </Row>
      </CardBody>
    </Card>
  );
};

export default ActionSection;
