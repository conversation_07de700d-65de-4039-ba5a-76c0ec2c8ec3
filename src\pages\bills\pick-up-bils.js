import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  <PERSON><PERSON>,
  Col,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import toastr from "toastr";
import { useLocation, useNavigate } from "react-router-dom";
import { billAPis } from "../../apis/bills/api";
import { clientsQueries } from "../../apis/clients/query";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { useForm } from "react-hook-form";
import { BillQueries } from "../../apis/bills/query";
import {
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { vatQueries } from "../../apis/vat/query";
import { productQueries } from "../../apis/products/query";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";
import { delegateQueries } from "../../apis/delegate/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import ActionSection from "../../components/Common/ActionSection";
import CustomSelect from "../../components/Common/Select";

import Input from "../../components/Common/Input";
import TextAreaField from "../../components/Common/textArea";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";

// Form Fields Components
export const FormField = ({
  field,
  errors,
  isShow,
  control,
  placeholder,
  cols = 2,
  isDisabled,
  onTotalChange,
}) => (
  <Col xs={cols} key={field.id}>
    <Input
      control={control}
      error={errors[field.name]}
      min={
        field.type === "date"
          ? formatDate(
              new Date(new Date().setFullYear(new Date().getFullYear() - 2))
            )
          : undefined
      }
      isDisabled={isDisabled || field.disable || isShow}
      step={field.type === "number" ? "any" : undefined}
      placeholder={placeholder}
      type={field.type}
      name={field.name}
      onChange={field.name === "total" ? onTotalChange : undefined}
    />
  </Col>
);

// Product Modal Components
const ProductModal = ({
  isOpen,
  onClose,
  onAdd,
  children,
  selectedProductId,
  isEditMode,
}) => {
  const { t } = useTranslation();

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} backdrop="static">
      <ModalHeader toggle={handleClose}>
        {isEditMode ? t("common.update") : t("common.add")}{" "}
        {t("common.product")}
      </ModalHeader>
      <ModalBody>
        <Row className="g-1">{children}</Row>
        <ModalFooter>
          <Button
            type="button"
            color="light"
            className="btn-sm"
            onClick={handleClose}
          >
            {t("common.close")}
          </Button>
          <Button
            type="button"
            className="btn-sm"
            onClick={onAdd}
            color="primary"
          >
            {isEditMode ? t("common.update") : t("common.add")}
          </Button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

const BillsActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [typeFromUrl, setTypeFromUrl] = useState(
    Number(queryParams.get("type"))
  );
  const [openAddModal, setOpenAddModal] = useState(false);
  const [productList, setProductList] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTotalPrice, setSelectedTotalPrice] = useState(0);
  const [selectedQuant, setSelectedQuant] = useState(0);
  const [selctedProductType, setSelectedProductType] = useState([]);
  const [selectedBills, setSelectedBills] = useState();
  const { t, i18n } = useTranslation();
  const [selectedBillType, setSelectedBillType] = useState(0);
  const [billTypeId, setBillTypeId] = useState();

  const { data, isLoading, isRefetching } = BillQueries.useGetBill({
    id: Number(selectId),
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const { data: vat } = vatQueries.useGetAll({
    enable: true,
  });

  const { data: bilTypes } = billTypesQueries.useGetAll({});

  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: bill_numbers } = BillQueries.useGetGenerateBill({
    id: typeFromUrl,
    enabled: !selectId,
  });

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const schema = yup
    .object({
      client_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      // delegate_id: yup
      //   .object()
      //   .shape({
      //     label: yup.string().required(),
      //     value: yup.number().required(),
      //   })
      //   .required(t("common.field_required")),
      total: yup
        .number()
        .min(0, t("bills.validation.greater_than_0"))
        .max(1000000, t("cars.validations.km_max"))
        .nullable(),
      bill_date: yup.string().required(t("common.field_required")),
    })
    .required();

  const initialSTate = useMemo(
    () => ({
      bill_type_id: typeFromUrl,
      client_id: null,
      pm_bill_id: null,
      contract_id: null,
      delegate_id: null,
      bill_date: formatDate(today),
      return_date: formatDate(today),
      notes: "",
      res_name: "",
      total: null,
      quant: null,
      product_id: null,
      product_note: "",
      bill_number: null,
      vat_rate: 0,
    }),
    []
  );

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    watch,
    clearErrors,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
    mode: "onBlur",
  });

  const { data: pickUpList } = BillQueries.useGetAllPickUpData({
    client_id: watch("client_id")?.value,
    enabled: typeFromUrl === 4 && watch("client_id")?.value > 0,
  });

  const handelOpenAddModal = () => {
    setIsEditMode(false);
    setSelectedProductId(0);
    setOpenAddModal(true);
  };
  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    resetInputs();

    // Explicitly reset these values
    setSelectedProductId(0);
    setIsEditMode(false);
    setSelectedQuant(0);
  };

  const resetInputs = () => {
    setValue("return_date", formatDate(today));
    setValue("pm_bill_id", null);
    setValue("bill_number", null);
    setSelectedProductId(0);
    setIsEditMode(false);
  };

  const { data: products } = productQueries.useGetAll({
    status: 1,
    // ProductTypeId: Number(watch("ProductTypeId")?.value),
    billType: selectedBillType,
  });

  useEffect(() => {
    if (watch("product_id")?.value) {
      const product = products?.result?.find(
        (item) => item.id === watch("product_id")?.value
      );

      if (product) {
        // Reset error first
        clearErrors("quant");

        // Set selected quantity for reference
        setSelectedQuant(product?.quant || 0);

        // Only validate if quant has a value
        if (
          watch("quant") !== null &&
          watch("quant") !== undefined &&
          watch("quant") !== ""
        ) {
          // Run all validations
          const validations = [
            {
              condition: Number(watch("quant")) <= 0,
              message: t("bills.validation.greater_than_0"),
            },
          ];

          // Check each validation rule
          const failedValidation = validations.find((v) => v.condition);

          if (failedValidation) {
            setError("quant", {
              type: "manual",
              message: failedValidation.message,
            });
          }
        }
      }
    }
  }, [watch("quant"), watch("product_id"), i18n.language, products?.result]);

  const fieldsNames = [
    {
      name: "bill_date",
      isRequired: false,
      errorMessage: errors?.bill_date?.message,
      label: t("bills.bill_date"),
      type: "date",
      placeholder: "",
      showIn: true,
    },
    {
      name: "res_name",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("common.responsible"),
      type: "text",
      showIn: true,
      disabled: isShow,
    },
    {
      name: "return_date",
      isRequired: false,
      errorMessage: errors?.return_date?.message,
      label: t("bills.return_bill"),
      type: "date",
      showIn: typeFromUrl === 4, // PickupMaintenance Bill
    },
  ];

  const totalsFields = [
    {
      name: "total",
      isRequired: false,
      errorMessage: errors?.total?.message,
      label: t("bills.pickup_maintenance_price"),
      type: "number",
      disable: false,
      showIn: true, // Sales, Return, Contract Bills
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "note",
      label: t("common.note"),
      isRequired: false,
      showIn: true,
    },
  ];

  const productTypesOption = useSetSelectOptions({
    data: selctedProductType,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item.title.en : item.title.ar, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  const billsOptions = useSetSelectOptions({
    data: pickUpList?.result,
    getOption: (item) => ({
      label: item.bill_number,
      value: item.id,
    }),
  });

  const productsOptions = useSetSelectOptions({
    data: products?.result,
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  const productsFields = [
    {
      id: 0,
      field: (
        <Col xs={6} className="mb-2">
          <CustomSelect
            control={control}
            name="product_id"
            placeholder={t("common.product")}
            isDisabled={
              isShow || watch("pm_bill_id")?.value || typeFromUrl === 4
            }
            options={productsOptions}
            error={errors?.product_id}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 1,
      field: (
        <Col xs={6}>
          <Input
            control={control}
            name="quant"
            isDisabled={!watch("product_id") || isShow}
            placeholder={t("common.quant")}
            type="number"
            min={1}
            max={5000}
            error={errors?.quant}
          />
        </Col>
      ),
      showIn: true,
    },
  ];

  useEffect(() => {
    if (bilTypes?.result) {
      const selectedBillType = bilTypes?.result.find(
        (item) => item.type === typeFromUrl
      );
      setSelectedBillType(selectedBillType?.id);
    }
  }, [bilTypes?.result]);

  useEffect(() => {
    if (bilTypes?.result?.length > 0) {
      const foundItem = bilTypes.result.find(
        (item) => item.type === Number(typeFromUrl)
      );
      setBillTypeId(foundItem?.id);
    }
  }, [bilTypes?.result]);

  const handelFilterFromProductList = (id) => {
    // Filter out the item with the given id from both productList and rowData
    const updatedProductList = productList.filter(
      (item) => item.product_id !== id
    );

    // Update the productList state (this will automatically trigger a re-render)
    setProductList(updatedProductList);

    // Recalculate totals after removing a product
    setTimeout(() => {
      recalculateTotals();
    }, 0);
  };

  const recalculateTotals = ({ totalValue, hasValue, vat_rate } = {}) => {
    if (productList.length > 0) {
      // Calculate total of all products
      let total = 0;
      let discounts = 0;
      productList.forEach((item) => {
        total += Number(item.total) || 0;
        discounts += Number(item.discount_value) || 0;
      });

      // Get the current manually set total, if any
      const currentTotal = hasValue ? totalValue : watch("total");
      const rowTotalDiscount = watch("total_discounts");

      // Check if we should allow manual total override
      // For bill types 3 (PickupMaintenance), 4 (DeliverMaintenance), 6 (Sales Bill), and 7 (Contract Bill)
      const allowManualOverride = [3, 4, 6, 7].includes(typeFromUrl);

      // If manual override is allowed and a value exists, use it
      if (
        allowManualOverride &&
        currentTotal !== null &&
        currentTotal !== undefined &&
        currentTotal !== ""
      ) {
        // Use the manually set total for further calculations
        total = Number(currentTotal) - Number(rowTotalDiscount);
      } else {
        // Otherwise set the calculated total
        // setValue("total", total);
      }
      setValue("total_discounts", discounts);

      // Get tax rate from form
      const taxRate = hasValue ? vat_rate : Number(watch("vat_rate")) || 0;

      // Calculate the final total price with tax included
      let finalTotal;
      if (taxRate > 0 && vat?.data?.vat_status === 1) {
        // Add tax to the total: finalTotal = totalBeforeTax + (totalBeforeTax * taxRate/100)
        const taxAmount = (currentTotal * taxRate) / 100;
        finalTotal = currentTotal + taxAmount;
      }

      // Update the selectedTotalPrice state (which is the total after tax)
      const finalTotalValue = Math.max(finalTotal, 0);
      if (typeFromUrl !== 7) {
        setSelectedTotalPrice(finalTotalValue);
      }

      // Store the calculated total in a hidden form field to persist it
      setValue("calculated_total_with_tax", finalTotalValue);

      // Force update the selectedTotalPrice in the form data to ensure it's available for submission
      if (typeFromUrl !== 3) {
        setValue("selectedTotalPrice", finalTotalValue);
      }

      // Log the calculation for debugging
    }
  };

  const handelAddProductToList = () => {
    try {
      // Capture all form values upfront to avoid stale closures
      const productId = watch("product_id");

      const quant = Number(watch("quant"));

      if (quant <= 0) {
        setError("quant", {
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (!quant) {
        setError("quant", {
          message: t("bills.validation.quant_required"),
        });
        return;
      }

      if (watch("quant") && Number(watch("quant")) === 0 && openAddModal) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }
      if (watch("quant") && Number(watch("quant")) > 5000) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }

      if (productId && quant > 0) {
        // Extract all form values
        const productNote = watch("product_note");
        const ProductTypeId = watch("ProductTypeId");

        // Create new product object
        const newProduct = {
          product_id: productId.value,
          product_name: productId.label,
          maintaince_quant: quant,
          notes: productNote,
          ProductTypeId: ProductTypeId?.value,
        };

        if (isEditMode && selectedProductId) {
          // In edit mode, directly modify the productList
          const newProductList = [...productList];

          // Find the product in the list by matching product_id with selectedProductId
          const indexToUpdate = newProductList.findIndex(
            (item) => Number(item.product_id) === Number(selectedProductId)
          );

          if (indexToUpdate !== -1) {
            // Update the existing product
            newProductList[indexToUpdate] = newProduct;
            setProductList(newProductList);
          } else {
            // Fallback: add as new if not found (shouldn't happen)

            setProductList([...productList, newProduct]);
          }
        } else {
          // In add mode, simply append to the list
          setProductList([...productList, newProduct]);
        }

        // Reset form and close modal
        // resetInputs();

        setValue("product_id", null);
        setValue("quant", null);
        setValue("product_note", "");
        setValue("ProductTypeId", null);
        setOpenAddModal(false);
        setSelectedQuant(0);
        setSelectedProductId(0);
        setIsEditMode(false);

        // Force recalculation of totals with a slight delay to ensure state is updated
        setTimeout(() => {
          recalculateTotals();
        }, 100);
      }
    } catch (error) {
      console.log("Error handling product:", error);
    }
  };

  const handelSetUpdate = (id) => {
    // Clear any existing errors first
    clearErrors();

    // Find the product in the list
    const currentProduct = productList.find((item) => item.product_id === id);

    if (!currentProduct) {
      return;
    }

    // Set the selected product ID *before* opening the modal
    setSelectedProductId(id);
    setIsEditMode(true);

    // Open the modal after setting the state
    setOpenAddModal(true);

    // Set values with a slight delay to ensure form is ready
    setTimeout(() => {
      setValue("product_id", {
        label: currentProduct.product_name,
        value: currentProduct.product_id,
      });
      setValue("quant", currentProduct.maintaince_quant);
      setValue("product_note", currentProduct.notes);
      const SelectedProductType = {
        label: productTypesOption.find(
          (opt) => opt.value === currentProduct.ProductTypeId
        )?.label,
        value: currentProduct.ProductTypeId,
      };

      if (currentProduct.ProductTypeId) {
        setValue("ProductTypeId", SelectedProductType);
      }

      // Log the current state after setting values
    }, 100);
  };

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];

      // Update typeFromUrl with bill_type_id from backend when editing
      if (data?.result.bill_type && data?.result.bill_type.id) {
        setTypeFromUrl(data?.result.bill_type.type);
        setBillTypeId(data?.result.bill_type.id);
      }

      data?.result.details?.map((item) => {
        returnedProduct.push({
          product_id: item.product.id,
          product_name: item.product.name,
          maintaince_quant: item.maintaince_quant,
          notes: item.notes,
          ProductTypeId: item?.product?.product_type?.id,
        });
      });
      setProductList(returnedProduct);

      const taxAmount = data?.result?.vat_rate / 100;
      const original_value = data?.result?.total / (1 + taxAmount);

      // Populate form with role data when loaded
      reset({
        client_id: {
          label: `${data?.result.client.company_name}/${
            data?.result.client.full_name || "---"
          }`,
          value: data?.result.client.id,
        },
        bill_date: data?.result.bill_date?.split(" ")[0],
        res_name: data?.result.res_name,
        delegate_id: {
          label: data?.result.delegate?.full_name,
          value: data?.result.delegate?.id,
        },
        total: typeFromUrl === 3 ? data?.result?.total : original_value,
        vat_rate: data?.result?.vat_rate || 0,
        return_date: data?.result.return_date?.split(" ")[0],
        notes: data?.result.notes,
        bill_number: {
          label: data?.result?.related_bill_number,
          value: data?.result.related_bill_number,
        },
      });

      // Make sure to recalculate totals after loading data
      setTimeout(() => {
        if (typeFromUrl === 4)
          recalculateTotals({
            totalValue: data?.result.total,
            hasValue: true,
            vat_rate: data?.result?.vat_rate,
          });
        else {
          recalculateTotals({
            totalValue: data?.result.total,
            hasValue: true,
            vat_rate: data?.result?.vat_rate,
          });
        }
      }, 100);
    }
  }, [data, isLoading, isRefetching]);

  useEffect(() => {
    if (watch("bill_number")?.value) {
      const value = pickUpList?.result.find(
        (item) => item.id === watch("bill_number")?.value
      );

      setSelectedBills(value);
    }
  }, [watch("bill_number")?.value]);

  // ! reset after pick up list and sample bill
  useEffect(() => {
    if (pickUpList?.result?.length > 0 && selectedBills) {
      const returnedProduct = [];
      // const taxAmount = selectedBills?.vat_rate / 100;
      // const original_value = selectedBills?.total / (1 + taxAmount);
      // console.log("original_value", selectedBills?.total);
      setValue("total", selectedBills?.total);
      selectedBills?.details?.map((item) => {
        returnedProduct.push({
          product_id: item.product.id,
          product_name: item.product.name,
          quant: item.maintaince_quant,
          notes: item.notes,
          ProductTypeId: item?.product?.product_type?.id,
        });
      });
      setProductList(returnedProduct);
      setTimeout(() => {
        recalculateTotals({ totalValue: data?.result.total, hasValue: true });
      }, 200);
    }
  }, [pickUpList, selectedBills]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (_data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }
    try {
      // Ensure the discount values are the latest
      // recalculateTotals();
      const taxRate = Number(watch("vat_rate")) || 0;
      const dataToSend = {
        products: productList,
        contract_id: _data.contract_id?.value,
        client_id: _data.client_id?.value,
        bill_date: _data.bill_date,
        bill_type_id: data?.result?.bill_type.id,
        return_date: _data.return_date,
        notes: _data.notes,
        delegate_id: _data?.delegate_id?.value || null,
        // total: selectedTotalPrice,
        total:
          typeFromUrl === 3 || !selectedTotalPrice
            ? _data.total
            : selectedTotalPrice,
        vat_rate: taxRate,
        res_name: _data.res_name,
      };

      const response = await billAPis.update({
        payload: dataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }
    try {
      const taxRate = Number(watch("vat_rate")) || 0;

      const dataToSend = {
        products: productList,
        contract_id: data.contract_id?.value,
        client_id: data.client_id?.value,
        bill_date: data.bill_date,
        bill_type_id: billTypeId,
        return_date: data.return_date,
        notes: data.notes,
        total:
          typeFromUrl === 3 || !selectedTotalPrice
            ? data.total
            : selectedTotalPrice,
        vat_rate: taxRate,
        delegate_id: data?.delegate_id?.value || null,
        res_name: data.res_name,
        pm_bill_id:
          Number(data.bill_number?.value) > 0
            ? Number(data.bill_number?.value)
            : null,
      };

      const response = await billAPis.add({ payload: dataToSend });
      toastr.success(response.message);
      navigate(-1);
      reset(); // Reset form after successful submission
      setProductList([]);
    } catch (error) {
      console.log("error", error);
      handleBackendErrors({ error, setError });

      // Scroll to the top to show errors
      window.scrollTo(0, 0);
    }
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "product_note",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              {typeFromUrl !== 4 && (
                <div
                  className="text-primary"
                  onClick={() => {
                    console.log(
                      "Edit clicked for product ID:",
                      cellProps.product_id
                    );
                    handelSetUpdate(cellProps.product_id);
                  }}
                >
                  {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                  <FaPenToSquare size={14} />
                </div>
              )}
              {typeFromUrl !== 4 && (
                <div
                  onClick={() => {
                    handelFilterFromProductList(cellProps.product_id);
                  }}
                  className="text-danger"
                >
                  {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                  <MdDeleteSweep size={18} />
                </div>
              )}
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.maintaince_quant,
              total: item.total,
              product_note: item.notes,
            }))
            .reverse()
        : [],
    [productList, i18n.language]
  );

  useEffect(() => {
    if (typeFromUrl > 0 && bilTypes?.result) {
      const selectedBillType = bilTypes.result.find(
        (item) => item.id === typeFromUrl
      );
      if (selectedBillType) {
        setSelectedProductType(selectedBillType?.product_types || []);
      }
    }
  }, [typeFromUrl, bilTypes?.result]);

  useEffect(() => {
    if (Number(watch("bill_type_id")) > 0) {
      const _selectedBillTypes = bilTypes?.result?.find(
        (item) => item.type === Number(watch("bill_type_id"))
      );
      setSelectedProductType(_selectedBillTypes?.product_types);
    }
  }, [watch("bill_type_id"), openAddModal, bilTypes?.result]);

  const delegateOptions = useSetSelectOptions({
    data: delegates?.result,
    getOption: (item) => ({
      label: item.full_name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  useEffect(() => {
    if (vat?.data && vat?.data?.vat_status === 1) {
      setValue("vat_rate", vat.data.vat_rate);
    }
  }, [vat?.data]);

  useEffect(() => {
    if (watch("total")) {
      recalculateTotals();
    }
  }, [watch("total")]);

  const typesToShow = [
    { label: t("types.bill.sales_bill"), value: 6 },
    { label: t("types.bill.return_bill"), value: 2 },
    { label: t("types.bill.change_oil"), value: 5 },
    { label: t("types.bill.contract_bill"), value: 7 },
    { label: t("types.bill.pickup_maintenance_bill"), value: 3 },
    { label: t("types.bill.deliver_maintenance_bill"), value: 4 },
    { label: t("types.bill.sample_bill"), value: 1 },
    { label: t("types.bill.return_sample_bill"), value: 9 },
  ];

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("bills.bills")}
          addTitle={t("common.add") + " " + t("common.product")}
          handleOrderClicks={handelOpenAddModal}
          canPermission="bill.store"
          isAddOptions={!isShow && typeFromUrl === 3}
          titleOfSection={t("bills.bills")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
        />

        <ActionSection
          isLoading={isLoading || isRefetching}
          handleSubmit={handleSubmit}
          selectId={selectId}
          UpdateFun={UpdateFun}
          addFun={addFun}
          handelCancel={handelCancel}
          isShow={isShow}
          isSubmitting={isSubmitting}
        >
          {(data?.result?.bill_number || bill_numbers) && (
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {t("bills.bill_type") +
                ": " +
                typesToShow.find((item) => item.value === typeFromUrl)
                  ?.label}{" "}
              /{" "}
              {t("bills.bill_number") +
                ": " +
                (data?.result?.bill_number || bill_numbers)}
            </h1>
          )}
          <Row className="g-2">
            {fieldsNames.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    isDisabled={field.disabled || field.disable}
                    t={t}
                    control={control}
                    placeholder={field.label}
                  />
                )
            )}

            <Col xs={2}>
              <CustomSelect
                control={control}
                error={errors.client_id}
                label={t("common.client")}
                options={clientOptions}
                name="client_id"
                isDisabled={isShow || selectId}
              />
            </Col>

            <Col xs={2}>
              <CustomSelect
                control={control}
                error={errors.delegate_id}
                label={t("common.delegate")}
                options={delegateOptions}
                name="delegate_id"
                isDisabled={isShow}
              />
            </Col>
            {typeFromUrl === 4 && (
              <Col xs={2}>
                <CustomSelect
                  control={control}
                  error={errors.bill_number}
                  label={t("bills.bill_number")}
                  options={billsOptions}
                  name="bill_number"
                  isDisabled={isShow || selectId}
                />
              </Col>
            )}

            {textAreaField.map((field) => (
              <Col key={field.id} xs={5}>
                <TextAreaField
                  name="notes"
                  control={control}
                  placeholder={t("common.note")}
                  defaultValue=""
                  className="mb-2"
                  rows={1}
                  disabled={isShow}
                  error={errors?.notes}
                />
              </Col>
            ))}

            <TableContainer
              hideSHowGFilter={true}
              columns={columns || []}
              data={rowData}
              hidePagination
              isPagination={true}
              isAddOptions={!isShow}
              iscustomPageSize={true}
              isBordered={true}
              isSmall
              canPermission="bill.add"
              customPageSize={10}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
            {totalsFields.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    isDisabled={field.disabled || field.disable}
                    t={t}
                    control={control}
                    placeholder={field.label}
                    // onTotalChange={handleTotalChange}
                  />
                )
            )}
            {((!selectId && vat?.data?.vat_status === 1) ||
              (selectId && data?.result?.vat_rate > 0)) &&
            typeFromUrl !== 3 ? (
              <Col xs={12} className="mt-2">
                <Row>
                  <Col xs={2} className="mb-2">
                    <Input
                      control={control}
                      name="vat_rate"
                      isDisabled={true}
                      placeholder={t("common.vat_rate")}
                      type="number"
                      error={errors?.vat_rate}
                    />
                  </Col>
                </Row>
              </Col>
            ) : (
              ""
            )}

            {selectedTotalPrice >= 0 &&
            typeFromUrl !== 3 &&
            ((!selectId && vat?.data?.vat_status === 1) ||
              (selectId && data?.result?.vat_rate > 0)) ? (
              <div
                style={{
                  padding: 10,
                  borderTop: "1px solid #ccc",
                  marginTop: 20,
                  fontSize: 15,
                  fontWeight: "bold",
                }}
              >
                {t("common.total")}: {selectedTotalPrice.toFixed(2)}
              </div>
            ) : (
              ""
            )}
            <div className="mb-1" />
          </Row>
          {typeFromUrl === 3 && <div style={{ marginBottom: 8 }}></div>}
        </ActionSection>
        <ProductModal
          isOpen={openAddModal}
          onClose={handelCloseAddModal}
          onAdd={handelAddProductToList}
          selectedProductId={selectedProductId}
          isEditMode={isEditMode}
        >
          <Row className="g-2">
            {productsFields.map((item) => item.showIn && item.field)}

            <Col xs={12} className="mt-2">
              <TextAreaField
                placeholder={t("common.note")}
                name="product_note"
                control={control}
                defaultValue=""
                className="mb-2"
                disabled={isShow}
              />
            </Col>
          </Row>
        </ProductModal>
      </Container>
    </div>
  );
};

export default BillsActions;
