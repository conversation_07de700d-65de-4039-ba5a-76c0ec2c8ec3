import ApiInstance from "../../constant/api-instance";
import { SECTIONS_ROUTES } from "./route";

const getAll = async ({ limit, page, sectionId }) => {
  const { data } = await ApiInstance.get(SECTIONS_ROUTES.TEMPLATE + sectionId, {
    params: { limit, page },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${SECTIONS_ROUTES.SECTION}${id}`);
  return data;
};

const add = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(
    `${SECTIONS_ROUTES.TEMPLATE}${id}`,
    payload
  );
  return data;
};
const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${SECTIONS_ROUTES.SECTION}${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  await ApiInstance.delete(`${SECTIONS_ROUTES.SECTION}${id}`);
};

export const sectionsAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
};
