import ApiInstance from "../../constant/api-instance";
import { BILL_ROUTES } from "./route";

const getAll = async ({ limit, page, searchParams }) => {
  const { data } = await ApiInstance.get(BILL_ROUTES.BILLS, {
    params: {
      limit,
      page,
      ...searchParams,
    },
  });
  return data;
};

const getGenerateBills = async ({ id }) => {
  const { data } = await ApiInstance.get(BILL_ROUTES.GENERATE_BILLS + id, {});
  return data;
};

const getAllFollwUp = async ({ limit, page }) => {
  const { data } = await ApiInstance.get(BILL_ROUTES.FOLLOW_UP, {
    params: {
      limit,
      page,
    },
  });
  return data;
};

const getAllPickUp = async ({ client_id }) => {
  const { data } = await ApiInstance.get(
    BILL_ROUTES.PICKUP_BILLS + "/" + client_id,
    {
      params: {},
    }
  );
  return data;
};

const getSampleBill = async ({ client_id }) => {
  const { data } = await ApiInstance.get(
    BILL_ROUTES.SAMPLE_BILL + "/" + client_id,
    {
      params: {},
    }
  );
  return data;
};

const getAllListForDelete = async ({ limit, page }) => {
  const { data } = await ApiInstance.get(BILL_ROUTES.BILL_FOR_DELETE, {
    params: {
      limit,
      page,
    },
  });
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${BILL_ROUTES.BILLS}/${id}`);
  return data;
};

const getOneFollwUp = async ({ id }) => {
  const { data } = await ApiInstance.get(`${BILL_ROUTES.FOLLOW_UP}/${id}`);
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(`${BILL_ROUTES.BILLS}`, payload);
  return data;
};
const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(
    `${BILL_ROUTES.BILLS}/${id}`,
    payload
  );
  return data;
};

const deleteBill = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${BILL_ROUTES.BILLS}/${id}`);
  return data;
};
const deleteFollowUp = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${BILL_ROUTES.FOLLOW_UP}/${id}`);
  return data;
};

const deleteForDelete = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${BILL_ROUTES.DELETE_BILL_FOR_DELETE}${id}`
  );
  return data;
};

const changeStatus = async ({ id }) => {
  const { data } = await ApiInstance.post(
    `${BILL_ROUTES.BILLS}/${id}${BILL_ROUTES.CHANGE_STATUS}`
  );
  return data;
};

export const billAPis = {
  getAll,
  getAllListForDelete,
  getOne,
  add,
  update,
  deleteBill,
  deleteForDelete,
  getAllFollwUp,
  getOneFollwUp,
  deleteFollowUp,
  getAllPickUp,
  getGenerateBills,
  getSampleBill,
  changeStatus,
};
