import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Col, Container, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState, useRef } from "react";
import toastr from "toastr";
import { useLocation, useNavigate } from "react-router-dom";
import { billAPis } from "../../apis/bills/api";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { useForm } from "react-hook-form";
import { BillQueries } from "../../apis/bills/query";
import {
  handleBackendErrors,
  today,
  formatDate,
} from "../../helpers/api_helper";
import { vatQueries } from "../../apis/vat/query";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";
import ActionSection from "../../components/Common/ActionSection";
import Input from "../../components/Common/Input";
import { FormField } from "./action-bill";
import HeaderSection from "./components/headerSection";
import ProductSection from "./components/productSection";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";

const BillsActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [typeFromUrl, setTypeFromUrl] = useState(
    Number(queryParams.get("type"))
  );
  const [openAddModal, setOpenAddModal] = useState(false);
  const [productList, setProductList] = useState([]);
  const [selectedBillType, setSelectedBillType] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const { t, i18n } = useTranslation();

  // Track which total discount field the user edited last to avoid infinite loops
  const lastTotalDiscountEditedRef = useRef(null); // 'value' | 'percentage' | null
  const [selectedTotalPrice, setSelectedTotalPrice] = useState(0);
  const [billTypeId, setBillTypeId] = useState();

  const { data, isLoading, isRefetching } = BillQueries.useGetBill({
    id: Number(selectId),
  });

  const { data: vat } = vatQueries.useGetAll({
    enable: true,
  });

  const { data: bilTypes } = billTypesQueries.useGetAll({});

  const { data: bill_numbers } = BillQueries.useGetGenerateBill({
    id: typeFromUrl,
    enabled: !selectId,
  });

  const navigate = useNavigate();

  useEffect(() => {
    if (bilTypes?.result) {
      const selectedBillType = bilTypes?.result.find(
        (item) => item.type === typeFromUrl
      );
      setSelectedBillType(selectedBillType?.id);
    }
  }, [bilTypes?.result]);

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const schema = yup
    .object({
      client_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
      bill_date: yup.string().required(t("common.field_required")),
      total_discount_value: yup
        .number()
        .transform((value, originalValue) =>
          originalValue === "" ? null : value
        )
        .min(0)
        .nullable(),

      total_discount_percentage: yup
        .number()
        .transform((value, originalValue) =>
          originalValue === "" ? null : value
        )
        .min(0)
        .max(100)
        .nullable(),
      vat_rate: yup.number().min(0).nullable(),
    })
    .required();

  const initialSTate = useMemo(
    () => ({
      client_id: null,
      bill_date: formatDate(today),
      return_date: formatDate(today),
      notes: "",
      res_name: "",
      total: null,
      quant: null,
      product_id: null,
      product_note: "",
      product_gift: null,
      product_total: null,
      product_price: null,
      discount_product_value: null,
      discount_product_percentage: null,
      bill_number: null,
      vat_rate: 0,
      discount_value: null,
      discount_percentage: null,
    }),
    []
  );

  const typesToShow = [
    { label: t("types.bill.sales_bill"), value: 6 },
    { label: t("types.bill.return_bill"), value: 2 },
    { label: t("types.bill.change_oil"), value: 5 },
    { label: t("types.bill.contract_bill"), value: 7 },
    { label: t("types.bill.pickup_maintenance_bill"), value: 3 },
    { label: t("types.bill.deliver_maintenance_bill"), value: 4 },
    { label: t("types.bill.sample_bill"), value: 1 },
    { label: t("types.bill.return_sample_bill"), value: 9 },
  ];

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isDirty, dirtyFields },
    register,
    setError,
    control,
    setValue,
    watch,
    clearErrors,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
    mode: "onBlur",
  });

  const handelOpenAddModal = () => {
    setIsEditMode(false);
    setSelectedProductId(0);
    setOpenAddModal(true);
    // Clear any existing errors when opening modal
    clearErrors();
  };

  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    setIsEditMode(false);
    setSelectedProductId(0);

    // Clear all product form fields
    setValue("product_id", null);
    setValue("quant", "");
    setValue("product_price", "");
    setValue("product_total", "");
    setValue("product_gift", "");
    setValue("product_note", "");
    setValue("discount_product_value", "");
    setValue("discount_product_percentage", "");

    // Clear any existing errors
    clearErrors();
  };

  const totalsFields = [
    {
      name: "total",
      isRequired: false,
      errorMessage: errors?.total?.message,
      label: t("common.rows_total"),
      type: "number",
      step: "any",
      disable: true,
      showIn: true,
    },
    {
      name: "total_discounts",
      isRequired: false,
      errorMessage: errors?.total_discounts?.message,
      label: t("common.rows_discount"),
      type: "number",
      step: "any",
      showIn: true, // Sales Bill
      disabled: true,
    },
  ];

  useEffect(() => {
    if (vat?.data && vat?.data?.vat_status === 1) {
      setValue("vat_rate", vat.data.vat_rate);
    }
  }, [vat?.data]);

  const handelSetUpdate = (id) => {
    // Clear any existing errors first
    clearErrors();

    // Find the product in the list
    const currentProduct = productList.find((item) => item.product_id === id);

    if (!currentProduct) {
      console.error("Product not found in list:", id);
      return;
    }

    console.log("currentProduct", currentProduct);

    // Set the selected product ID *before* opening the modal
    setSelectedProductId(id);
    setIsEditMode(true);

    // Populate form fields with current product data
    setValue("product_id", {
      value: currentProduct.product_id,
      label: currentProduct.product_name,
    });
    setValue("quant", currentProduct.quant);
    setValue("product_price", currentProduct.price);
    setValue("product_total", currentProduct.net_price);
    setValue("product_gift", currentProduct.gift);
    setValue("product_note", currentProduct.notes);
    setValue("discount_product_value", currentProduct.discount_value || 0);
    setValue(
      "discount_product_percentage",
      currentProduct.discount_percentage || 0
    );

    // Open the modal after setting the state
    setOpenAddModal(true);
  };

  const handleDeleteProduct = (productId) => {
    // Remove the product from the productList
    const updatedProductList = productList.filter(
      (item) => item.product_id !== productId
    );
    setProductList(updatedProductList);
  };

  // Calculate totals automatically when productList changes
  const calculatedTotals = useMemo(() => {
    let total = 0;
    let totalDiscounts = 0;

    productList.forEach((item) => {
      total += Number(item.total || 0);
      totalDiscounts += Number(item.discount_value || 0);
    });

    return {
      total: Number(total.toFixed(2)),
      totalDiscounts: Number(totalDiscounts.toFixed(2)),
    };
  }, [productList]);

  // Calculate total_before_tax including total discount value
  const totalBeforeTax = useMemo(() => {
    const totalDiscountValue = Number(watch("total_discount_value")) || 0;
    const result =
      calculatedTotals.total -
      calculatedTotals.totalDiscounts -
      totalDiscountValue;
    return Number(result.toFixed(2));
  }, [
    calculatedTotals.total,
    calculatedTotals.totalDiscounts,
    watch("total_discount_value"),
  ]);

  // Update form values when calculated totals change
  useEffect(() => {
    setValue("total", calculatedTotals.total);
    setValue("total_discounts", calculatedTotals.totalDiscounts);
  }, [calculatedTotals, setValue]);

  // Update total_before_tax when it changes
  useEffect(() => {
    setValue("total_before_tax", totalBeforeTax);
  }, [totalBeforeTax, setValue]);

  // Sync total discount percentage when total discount value changes
  useEffect(() => {
    const totalDiscountValueRaw = watch("total_discount_value");
    const totalDiscountValue = Number(totalDiscountValueRaw);

    // Run only when the user last edited the value field
    if (lastTotalDiscountEditedRef.current === "value") {
      // Use the base total (before applying total discount) for percentage calculation
      const baseTotal =
        calculatedTotals.total - calculatedTotals.totalDiscounts;
      const safeTotalBeforeTax =
        isFinite(baseTotal) && baseTotal > 0 ? baseTotal : 0;

      const clampedValue = Math.max(
        0,
        Math.min(
          isNaN(totalDiscountValue) ? 0 : totalDiscountValue,
          safeTotalBeforeTax
        )
      );

      if (clampedValue !== totalDiscountValue) {
        setValue("total_discount_value", Number(clampedValue.toFixed(2)));
      }

      const nextPerc =
        safeTotalBeforeTax > 0 ? (clampedValue / safeTotalBeforeTax) * 100 : 0;
      const currPerc = Number(watch("total_discount_percentage"));
      const roundedPerc = Number(nextPerc.toFixed(2));

      if (!isNaN(roundedPerc) && roundedPerc !== currPerc) {
        setValue("total_discount_percentage", roundedPerc);
      }
    }
  }, [watch("total_discount_value"), calculatedTotals]);

  // Sync total discount value when total discount percentage changes
  useEffect(() => {
    const totalDiscountPercRaw = watch("total_discount_percentage");
    const totalDiscountPerc = Number(totalDiscountPercRaw);

    if (lastTotalDiscountEditedRef.current === "percentage") {
      const safePerc = isNaN(totalDiscountPerc)
        ? 0
        : Math.max(0, Math.min(totalDiscountPerc, 100));

      if (safePerc !== totalDiscountPerc) {
        setValue("total_discount_percentage", Number(safePerc.toFixed(2)));
      }

      // Use the base total (before applying total discount) for value calculation
      const baseTotal =
        calculatedTotals.total - calculatedTotals.totalDiscounts;
      const nextVal =
        isFinite(baseTotal) && baseTotal > 0 ? (baseTotal * safePerc) / 100 : 0;
      const currVal = Number(watch("total_discount_value"));
      const roundedVal = Number(nextVal.toFixed(2));

      if (!isNaN(roundedVal) && roundedVal !== currVal) {
        setValue("total_discount_value", roundedVal);
      }
    }
  }, [watch("total_discount_percentage"), calculatedTotals]);

  // Calculate final total with VAT
  useEffect(() => {
    const vatRate = Number(watch("vat_rate")) || 0;

    // totalBeforeTax already includes the total discount subtraction
    // Calculate VAT amount
    const vatAmount = (totalBeforeTax * vatRate) / 100;

    // Calculate final total (total_before_tax + VAT)
    const finalTotal = totalBeforeTax + vatAmount;

    // Update VAT amount field
    setValue("vat_amount", Number(vatAmount.toFixed(2)));

    setSelectedTotalPrice(Number(finalTotal.toFixed(2)));
  }, [totalBeforeTax, watch("vat_rate"), setValue]);

  useEffect(() => {
    if (bilTypes?.result?.length > 0) {
      const foundItem = bilTypes.result.find(
        (item) => item.type === Number(typeFromUrl)
      );
      setBillTypeId(foundItem?.id);
    }
  }, [bilTypes?.result]);

  // UseEffect for loading bill data when editing
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];

      // Update typeFromUrl with bill_type_id from backend when editing
      if (data?.result.bill_type && data?.result.bill_type.id) {
        setTypeFromUrl(data?.result.bill_type.type);
        setBillTypeId(data?.result.bill_type.id);
      }

      // Process product details
      data?.result.details?.forEach((item) => {
        returnedProduct.push({
          product_id: item.product.id,
          product_name: item.product.name,
          quant: item.quant,
          gift: item.gift || 0,
          price: item.price,
          total: item.total,
          notes: item.notes || "",
          discount_value: item.discount_value || 0,
          discount_percentage: item.discount_percentage || 0,
          net_price: item.total - (item.discount_value || 0),
        });
      });

      setProductList(returnedProduct);

      // Calculate totals from products for form fields
      const calculatedTotal = returnedProduct.reduce(
        (sum, item) => sum + Number(item.total || 0),
        0
      );
      const calculatedDiscounts = returnedProduct.reduce(
        (sum, item) => sum + Number(item.discount_value || 0),
        0
      );

      // Set the selectedTotalPrice from the main object (this is the final total)
      setSelectedTotalPrice(Number(data?.result.total) || 0);

      // Populate form with bill data when loaded
      reset({
        client_id: {
          label: `${data?.result.client.company_name}${
            data?.result.client.full_name
              ? `/${data?.result.client.full_name}`
              : ""
          }`,
          value: data?.result.client.id,
        },
        bill_date: data?.result.bill_date?.split(" ")[0],
        res_name: data?.result.res_name || "",
        delegate_id: data?.result.delegate
          ? {
              label: data?.result.delegate.full_name,
              value: data?.result.delegate.id,
            }
          : null,
        notes: data?.result.notes || "",
        vat_rate: data?.result.vat_rate || 0,
        total_discount_value: data?.result.discount_value || 0,
        total_discount_percentage: data?.result.discount_percentage || 0,
        // Set calculated totals from products (for display purposes)
        total: calculatedTotal,
        total_discounts: calculatedDiscounts,
        // Calculate total before tax
        total_before_tax:
          calculatedTotal -
          calculatedDiscounts -
          (data?.result.discount_value || 0),
      });
    }
  }, [selectId, isLoading, data?.result, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (_data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }

    try {
      // Calculate total discounts from products
      let totalProductDiscounts = 0;
      productList.forEach((item) => {
        totalProductDiscounts += Number(item.discount_value || 0);
      });
      // Ensure the discount values are the latest
      // recalculateTotals();

      const discountValue = Number(watch("total_discount_value")) || 0;
      const discountPercentage =
        Number(watch("total_discount_percentage")) || 0;
      const taxRate = Number(watch("vat_rate")) || 0;

      const dataToSend = {
        products: productList,
        contract_id: _data.contract_id?.value,
        client_id: _data.client_id?.value,
        bill_date: _data.bill_date,
        bill_type_id: data?.result?.bill_type.id,
        notes: _data.notes,
        delegate_id: _data?.delegate_id?.value || null,
        products_discount: totalProductDiscounts,
        vat_rate: taxRate,
        discount_value: discountValue,
        discount_percentage: discountPercentage,
        res_name: _data.res_name,
        total: selectedTotalPrice,
      };

      const response = await billAPis.update({
        payload: dataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("bills.validation.select_product_validation"));
      window.scrollTo(0, document.body.scrollHeight);
      return;
    }

    try {
      // Calculate total discounts from products
      let totalProductDiscounts = 0;
      productList.forEach((item) => {
        totalProductDiscounts += Number(item.discount_value || 0);
      });

      const discountValue = Number(watch("total_discount_value")) || 0;
      const discountPercentage =
        Number(watch("total_discount_percentage")) || 0;
      const taxRate = Number(watch("vat_rate")) || 0;

      const dataToSend = {
        products: productList,
        contract_id: data.contract_id?.value,
        client_id: data.client_id?.value,
        bill_date: data.bill_date,
        bill_type_id: billTypeId,
        notes: data.notes,
        products_discount: totalProductDiscounts,
        vat_rate: taxRate,
        delegate_id: data?.delegate_id?.value || null,
        discount_value: discountValue,
        discount_percentage: discountPercentage,
        res_name: data.res_name,
        total: selectedTotalPrice,
      };

      const response = await billAPis.add({ payload: dataToSend });
      toastr.success(response.message);
      navigate(-1);
      reset(); // Reset form after successful submission
      setProductList([]);
    } catch (error) {
      handleBackendErrors({ error, setError });

      // Scroll to the top to show errors
      window.scrollTo(0, 0);
    }
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.gift"),
      accessor: "gift",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.price"),
      accessor: "price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: (cellProps) => (
        <div
          style={{
            display: "-webkit-box",
            WebkitLineClamp: 3,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
            width: "100%",
            wordBreak: "break-word",
            lineHeight: "1.2em",
            maxHeight: "3.6em",
          }}
        >
          {cellProps?.product_note}
        </div>
      ),
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.discount"),
      accessor: "discount_value",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.discount") + "%",
      accessor: "discount_percentage",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.net_price"),
      accessor: "net_price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  handelSetUpdate(cellProps.product_id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </div>
              <div
                onClick={() => handleDeleteProduct(cellProps.product_id)}
                className="text-danger"
                style={{ cursor: "pointer" }}
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              gift: item.gift || "----",
              price: item.price,
              total: item.total,
              product_note: item.notes,
              discount_percentage: `${item.discount_percentage}%`,
              discount_value: `${item.discount_value}`,
              discount: `${item.discount}`,
              net_price: `${item.net_price}`,
            }))
            .reverse()
        : [],
    [productList, i18n.language]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("bills.bills")}
          addTitle={t("common.add") + " " + t("common.product")}
          handleOrderClicks={handelOpenAddModal}
          canPermission="bill.store"
          isAddOptions={!isShow}
          titleOfSection={t("bills.bills")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
        />

        <ActionSection
          isLoading={isLoading || isRefetching}
          handleSubmit={handleSubmit}
          selectId={selectId}
          UpdateFun={UpdateFun}
          addFun={addFun}
          handelCancel={handelCancel}
          isShow={isShow}
          isSubmitting={isSubmitting}
        >
          {(data?.result?.bill_number || bill_numbers) && (
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {t("bills.bill_type") +
                ": " +
                typesToShow.find((item) => item.value === typeFromUrl)
                  ?.label}{" "}
              /{" "}
              {t("bills.bill_number") +
                ": " +
                (data?.result?.bill_number || bill_numbers)}
            </h1>
          )}
          <Row className="g-2">
            <HeaderSection
              control={control}
              errors={errors}
              isShow={isShow}
              register={register}
              selectId={selectId}
            />
            <TableContainer
              hideSHowGFilter={true}
              columns={columns || []}
              data={rowData}
              hidePagination
              isPagination={true}
              isAddOptions={!isShow}
              iscustomPageSize={true}
              isBordered={true}
              isSmall
              canPermission="bill.add"
              customPageSize={10}
            />
            {totalsFields.map(
              (field) =>
                field.showIn && (
                  <FormField
                    key={field.id}
                    field={field}
                    register={register}
                    errors={errors}
                    isShow={isShow}
                    isDisabled={field.disabled || field.disable}
                    t={t}
                    control={control}
                    placeholder={field.label}
                  />
                )
            )}

            <Col xs={2}>
              <Input
                control={control}
                name="total_discount_value"
                isDisabled={isShow}
                placeholder={t("bills.discount_value")}
                type="number"
                step="any"
                min={0}
                error={errors?.total_discount_value}
                isOnChange={() => {
                  lastTotalDiscountEditedRef.current = "value";
                }}
              />
            </Col>

            <Col xs={2}>
              <Input
                control={control}
                name="total_discount_percentage"
                isDisabled={isShow}
                placeholder={t("bills.discount_percentage")}
                type="number"
                step="any"
                min={0}
                max={100}
                error={errors?.total_discount_percentage}
                isOnChange={() => {
                  lastTotalDiscountEditedRef.current = "percentage";
                }}
              />
            </Col>

            <Col xs={12} row className="mt-2">
              <Row>
                {/* Total Before Tax Input Field */}
                <Col xs={2} className="mb-2 p-0 ps-3" style={{}}>
                  <Input
                    control={control}
                    name="total_before_tax"
                    isDisabled={true}
                    placeholder={t("bills.total_before_tax")}
                    type="number"
                    step="any"
                    error={errors?.total_before_tax}
                  />
                </Col>

                {/* VAT Input Field */}
                {((!selectId && vat?.data?.vat_status === 1) ||
                  (selectId && data?.result?.vat_rate > 0)) && (
                  <Col xs={2} className="mb-2 pe-0 ps-2">
                    <Input
                      control={control}
                      name="vat_rate"
                      isDisabled={true}
                      placeholder={t("common.vat_rate")}
                      type="number"
                      step="any"
                      error={errors?.vat_rate}
                    />
                  </Col>
                )}
              </Row>
            </Col>

            {!isNaN(Number(selectedTotalPrice)) &&
              Number(selectedTotalPrice) >= 0 && (
                <div
                  style={{
                    padding: 10,
                    borderTop: "1px solid #ccc",
                    marginTop: 20,
                    fontSize: 15,
                    fontWeight: "bold",
                  }}
                >
                  {t("common.total")}:{" "}
                  {(Number(selectedTotalPrice) || 0).toFixed(2)}
                </div>
              )}
          </Row>
        </ActionSection>
        <ProductSection
          control={control}
          errors={errors}
          isShow={isShow}
          openAddModal={openAddModal}
          handelCloseAddModal={handelCloseAddModal}
          typeFromUrl={typeFromUrl}
          selectedBillType={selectedBillType}
          productList={productList}
          watch={watch}
          isDirty={isDirty}
          dirtyFields={dirtyFields}
          setProductList={setProductList}
          setError={setError}
          setValue={setValue}
          isEditMode={isEditMode}
          selectedProductId={selectedProductId}
        />
      </Container>
    </div>
  );
};

export default BillsActions;
