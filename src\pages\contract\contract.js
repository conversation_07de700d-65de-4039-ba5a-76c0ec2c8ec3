import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState, useEffect } from "react";
import { ContracyQueries } from "../../apis/contract/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { contractAPis } from "../../apis/contract/api";
import { useLocation, useNavigate } from "react-router-dom";
import { formatDate, today } from "../../helpers/api_helper";
import { CreatingStatus } from "../../constant/constants";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import DeleteModal from "../../components/Common/DeleteModal";
import { useForm } from "react-hook-form";
import CustomInput from "../../components/Common/Input";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { clientsQueries } from "../../apis/clients/query";
import { ContractQueries } from "../../apis/types/contract-type/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { FaTimes } from "react-icons/fa";
import SearchCard from "../../components/Reports/search-card";
import { contractTemplateQueries } from "../../apis/contract_templete/query";
import { MdCancel, MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import { HiDotsVertical } from "react-icons/hi";
import { GrStatusGood } from "react-icons/gr";
import { TiDelete } from "react-icons/ti";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

// Custom component for amount comparison
const AmountComparisonField = ({ control, hasButtonSearch = true }) => {
  const { t } = useTranslation();

  const comparisonOptions = [
    { label: t("common.greater_than"), value: 1 },
    { label: t("common.less_than"), value: 2 },
    { label: t("common.equal_to"), value: 3 },
  ];

  return (
    <Row className="g-2">
      <Col xs={6} className="pe-0">
        <CustomFilterSearch
          name="amount_comparison"
          control={control}
          options={comparisonOptions}
          label={t("common.comparison")}
          hasButtonSearch={hasButtonSearch}
        />
      </Col>
      <Col xs={6}>
        <CustomInput
          name="amount_value"
          control={control}
          type="number"
          label={t("common.amount")}
        />
      </Col>
    </Row>
  );
};

const Contract = () => {
  const { pathname } = useLocation();
  const { t, i18n } = useTranslation();
  const [selectId, setSelectId] = useState(null);
  const navigate = useNavigate();
  const [pagination, setPagination] = useState(1);
  const [selectedTemplateId, selectedTemplateType] = useState(1);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [startDateError, setStartDateError] = useState(null);
  const [endDateError, setEndDateError] = useState(null);
  const [openTemplate, setOpenTemplate] = useState(false);
  const [selectedCustomItemsForFilter, setSelectedCustomItemsForFilter] =
    useState(null);

  const { control, watch, reset, handleSubmit, setValue } = useForm({
    defaultValues: {
      client_ids: null,
      contract_type_id: null,
      status: null,
      contract_number: "",
      start_date_from: formatDate(today),
      start_date_to: formatDate(today),
      end_date_from: formatDate(today),
      end_date_to: formatDate(today),
      amount_comparison: null,
      amount_value: null,
    },
  });

  const [searchParams, setSearchParams] = useState({});
  const { data, isLoading, refetch } = ContracyQueries.useGetAllBills({
    limit: 50,
    page: pagination,
    searchParams,
  });

  // Get data for filter dropdowns
  const { data: clientsData } = clientsQueries.useGetAll({ status: 1 });
  const { data: contractTypesData } = ContractQueries.useGetAllContractTypes({
    status: 1,
  });

  // Convert data to options format
  const clientOptions = useSetSelectOptions({
    data: clientsData?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });

  const contractTypeOptions = useSetSelectOptions({
    data: contractTypesData?.result,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item.title.en : item.title.ar,
      value: item.id,
    }),
    otherDepend: i18n.language,
  });

  const statusOptions = [
    { label: t("types.reason.new"), value: 4 },
    { label: t("common.in_progress"), value: 1 },
    { label: t("common.decline"), value: 2 },
  ];

  // Validate dates whenever they change
  useEffect(() => {
    const startDateFrom = watch("start_date_from");
    const startDateTo = watch("start_date_to");
    const endDateFrom = watch("end_date_from");
    const endDateTo = watch("end_date_to");

    if (startDateFrom && startDateTo) {
      const fromDate = new Date(startDateFrom);
      const toDate = new Date(startDateTo);

      if (fromDate > toDate) {
        setStartDateError(t("common.date_error"));
        toastr.error(t("common.date_error"));
      } else {
        setStartDateError(null);
      }
    }

    if (endDateFrom && endDateTo) {
      const fromDate = new Date(endDateFrom);
      const toDate = new Date(endDateTo);

      if (fromDate > toDate) {
        setEndDateError(t("common.date_error"));
        toastr.error(t("common.date_error"));
      } else {
        setEndDateError(null);
      }
    }
  }, [
    watch("start_date_from"),
    watch("start_date_to"),
    watch("end_date_from"),
    watch("end_date_to"),
    t,
  ]);

  useEffect(() => {
    // Only apply default filter if no search params exist and no URL params
    if (Object.keys(searchParams).length === 0) {
      const todayFormatted = formatDate(today);
      const defaultParams = {
        "filter[contract_start_date][from]": todayFormatted,
        "filter[contract_start_date][to]": todayFormatted,
        "filter[contract_end_date][from]": todayFormatted,
        "filter[contract_end_date][to]": todayFormatted,
      };
      setSearchParams(defaultParams);
    }
  }, []);

  // Manual search function
  const handleSearch = handleSubmit((formData) => {
    if (startDateError || endDateError) {
      return;
    }

    const params = {};

    // Handle client multi-select
    if (formData.client_ids) {
      if (Array.isArray(formData.client_ids)) {
        if (formData.client_ids.length > 0) {
          formData.client_ids.forEach((client) => {
            if (!params["filter[client_ids][]"]) {
              params["filter[client_ids][]"] = [client.value];
            } else {
              params["filter[client_ids][]"].push(client.value);
            }
          });
        }
      } else if (formData.client_ids.value) {
        params["filter[client_ids][]"] = [formData.client_ids.value];
      }
    }

    // Handle contract type multi-select
    if (formData.contract_type_id) {
      if (Array.isArray(formData.contract_type_id)) {
        if (formData.contract_type_id.length > 0) {
          formData.contract_type_id.forEach((type) => {
            if (!params["filter[contract_type_id][]"]) {
              params["filter[contract_type_id][]"] = [type.value];
            } else {
              params["filter[contract_type_id][]"].push(type.value);
            }
          });
        }
      } else if (formData.contract_type_id.value) {
        params["filter[contract_type_id][]"] = [
          formData.contract_type_id.value,
        ];
      }
    }

    // Handle status
    if (formData.status) {
      params["filter[status]"] = formData.status.value;
    }

    // Contract number
    if (formData.contract_number) {
      params["filter[contract_number]"] = formData.contract_number;
    }

    // Start date range
    if (formData.start_date_from) {
      params["filter[contract_start_date][from]"] = formData.start_date_from;
    }
    if (formData.start_date_to) {
      params["filter[contract_start_date][to]"] = formData.start_date_to;
    }

    // End date range
    if (formData.end_date_from) {
      params["filter[contract_end_date][from]"] = formData.end_date_from;
    }
    if (formData.end_date_to) {
      params["filter[contract_end_date][to]"] = formData.end_date_to;
    }

    // Amount comparison
    if (formData.amount_comparison && formData.amount_value) {
      params["filter[subscription_amount][operator]"] =
        formData.amount_comparison.value;
      params["filter[subscription_amount][value]"] = formData.amount_value;
      setSelectedCustomItemsForFilter({
        operation: formData.amount_comparison.label,
        value: formData.amount_value,
      });
    }

    // Update search params
    setSearchParams(params);
    // Reset to first page
    setPagination(1);
    // Trigger a refetch with new parameters
    refetch();
  });

  const handleReset = () => {
    reset({
      client_ids: null,
      contract_type_id: null,
      status: null,
      contract_number: "",
      start_date_from: "",
      start_date_to: "",
      end_date_from: "",
      end_date_to: "",
      amount_comparison: null,
      amount_value: "", // تفريغ حقل amount_value
    });

    setStartDateError(null);
    setEndDateError(null);
    setSelectedCustomItemsForFilter(null);
    setSearchParams({});
    setPagination(1);
    refetch();
  };

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await contractAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await contractAPis.decline({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("contracts.contracts"),
      link: pathname,
    },
  ];

  const [openMenu, setOpenMenu] = useState(null); // Track the open menu by row ID

  const toggleMenu = (id) => {
    setOpenMenu((prev) => (prev === id ? null : id)); // Open menu for current row, close if same row is clicked again
  };
  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.client"),
        accessor: "client_name",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_number"),
        accessor: "contract_number",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_type"),
        accessor: "contract_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_start_date"),
        accessor: "contract_start_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_end_date"),
        accessor: "contract_end_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.status"),
        accessor: "status",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.subscription_amount"),
        accessor: "subscription_amount",
        disableFilters: true,
        filterable: false,
      },
    ];

    const permissions = [
      "contract.update" ||
        "contract.destroy" ||
        "contract.declined" ||
        "contract.show",
    ];

    // Check if user has any of the required permissions
    const hasPer = permissions.some((permission) => hasPermission(permission));

    // Only add actions column if user has any permission
    if (hasPer) {
      baseColumns.push({
        Header: t("common.actions"),
        accessor: (cellProps) => {
          const isDefault = cellProps.is_default === 1;

          return (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger asChild>
                <button className="icon-button">
                  <HiDotsVertical size={18} className="mx-1" />
                </button>
              </DropdownMenu.Trigger>

              <DropdownMenu.Portal>
                <DropdownMenu.Content
                  side="bottom"
                  align="start"
                  sideOffset={4}
                  className="dropdown-content text-center"
                >
                  <Can permission={"contract.update"}>
                    {cellProps.status === t("types.reason.new") && (
                      <DropdownMenu.Item
                        className="dropdown-item text-primary"
                        onSelect={() => {
                          if (!isDefault) {
                            navigate(`/action-contract?id=${cellProps.id}`);
                          }
                        }}
                      >
                        <FaPenToSquare size={14} className="mx-2" />
                        {t("common.update")}
                      </DropdownMenu.Item>
                    )}
                  </Can>

                  <Can permission={"contract.declined"}>
                    {cellProps.status !== t("common.decline") &&
                      cellProps.status !== t("common.in_progress") && (
                        <DropdownMenu.Item
                          className="dropdown-item text-primary"
                          onSelect={() => {
                            setstausIsActive(true);
                            setOpenMenu(cellProps.id);
                            setOpenStatsusModal(true);
                            setSelectId(cellProps.id);
                          }}
                        >
                          <GrStatusGood size={14} className="mx-2" />
                          {t("common.active")}
                        </DropdownMenu.Item>
                      )}
                  </Can>

                  <Can permission={"contract.declined"}>
                    {cellProps.status !== t("common.decline") && (
                      <DropdownMenu.Item
                        className="dropdown-item text-warning"
                        onSelect={() => {
                          setstausIsActive(false);
                          setOpenMenu(cellProps.id);
                          setOpenStatsusModal(true);
                          setSelectId(cellProps.id);
                        }}
                      >
                        <MdCancel size={14} className="mx-2" />
                        {t("common.decline")}
                      </DropdownMenu.Item>
                    )}
                  </Can>

                  <Can permission={"contract.store"}>
                    <DropdownMenu.Item
                      className="dropdown-item text-dark"
                      onSelect={() => {
                        navigate(
                          `/action-contract/?id=${cellProps.id}?isDuplicate=true`
                        );
                      }}
                    >
                      <TiDelete size={14} className="mx-2" />
                      {t("common.duplicate")}
                    </DropdownMenu.Item>
                  </Can>

                  <Can permission={"contract.destroy"}>
                    {cellProps.status !== t("common.decline") &&
                      cellProps.status !== t("common.in_progress") &&
                      !isDefault && (
                        <DropdownMenu.Item
                          className="dropdown-item text-danger"
                          onSelect={() => {
                            handelOpenModal();
                            handelSelectId(cellProps.id);
                          }}
                        >
                          <MdDeleteSweep size={18} className="mx-2" />
                          {t("common.delete")}
                        </DropdownMenu.Item>
                      )}
                  </Can>

                  <Can permission={"contract.show"}>
                    <DropdownMenu.Item
                      className="dropdown-item text-success"
                      onSelect={() => {
                        navigate(
                          `/action-contract/?id=${cellProps.id}?Show=true`
                        );
                      }}
                    >
                      <FaInfoCircle size={14} className="mx-2" />
                      {t("common.show")}
                    </DropdownMenu.Item>
                  </Can>
                </DropdownMenu.Content>
              </DropdownMenu.Portal>
            </DropdownMenu.Root>
          );
        },
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [openMenu, t, i18n.language]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (pagination - 1) * 10 + index + 1, // 10 is your page size
              subscription_amount: item.subscription_amount || "----",
              contract_start_date:
                item?.contract_start_date?.split("T")[0] || "----",
              contract_number: item.contract_number || "----",
              contract_end_date: item.contract_end_date || "----",
              contract_type:
                i18n.language === "eng"
                  ? item.contract_type?.title?.en
                  : item.contract_type?.title?.ar,
              delegate: item.delegate || "----",
              status: CreatingStatus(t)[item.status],
              client_name: item?.client?.company_name
                ? (item?.client?.full_name ? item?.client?.full_name : "---") +
                  "/" +
                  (item?.client?.company_name
                    ? item?.client?.company_name
                    : "---")
                : "---", // Keep the original ID if needed
            }))
            .reverse()
        : [],
    [data?.result, i18n.language]
  );
  const [open, setOpen] = useState(false);

  const handelOPen = () => {
    setOpen(!open);
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await contractAPis.deleteForDelete({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const types = [
    { label: t("types.contract_types.sell_contract"), value: 1 },
    { label: t("types.contract_types.rent_contract"), value: 2 },
  ];

  const SearchData = [
    {
      id: 0,
      label: t("common.client"),
      type: "select",
      name: "client_ids",
      options: clientOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 1,
      label: t("contracts.contract_type"),
      type: "select",
      name: "contract_type_id",
      options: contractTypeOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 2,
      label: t("common.status"),
      type: "select",
      name: "status",
      options: statusOptions,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
  ];

  const inputsArray = [
    {
      id: 0,
      name: "contract_number",
      type: "text",
      label: t("contracts.contract_number"),
      cols: 2,
    },
    {
      id: 1,
      name: "start_date_from",
      type: "date",
      label: t("contracts.start_date_from"),
      cols: 2,
      error: startDateError ? { message: startDateError } : null,
    },
    {
      id: 2,
      name: "start_date_to",
      type: "date",
      label: t("contracts.start_date_to"),
      cols: 2,
      error: startDateError ? { message: startDateError } : null,
    },
    {
      id: 3,
      name: "end_date_from",
      type: "date",
      label: t("contracts.end_date_from"),
      cols: 2,
      error: endDateError ? { message: endDateError } : null,
    },
    {
      id: 4,
      name: "end_date_to",
      type: "date",
      label: t("contracts.end_date_to"),
      cols: 2,
      error: endDateError ? { message: endDateError } : null,
    },
  ];

  const { data: templates, isLoading: isLoadingTemplates } =
    contractTemplateQueries.useGetAll({
      language: i18n.language === "eng" ? 2 : 1,
      templateId: selectedTemplateId,
    });

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          breadcrumbItems={breadcrumbItems}
          canPermission={"contract.store"}
          handleOrderClicks={() => setOpen(true)}
          disabledAddTitle={false}
          addTitle={t("common.add") + " " + t("contracts.contracts")}
          isAddOptions={true}
          title={t("contracts.contracts")}
        />
        <Card style={{ height: "75vh", padding: 20 }}>
          <TableContainer
            customComponent={
              <div className="d-grid gap-2 w-100">
                <SearchCard
                  SearchData={SearchData}
                  control={control}
                  hadelReset={handleReset}
                  inputsArray={inputsArray}
                  watch={watch}
                  setValue={setValue}
                  handelSearch={handleSearch}
                  hasButtonSearch={true}
                  customComponents={
                    <Col xs={4}>
                      <div className="mb-2 ms-1">
                        <AmountComparisonField
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          hasButtonSearch={true}
                        />
                      </div>
                    </Col>
                  }
                  customItems={
                    selectedCustomItemsForFilter?.operation && (
                      <div
                        className="badge border d-flex g-2 align-items-center px-3 py-2"
                        style={{
                          background: "#0d6efd",
                          color: "#fff",
                        }}
                      >
                        <span className="me-3">
                          {selectedCustomItemsForFilter?.operation}
                        </span>
                        <span className="me-3">
                          {selectedCustomItemsForFilter?.value}
                        </span>
                        <FaTimes
                          role="button"
                          style={{
                            fontSize: 12,
                            color: "#fff",
                          }}
                          onClick={() => {
                            setSelectedCustomItemsForFilter(null);
                          }}
                        />
                      </div>
                    )
                  }
                />
              </div>
            }
            columns={columns || []}
            data={rowData || []}
            isLoading={isLoading}
            customHeight={"100%"}
            pageCount={data?.meta?.last_page || 1}
            currentPage={pagination}
            setPage={setPagination}
            total={data?.meta?.subscription_amount || 0}
            hasTotal
          />
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={DeleteFun}
          itemName={t("contracts.contracts")}
          isDeleting={isDeleting}
        />
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                type="button"
                className="btn-sm"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                className="btn-sm"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}

      <Modal isOpen={open} toggle={handelOPen} backdrop="static">
        <ModalHeader toggle={handelOPen}>
          {t("common.select") + " " + t("reports.Bills_type")}
        </ModalHeader>
        <ModalBody>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: 10,
            }}
          >
            {types.map((item) => (
              <div xs={4} className="w-100" key={item.value}>
                <Button
                  type="button"
                  color="primary"
                  className="rounded-1 mb-2 me-2 w-100 btn-sm p-2"
                  onClick={() => {
                    // navigate(`/action-contract?type=${item.value}`);
                    selectedTemplateType(item.value);
                    setOpen(false);
                    setOpenTemplate(true);
                  }}
                >
                  {item.label}
                </Button>
              </div>
            ))}
          </div>
        </ModalBody>
      </Modal>
      <Modal
        isOpen={openTemplate}
        toggle={() => {
          setOpenTemplate(false);
          selectedTemplateType(0);
        }}
        backdrop="static"
      >
        <ModalHeader
          toggle={() => {
            setOpenTemplate(false);
            selectedTemplateType(0);
          }}
        >
          {t("common.select") + " " + t("common.contract_template")}
        </ModalHeader>
        <ModalBody>
          <div
            style={{
              display: "grid",
              gridTemplateColumns:
                templates?.result?.length === 0 ? "1fr" : "1fr 1fr",
              gap: 10,
            }}
          >
            {templates?.result?.length === 0 ? (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  width: "100%",
                }}
              >
                {t("common.no-data")}
              </div>
            ) : isLoadingTemplates ? (
              <div
                style={{
                  height: 100,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <ClipLoader size={40} />
              </div>
            ) : (
              templates.result.map((item) => (
                <Button
                  type="button"
                  color="primary"
                  className="rounded-1 mb-2 me-2 w-100 btn-sm p-2"
                  onClick={() => {
                    navigate(
                      `/action-contract?type=${selectedTemplateId}?template_id=${item.id}`
                    );
                  }}
                >
                  {item.name}
                </Button>
              ))
            )}
          </div>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default Contract;
