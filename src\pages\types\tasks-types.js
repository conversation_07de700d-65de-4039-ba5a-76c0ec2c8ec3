import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Label,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Header,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import { tasksQueries } from "../../apis/types/tasks/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { TasksAPis } from "../../apis/types/tasks/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomTextArea from "../../components/Common/textArea";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const TasksTypes = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingTasksTypes,
    refetch,
  } = tasksQueries.useGetAll({ limit: 10, page: page });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [openSidebar, setOPenSideBar] = useState(false);
  const [isShow, setIsShow] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
    setOPenSideBar(false);
    setIsShow(false);
    refetch();
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);

  const handelAddBonds = () => {
    // navigate("/action-tasks-types");
    setOPenSideBar(true);
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      await TasksAPis.active({
        id: id,
      });
      refetch();
      toastr.success("Active done ");
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      toastr.error("There are error");
      // toastr.error(error?.response?.data?.message);
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      await TasksAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success("in Active done");
    } catch (error) {
      setIsDeleting(false);
      toastr.error("There are error");
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const breadcrumbItems = [
    {
      title: t("tasks.tasks_type"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  // const { i18n } = useTranslation();
  const { data, isLoading } = tasksQueries.useGet({
    id: Number(selectId),
  });

  // const schema = yup
  //   .object({
  //     titleArabic: yup.string().required("this field is required"),
  //     titleEnglish: yup.string().required("this field is required"),
  //   })
  //   .required();

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    watch,
    clearErrors,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      def_duo_days: 1,
      show_in_customer_dashboard: 0,
    },
    // resolver: yupResolver(schema),
  });

  const handelCancel = () => {
    handelCLoseModal();
    reset({
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      def_duo_days: 1,
      show_in_customer_dashboard: 0,
    });
  };

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Populate form with role data when loaded
      reset({
        titleArabic: data?.result?.title.ar || "",
        titleEnglish: data?.result?.title.en || "",
        descriptionArabic: data?.result?.description.ar || "",
        descriptionEnglish: data?.result?.description.en || "",
        def_duo_days: data?.result?.def_duo_days,
        show_in_customer_dashboard:
          data?.result?.show_in_customer_dashboard || 0,
      });
    }
  }, [selectId, isLoading, data, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      clearErrors();
      const response = await TasksAPis.update({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        def_duo_days: data.def_duo_days,
        show_in_customer_dashboard: data.show_in_customer_dashboard ? 1 : 0,
        id: Number(selectId),
      });
      refetch();

      toastr.success(response.message);
      // navigate("/tasks-types");
      handelCLoseModal();
      reset();
      handelCancel();
      refetch();
    } catch (error) {
      if (error?.response?.data?.errors?.title) {
        setError("titleArabic", {
          message: error.response.data.errors.title,
        });
        setError("titleEnglish", {
          message: error.response.data.errors.title,
        });
      } else {
        handleBackendErrors({ error, setError });
      }
      // handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    try {
      clearErrors();
      const response = await TasksAPis.add({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        def_duo_days: data.def_duo_days,
        show_in_customer_dashboard: data.show_in_customer_dashboard ? 1 : 0,
      });
      toastr.success(response.message);
      // navigate("/tasks-types");
      refetch();
      handelCLoseModal();
      handelCancel();
      reset(); // Reset form after successful submission
    } catch (error) {
      if (error?.response?.data?.errors?.title) {
        setError("titleArabic", {
          message: error.response.data.errors.title,
        });
        setError("titleEnglish", {
          message: error.response.data.errors.title,
        });
      } else {
        handleBackendErrors({ error, setError });
      }
      // handleBackendErrors({ error, setError });
    }
  };
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("tasks.deadLine_task"),
      accessor: "def_duo_days",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.count"),
      accessor: "tasks_count",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: "status",
    //   disableFilters: true,
    //   filterable: false,
    //   accessor: (cellProps) => {
    //     return (
    //       <div className="form-check form-switch">
    //         <Input
    //           type="checkbox"
    //           className="form-check-input"
    //           defaultChecked={cellProps.status === 1 ? true : false}
    //           onClick={() => handelToggleStatus({ cellProps })}
    //         />
    //         <></>
    //       </div>
    //     );
    //   },
    // },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"task_type.update"}>
              <Link
                to={""}
                className="text-primary"
                onClick={() => {
                  handelOpenSideBar();
                  setSelectId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"task_type.destroy"}>
              {cellProps.is_default === 0 && (
                <Link
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenModal();
                      // handelSelectId(cellProps.id);
                      setSelectId(cellProps.id);
                    }
                  }}
                  to="#"
                  className="text-danger"
                >
                  {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                  <MdDeleteSweep size={18} />
                </Link>
              )}
            </Can>
            <Can permission={"task_type.show"}>
              <Link
                onClick={() => {
                  handelOpenSideBar();
                  setSelectId(cellProps.id);
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .filter((item) => item.is_setting === 0)
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title:
                i18n.language === "eng"
                  ? truncateText({ text: item.title.en, maxLengthPercent: 0.1 })
                  : truncateText({
                      text: item.title.ar,
                      maxLengthPercent: 0.3,
                    }) || "----",
              description:
                truncateText({
                  text: item.description,
                  maxLengthPercent: 0.3,
                }) || "----",
              tasks_count: item.tasks_count || "----",
              def_duo_days: item.def_duo_days || 0,
              is_default: item.is_default || 0,
              is_settings: item.is_setting || 0,
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await TasksAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handelCancel();
      // toastr.error("There are error");
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error, setError });

      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("tasks.tasks_type")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("tasks.tasks_type")}
          handleOrderClicks={handelAddBonds}
          canPermission={"task_type.store"}
        />
        <Card style={{ height: "78vh" }}>
          <CardBody>
            {/* {isLoadingTasksTypes ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              iscustomPageSize={true}
              isBordered={true}
              isLoading={isLoadingTasksTypes}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              customHeight={"80%"}
            />
            {/* )} */}
          </CardBody>
        </Card>{" "}
        <Modal isOpen={openSidebar} toggle={handelCancel} backdrop="static">
          <ModalHeader toggle={handelCancel}>
            {isShow
              ? t("common.show") + " " + t("tasks.tasks_type")
              : selectId
              ? t("common.update") + " " + t("tasks.tasks_type")
              : t("common.add") + " " + t("tasks.tasks_type")}
          </ModalHeader>
          <ModalBody>
            <Row>
              <form
                onSubmit={
                  selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
                }
              >
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row>
                      <Col xs={6}>
                        <div className="mb-4">
                          <CustomInput
                            name="titleEnglish"
                            control={control}
                            label={t("common.title_in_english")}
                            type="text"
                            placeholder={t("common.title_in_english")}
                            disabled={isShow}
                            error={errors.titleEnglish}
                            rules={{ required: t("common.field_required") }}
                          />
                        </div>
                      </Col>

                      <Col xs={6}>
                        <div className="mb-4">
                          <CustomInput
                            name="titleArabic"
                            control={control}
                            label={t("common.title_in_arabic")}
                            type="text"
                            placeholder={t("common.title_in_arabic")}
                            disabled={isShow}
                            error={errors.titleArabic}
                            rules={{ required: t("common.field_required") }}
                          />
                        </div>
                      </Col>
                    </Row>
                    <div className="mb-4">
                      <CustomInput
                        name="def_duo_days"
                        control={control}
                        label={t("tasks.deadLine_task")}
                        type="number"
                        placeholder=""
                        disabled={isShow}
                        error={errors.def_duo_days}
                      />
                    </div>

                    <div className="mb-2">
                      <Label className="form-label">
                        {t("common.description_in_arabic")}
                      </Label>
                      <CustomTextArea
                        name="descriptionArabic"
                        control={control}
                        placeholder={t("common.description_in_arabic")}
                        isShow={isShow}
                        rows={4}
                        error={errors.descriptionArabic}
                      />
                    </div>
                    <div className="mb-4">
                      <Label className="form-label">
                        {t("common.description_in_english")}
                      </Label>
                      <CustomTextArea
                        name="descriptionEnglish"
                        control={control}
                        placeholder={t("common.description_in_english")}
                        isShow={isShow}
                        rows={4}
                        error={errors.descriptionEnglish}
                      />
                    </div>
                    <div className="mb-4">
                      <div className="form-check form-switch">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          {...register("show_in_customer_dashboard")}
                          disabled={isShow}
                        />
                        <Label className="form-check-label">
                          {t("tasks.show_in_customer_dashboard")}
                        </Label>
                      </div>
                    </div>
                  </>
                )}
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCancel}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={
                        isSubmitting ||
                        (!watch("titleArabic") && !watch("titleEnglish"))
                      }
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : selectId ? (
                        t("common.update")
                      ) : (
                        t("common.add")
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </Row>
          </ModalBody>
        </Modal>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete") + " " + t("tasks.tasks_type")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                type="button"
                color="primary"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
    </div>
  );
};
export default TasksTypes;
