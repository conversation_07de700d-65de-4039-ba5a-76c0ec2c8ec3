import { Link } from "react-router-dom";
import <PERSON><PERSON><PERSON>oader from "react-spinners/ClipLoader";
import TableContainer from "../../Common/TableContainer";
import { useMemo, useState } from "react";
import { citiesQueries } from "../../../apis/cities/query";
import { Button, Modal, <PERSON>, <PERSON>dal<PERSON>ooter, ModalHeader } from "reactstrap";
import toastr from "toastr";
import { handleBackendErrors } from "../../../helpers/api_helper";
import { locationsAPis } from "../../../apis/locations/api";
import { locationQueries } from "../../../apis/locations/query";
import { useTranslation } from "react-i18next";
import AddLocationModel from "./add-modal";
import { Can } from "../../permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const Locations = () => {
  const [selectedId, setSelectedId] = useState(0);
  const [selectedDeleteId, setSelectedDeleteId] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteMdal, setOpendeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const { data: cities } = citiesQueries.useGetAll({});

  const { data: locations, isLoading, refetch } = locationQueries.useGetAll({});

  const handelCLoseModal = () => {
    setOpendeleteModal(false);
    setSelectedId(0);
    setSelectedDeleteId(0);
    setOpenModal(false);
  };
  const { t } = useTranslation();
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.city"),
      accessor: "city",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-start">
            <Can permission={"setting.update"}>
              <Link
                className="text-primary"
                onClick={() => {
                  handelAdd();
                  setSelectedId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"setting.destroy"}>
              <Link
                onClick={() => {
                  // setSelectedId(cellProps.id);
                  setSelectedDeleteId(cellProps.id);
                  setOpendeleteModal(true);
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      locations?.result?.length > 0
        ? locations.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: index + 1,
              name: item?.name || "----",
              city: item?.city?.name || "----",
            }))
            .reverse()
        : [],
    [locations?.result, cities?.result]
  );

  const handelAdd = () => {
    setOpenModal(true);
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await locationsAPis.deleteFu({
        id: selectedDeleteId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
      setSelectedId(null);
      setSelectedDeleteId(null);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div>
      {isLoading ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <TableContainer
          hideSHowGFilter={false}
          columns={columns || []}
          data={rowData || []}
          isPagination={false}
          hidePagination
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("common.locations")}
          handleOrderClicks={handelAdd}
          iscustomPageSize={true}
          isBordered={true}
          customComponent={<div>{t("common.locations")}</div>}
          customPageSize={10}
          className="custom-header-css table align-middle table-nowrap"
          tableClassName="table-centered align-middle table-nowrap mb-0"
          theadClassName="text-muted table-light"
          canPermission="client.store"
        />
      )}
      <AddLocationModel
        handelCLoseModal={handelCLoseModal}
        openModal={openModal}
        refetch={refetch}
        selectedId={selectedId}
      />

      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("common.locations")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button type="button" color="light" onClick={handelCLoseModal}>
              {t("common.close")}
            </Button>
            <Button onClick={DeleteFun} type="button" color="danger">
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};

export default Locations;
