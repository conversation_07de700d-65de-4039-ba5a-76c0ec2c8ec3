import { Container } from "reactstrap";
import SettingsHead from "../../components/settings/settings-head/settings-head";
import { useState } from "react";
import SettingContainer from "../../components/settings/settings-container/settings-container";
import Cities from "../../components/settings/cities/cities";
import States from "../../components/settings/states/states";
import Locations from "../../components/settings/location/location";
import ContractTemplate from "../contract-template";
import Vat from "../../components/settings/vat/vat";
import { useTranslation } from "react-i18next";
import { hasPermission } from "../../helpers/api_helper";

const Settings = () => {
  const [selectedTab, setSelectedTab] = useState(1);
  const [selectedRegions, setSelectedRegions] = useState(1);
  const { t } = useTranslation();

  const handelSelectTab = (id) => {
    setSelectedTab(id);
  };

  const handelSelectRegion = (id) => {
    setSelectedRegions(id);
  };

  const regionsTabs = [
    {
      id: 1,
      title: t("common.states"),
      component: <States />,
      permission: "setting.index",
    },
    {
      id: 2,
      title: t("common.cities"),
      component: <Cities />,
      permission: "setting.index",
    },
    {
      id: 3,
      title: t("common.locations"),
      component: <Locations />,
      permission: "setting.index",
    },
    {
      id: 4,
      title: t("common.contract_template"),
      component: <ContractTemplate />,
      permission: "setting.index",
    },
  ];

  const Tabs = [
    {
      id: 1,
      title: t("common.regions"),
      component: (
        <SettingContainer
          title={t("common.regions")}
          component={
            <Container>
              <SettingsHead
                handelSelectTab={handelSelectRegion}
                selectedTab={selectedRegions}
                Tabs={regionsTabs}
              />
              {hasPermission(regionsTabs[selectedRegions - 1].permission) &&
                regionsTabs[selectedRegions - 1]?.component}
            </Container>
          }
        />
      ),
    },
    {
      id: 2,
      title: t("common.vat"),
      component: (
        <SettingContainer
          title={t("common.vat")}
          component={
            <Container>{hasPermission("setting.vat") && <Vat />}</Container>
          }
        />
      ),
    },
  ];

  return (
    <div className="page-content">
      <Container>
        <SettingsHead
          handelSelectTab={handelSelectTab}
          selectedTab={selectedTab}
          Tabs={Tabs}
        />
        {Tabs[selectedTab - 1]?.component}
      </Container>
    </div>
  );
};

export default Settings;
