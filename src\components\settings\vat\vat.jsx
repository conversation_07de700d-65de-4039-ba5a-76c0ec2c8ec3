import ClipLoader from "react-spinners/ClipLoader";
import { useEffect, useState } from "react";
import toastr from "toastr";
import { useTranslation } from "react-i18next";
import { vatQueries } from "../../../apis/vat/query";
import { VatAPis } from "../../../apis/vat/api";
import { useForm } from "react-hook-form";
import { Button, Col, Input, Label, Row } from "reactstrap";
import { handleBackendErrors } from "../../../helpers/api_helper";

const Vat = () => {
  const { t } = useTranslation();
  const [status, setStatus] = useState(false);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    register,
    setError,
    setValue,
  } = useForm({ defaultValues: { vat_rate: null, vat_status: false } });

  const {
    data: vat,
    isLoading,
    refetch,
  } = vatQueries.useGetAll({ enable: true });

  const handelToggleStatus = () => {
    setStatus((prev) => !prev);
  };

  useEffect(() => {
    if (vat?.data) {
      setValue("vat_rate", vat.data.vat_rate);
      setStatus(vat.data.vat_status);
    }
  }, [vat?.data]);

  const handelUpdate = async (data) => {
    try {
      const response = await VatAPis.update({
        payload: { vat_rate: data.vat_rate, vat_status: status },
      });
      refetch();
      toastr.success(response.message);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <form onSubmit={handleSubmit(handelUpdate)}>
          <Row>
            <Col xs={4}>
              <div className="mb-4">
                <Label className="form-label" htmlFor="file">
                  {t("common.vat_rate")}
                </Label>
                <input
                  type="number"
                  className={`form-control ${
                    errors.vat_rate ? "is-invalid" : ""
                  }`}
                  {...register("vat_rate")}
                />
                {errors.vat_rate && (
                  <div className="invalid-feedback">
                    {errors.vat_rate.message}
                  </div>
                )}
              </div>
            </Col>
            <Col xs={6}>
              <Label className="form-label" htmlFor="file">
                {t("common.vat_status")}
              </Label>
              <div className="form-check form-switch">
                <Input
                  type="checkbox"
                  className="form-check-input "
                  style={{ width: 50, height: 25, cursor: "pointer" }}
                  checked={status}
                  onClick={() => handelToggleStatus({ value: 2 })}
                />
                {errors.vat_status && (
                  <div className="invalid-feedback">
                    {errors.vat_status.message}
                  </div>
                )}
              </div>
            </Col>
            <Col xs={4}>
              <Button
                color="primary"
                className="waves-effect waves-light primary-button"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.save")
                )}
              </Button>
            </Col>
          </Row>
        </form>
      )}
    </div>
  );
};

export default Vat;
