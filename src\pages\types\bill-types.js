import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Label,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { BillTYpeTypesAPis } from "../../apis/types/bill-type/api";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation } from "react-router-dom";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

import "./roles.scss";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomTextArea from "../../components/Common/textArea";
const BillTypes = () => {
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = billTypesQueries.useGetAll({ limit: 10, page: page });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [isShow, setIsShow] = useState(false);

  const { t, i18n } = useTranslation();

  const { data: contractType, isLoading: isLoadingContractType } =
    billTypesQueries.useGet({
      id: selectId,
    });

  const [openSidebar, setOPenSideBar] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const schema = yup
    .object({
      titleArabic: yup.string().required(t("common.field_required")),
      titleEnglish: yup.string().required(t("common.field_required")),
    })
    .required();

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    watch,
    register,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      next_visit_date: 0,
    },
    resolver: yupResolver(schema),
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingContractType && contractType?.result) {
      // Populate form with role data when loaded
      reset({
        titleArabic: contractType?.result?.title.ar,
        titleEnglish: contractType?.result?.title.en,
        descriptionArabic: contractType?.result?.description.ar || "",
        descriptionEnglish: contractType?.result?.description.en || "",
      });
    }
  }, [selectId, isLoadingContractType, contractType?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    reset({ title: "", description: "" });
    setSelectId(null);
    setIsShow(false);
    refetch();
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const UpdateFun = async (data) => {
    try {
      const response = await BillTYpeTypesAPis.update({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        next_visit_date: data.next_visit_date,
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    {
      title: t("types.bill.bill_types"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.count"),
      accessor: "bill_count",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: "Next visit date",
    //   accessor: "next_visit_date",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"bill_type.update"}>
              <Link
                to="#"
                className="me-3 text-primary"
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenSideBar();
                    // handelSelectId(cellProps.id);
                    setSelectId(cellProps.id);
                  }
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"bill_type.show"}>
              <Link
                onClick={() => {
                  handelOpenSideBar();
                  handelSelectId(cellProps.id);
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const BILL_TYPES = {
    1: t("types.bill.sample_bill"),
    2: t("types.bill.return_bill"),
    3: t("types.bill.pickup_maintenance_bill"),
    4: t("types.bill.deliver_maintenance_bill"),
    5: t("types.bill.change_oil"),
    6: t("types.bill.sales_bill"),
    7: t("types.bill.contract_bill"),
    8: t("types.bill.follow_up"),
    9: t("types.bill.return_sample_bill"),
  };

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title:
                i18n.language === "eng"
                  ? truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    })
                  : truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    }),
              description:
                truncateText({
                  text: item.description,
                  maxLengthPercent: 0.3,
                }) || "----",
              bill_count: item.bills_count || "----",
              type: BILL_TYPES[item.type],
              next_visit_date: item.next_visit_dates || "----",
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("types.bill.bill_types")}
          breadcrumbItems={breadcrumbItems}
        />
        <div>
          <Modal
            isOpen={openSidebar}
            toggle={handelCloseSideBar}
            backdrop="static"
          >
            <ModalHeader toggle={handelCloseSideBar}>
              {isShow
                ? t("common.show") + " " + t("bills.bill_type")
                : selectId
                ? t("common.update") + " " + t("bills.bill_type")
                : t("common.add") + " " + t("bills.bill_type")}
            </ModalHeader>
            <ModalBody>
              <form onSubmit={handleSubmit(UpdateFun)}>
                {isLoadingContractType ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <Row>
                    <Col xs={6}>
                      <div className="mb-2">
                        <CustomInput
                          name="titleEnglish"
                          control={control}
                          label={t("common.title_in_english")}
                          type="text"
                          placeholder={t("common.title_in_english")}
                          disabled={isShow}
                          error={errors.titleEnglish}
                          rules={{ required: t("common.field_required") }}
                        />
                      </div>
                    </Col>
                    <Col xs={6}>
                      <div className="mb-2">
                        <CustomInput
                          name="titleArabic"
                          control={control}
                          label={t("common.title_in_arabic")}
                          type="text"
                          placeholder={t("common.title_in_arabic")}
                          disabled={isShow}
                          error={errors.titleArabic}
                        />
                      </div>
                    </Col>
                    <Col xs={12}>
                      <div className="mb-2">
                        <Label className="form-label">
                          {t("common.description_in_arabic")}
                        </Label>
                        <CustomTextArea
                          name="descriptionArabic"
                          control={control}
                          placeholder={t("common.description_in_arabic")}
                          isShow={isShow}
                          rows={4}
                        />
                      </div>
                    </Col>
                    <Col xs={12}>
                      <div className="">
                        <Label className="form-label">
                          {t("common.description_in_english")}
                        </Label>
                        <CustomTextArea
                          name="descriptionEnglish"
                          control={control}
                          placeholder={t("common.description_in_english")}
                          isShow={isShow}
                          rows={4}
                        />
                      </div>
                    </Col>
                  </Row>
                )}
                <ModalFooter>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCloseSideBar}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={
                        isSubmitting ||
                        (!watch("titleArabic") && !watch("titleEnglish"))
                      }
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("common.update")
                      )}
                    </Button>
                  )}
                </ModalFooter>
              </form>
            </ModalBody>
          </Modal>
        </div>
        <Card style={{ height: "95vh" }}>
          <CardBody>
            {/* {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              handleOrderClicks={handelOpenSideBar}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              isLoading={isLoadingUsers}
            />
            {/* )} */}
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default BillTypes;
