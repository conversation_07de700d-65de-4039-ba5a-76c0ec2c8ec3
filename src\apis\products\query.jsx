import { useQuery } from "@tanstack/react-query";
import { productAPis } from "./api";

const useGetAll = ({
  limit,
  page,
  enabled = true,
  status,
  ProductTypeId,
  searchParams,
  billType,
  contractType
}) => {
  const queryResult = useQuery({
    queryKey: [
      "all-products",
      limit,
      page,
      status,
      ProductTypeId,
      searchParams,
      billType,
      contractType,
    ],
    queryFn: () =>
      productAPis.getAll({
        limit,
        page,
        status,
        ProductTypeId,
        searchParams,
        billType,
        contractType,
      }),
    enabled: enabled,
  });
  return queryResult;
};

const useGetAllProductUnite = ({ limit, page, status }) => {
  const queryResult = useQuery({
    queryKey: ["all-products-unite", limit, page, status],
    queryFn: () => productAPis.getProductUnites({ limit, page, status }),
  });
  return queryResult;
};

const useGetAllProductTypes = ({ limit, page, status }) => {
  const queryResult = useQuery({
    queryKey: ["all-products-types", limit, page, status],
    queryFn: () => productAPis.getProductTypes({ limit, page, status }),
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["produtc-one", id],
    queryFn: () =>
      id > 0 ? productAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const productQueries = {
  useGetAll,
  useGet,
  useGetAllProductTypes,
  useGetAllProductUnite,
};
