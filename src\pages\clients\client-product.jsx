import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import toastr from "toastr";
import { useTranslation } from "react-i18next";
import ActionProduct from "./action-client-product";
import { clientsQueries } from "../../apis/clients/query";
import { Can } from "../../components/permissions-way/can";
import ClipLoader from "react-spinners/ClipLoader";
import { handleBackendErrors } from "../../helpers/api_helper";
import { clientsAPis } from "../../apis/clients/api";
import { useLocation, useNavigate } from "react-router-dom";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const Tasks = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  // const warranty_status = queryParams.get("id")?.split("?")[1]?.split("=")[1];
  const { data: clients } = clientsQueries.useGetAll({});
  const [pagination, setPagination] = useState(1);
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(0);

  const { data, isLoading, refetch, isRefetching } =
    clientsQueries.useGetClientProduct({
      id: selectId,
      enabled: true,
    });

  const handelCloseAddModal = () => {
    setOpen(false);
    setOpenDeleteModal(false);
    setSelectedProduct(0);
    setIsShow(false);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("tasks.product_code"),
      accessor: "product_code",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("tasks.card_number"),
      accessor: "card_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.start_date"),
      accessor: "start_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.end_date"),
      accessor: "end_date",
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-center">
            <Can permission={"client.update"}>
              <div
                className="text-primary"
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setSelectedProduct(cellProps.id);
                  setOpen(true);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </div>
            </Can>
            <Can permission={"client.destroy"}>
              <div
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setOpenDeleteModal(true);
                  setSelectedProduct(cellProps.idForDelete);
                }}
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </div>
            </Can>
            <div
              className="text-success"
              style={{ cursor: "pointer" }}
              onClick={() => {
                setIsShow(true);
                setOpen(true);
                setSelectedProduct(cellProps.id);
              }}
            >
              {/* <i className=" ri-information-fill font-size-16"></i> */}
              <FaInfoCircle size={14} />
            </div>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.data?.length > 0
        ? data.data
            .map((item, index) => ({
              id: item?.warranty_card?.id,
              idForDelete: item?.id,
              id_toShow: index + 1, // 10 is your page size
              end_date: item.warranty_card.end_date || "---",
              start_date: item.warranty_card.start_date || "---",
              name: item.product?.name || "---",
              card_number: item.warranty_card?.card_number || "---",
              product_code: item.product_code || "---",
            }))
            .reverse()
        : [],
    [data?.data]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await clientsAPis.deleteClientProduct({
        id: selectedProduct,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCloseAddModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const sendWarrantyReadyFun = async () => {
    try {
      setIsDeleting(true);
      const response = await clientsAPis.sendWarrantyReady({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCloseAddModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const { pathname } = useLocation();

  const breadcrumbItems = [
    {
      title: t("clients.client_product"),
      link: pathname,
    },
  ];

  const handelAddBonds = () => {
    setOpen(true);
  };

  const selectedClient = clients?.result?.find((item) => item.id === selectId);

  const navigate = useNavigate();

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("clients.client_product")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("clients.client_product")}
          handleOrderClicks={handelAddBonds}
          canPermission="client.store"
        />

        <Card style={{ height: "78vh" }}>
          <CardBody>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <span
                  style={{
                    padding: 10,
                    border: "1px solid #ccc",
                    borderRadius: 10,
                  }}
                >
                  {t("common.full_name")}:{selectedClient?.full_name}
                </span>
                <span
                  style={{
                    padding: 10,
                    border: "1px solid #ccc",
                    borderRadius: 10,
                  }}
                >
                  {t("clients.company_name")}:{selectedClient?.company_name}
                </span>
                <span
                  style={{
                    padding: 10,
                    border: "1px solid #ccc",
                    borderRadius: 10,
                  }}
                >
                  {t("common.email")}:{selectedClient?.email}
                </span>
                <span
                  style={{
                    padding: 10,
                    borderRadius: 10,
                    background:
                      Number(data?.warranty_status) === 2
                        ? "#ebd64a"
                        : Number(data?.warranty_status) === 3
                        ? "#51dd13"
                        : "#dd1313",
                    color: "#fff",
                  }}
                >
                  {Number(data?.warranty_status) === 2
                    ? t("clients.warrantyed")
                    : Number(data?.warranty_status) === 3
                    ? t("clients.sende_warrantyed")
                    : t("clients.not_warrantyed")}
                </span>
              </div>
              {Number(data?.warranty_status) === 2 && (
                <Button
                  color="primary"
                  className="btn-sm"
                  onClick={() => sendWarrantyReadyFun()}
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.send_warranty")
                  )}
                </Button>
              )}
            </div>

            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              setPage={setPagination}
              pageCount={data?.meta?.last_page}
              currentPage={pagination}
              isLoading={isLoading || isRefetching}
              hidePagination
              customHeight="65%"
            />

            <Button
              type="button"
              color="light"
              className="btn-sm"
              style={{ height: "32px", width: "54px" }}
              onClick={() => {
                navigate("/clients");
              }}
            >
              {t("common.close")}
            </Button>
          </CardBody>
        </Card>

        <Modal
          isOpen={open}
          backdrop="static"
          style={{
            maxWidth: "50vw",
            maxHeight: "80vh",
            overflowY: "auto",
            height: "90vh",
            borderRadius: 10,
          }}
        >
          <ModalHeader
            toggle={() => {
              handelCloseAddModal();
            }}
          >
            {isShow
              ? t("common.show") + " " + t("clients.client_product")
              : selectId
              ? t("common.update") + " " + t("clients.client_product")
              : t("common.add") + " " + t("clients.client_product")}
          </ModalHeader>
          <ModalBody>
            <ActionProduct
              handelCloseAddModal={handelCloseAddModal}
              setOpenAddModal={setOpen}
              refetch={refetch}
              clientId={selectId}
              isShow={isShow}
              selectId={selectedProduct}
            />
          </ModalBody>
        </Modal>
      </Container>

      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCloseAddModal}
        // backdrop="static"
      >
        <ModalHeader toggle={handelCloseAddModal}>
          {t("common.delete")} {t("clients.clients")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCloseAddModal}
            >
              {t("common.close")}
            </Button>
            <Button
              className="btn-sm"
              onClick={DeleteFun}
              type="button"
              color="danger"
            >
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default Tasks;
