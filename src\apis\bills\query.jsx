import { useQuery } from "@tanstack/react-query";
import { billAPis } from "./api";

const useGetAllBills = ({ limit, page, searchParams }) => {
  const queryResult = useQuery({
    queryKey: ["all-Bills", limit, page, searchParams],
    queryFn: () => billAPis.getAll({ limit, page, searchParams }),
  });
  return queryResult;
};

const useGetAllListBillsForDelete = ({ limit, page }) => {
  const queryResult = useQuery({
    queryKey: ["all-Bills-for-delete", limit, page],
    queryFn: () => billAPis.getAllListForDelete({ limit, page }),
  });
  return queryResult;
};

const useGetGenerateBill = ({ id, enabled }) => {
  const queryResult = useQuery({
    queryKey: ["get_generate_bill", id],
    queryFn: () => billAPis.getGenerateBills({ id }),
    enabled,
  });
  return queryResult;
};

const useGetAllPickUpData = ({ client_id, enabled }) => {
  const queryResult = useQuery({
    queryKey: ["all-pick-up", client_id],
    queryFn: () => billAPis.getAllPickUp({ client_id }),
    enabled: enabled,
  });
  return queryResult;
};

const useGetSampleBill = ({ client_id, enabled }) => {
  const queryResult = useQuery({
    queryKey: ["all-sample-bill", client_id],
    queryFn: () => billAPis.getSampleBill({ client_id }),
    enabled: enabled,
  });
  return queryResult;
};

const useGetAllFollowUpList = ({ limit, page }) => {
  const queryResult = useQuery({
    queryKey: ["all-Bills-follow-up", limit, page],
    queryFn: () => billAPis.getAllFollwUp({ limit, page }),
  });
  return queryResult;
};

const useGetBill = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["get-bill", id],
    queryFn: () =>
      id > 0 ? billAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

const useGetOneFollowUp = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["get-one-follow-up", id],
    queryFn: () =>
      id > 0 ? billAPis.getOneFollwUp({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const BillQueries = {
  useGetBill,
  useGetAllListBillsForDelete,
  useGetOneFollowUp,
  useGetAllBills,
  useGetAllFollowUpList,
  useGetAllPickUpData,
  useGetGenerateBill,
  useGetSampleBill,
};
