import ApiInstance from "../../constant/api-instance";
import { TERMS_ROUTES } from "./route";

const getAll = async ({ limit, page, sectionId }) => {
  const { data } = await ApiInstance.get(TERMS_ROUTES.TEMPLATE + sectionId, {
    params: { limit, page },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${TERMS_ROUTES.SECTION}${id}`);
  return data;
};

const add = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(
    `${TERMS_ROUTES.TEMPLATE}${id}`,
    payload
  );
  return data;
};
const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${TERMS_ROUTES.SECTION}${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  await ApiInstance.delete(`${TERMS_ROUTES.SECTION}${id}`);
};

export const TermsAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
};
