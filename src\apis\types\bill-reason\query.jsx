import { useQuery } from "@tanstack/react-query";
import { billReasonsApi } from "./api";

const useGetAll = ({ status, enable = true, limit, page }) => {
  const queryResult = useQuery({
    queryKey: ["all-bill-reasons", status, limit, page],
    queryFn: () => billReasonsApi.getAll({ status, limit, page }),
    enabled: enable,
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["bill-reason", id],
    queryFn: () => billReasonsApi.getOne({ id }),
    enabled: !!id,
  });
  console.log("queryResult", queryResult.data);
  return queryResult;
};

export const useBillReasonsQueries = { useGetAll, useGet };
