import ApiInstance from "../../constant/api-instance";
import { CLIENTS_ROUTES } from "./route";

const getAll = async ({ limit, page, status, searchParams }) => {
  const { data } = await ApiInstance.get(CLIENTS_ROUTES.CLIENTS, {
    params: {
      limit,
      page,
      "filter[status]": status,
      ...searchParams,
    },
  });
  return data;
};

const getAllDelegateDependOnClient = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CLIENTS_ROUTES.CLIENTS}/${id}/delegate`,
    {
      params: {
        id,
      },
    }
  );
  return data;
};

const getTask = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CLIENTS_ROUTES.CLIENTS}/${id}`);
  return data;
};
const active = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CLIENTS_ROUTES.ACTIVE}/${id}`);
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CLIENTS_ROUTES.IN_ACTIVE}/${id}`);
  return data;
};
const add = async (formData) => {
  const { data } = await ApiInstance.post(
    `${CLIENTS_ROUTES.CLIENTS}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

const update = async ({ formData, id }) => {
  const { data } = await ApiInstance.post(
    `${CLIENTS_ROUTES.CLIENTS}/${id}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

const addClient = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${CLIENTS_ROUTES.WARRANTY}`,
    payload
  );
  return data;
};

const updateClient = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${CLIENTS_ROUTES.WARRANTY}/${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${CLIENTS_ROUTES.CLIENTS}/${id}`);
  return data;
};

const getClientProduct = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CLIENTS_ROUTES.CLIENT_PRODUCT}/${id}/warranty-products`
  );
  return data;
};

const sendWarrantyReady = async ({ id }) => {
  const { data } = await ApiInstance.post(
    `${CLIENTS_ROUTES.CLIENT_PRODUCT}/${id}/warranty-ready`
  );
  return data;
};


const getClient = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CLIENTS_ROUTES.WARRANTY}/${id}`);
  return data;
};

const deleteClientProduct = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${CLIENTS_ROUTES.DELETE_CLIENT_PRODUCT}/${id}`
  );
  return data;
};

export const clientsAPis = {
  deleteFu,
  update,
  add,
  getTask,
  active,
  inActive,
  getAll,
  getAllDelegateDependOnClient,
  addClient,
  updateClient,
  getClientProduct,
  getClient,
  deleteClientProduct,
  sendWarrantyReady,
};
