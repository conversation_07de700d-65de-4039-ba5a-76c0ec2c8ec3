import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Label,
  Row,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, useNavigate } from "react-router-dom";
import Select from "react-select";
import { BondsQueries } from "../../apis/bound/query";
import { BondsAPis } from "../../apis/bound/api";
import { carLogsQueries } from "../../apis/car-logs/query";
import {
  formatDate,
  getTodayDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";

const ClientActions = () => {
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);

  const selectId = Number(queryParams.get("id")?.split("?")[0]);

  const isShow = queryParams.get("id")?.split("?")[1];

  const { pathname } = useLocation();

  const [optionBondTypesGroup, setOptionBondTypesGroup] = useState([]); // Assuming you have a way to populate this

  const { t, i18n } = useTranslation();

  const { data, isLoading: isLoadingBond } = BondsQueries.useGetOne({
    id: Number(selectId),
  });

  const { data: carsLogsList, isLoading: isCarLogLoading } =
    carLogsQueries.useGetAll({
      un_paid: isShow ? 3 : Number(selectId) ? 2 : 1,
      bond_id: selectId ? Number(selectId) : null,
    });

  const [checkedItems, setCheckedItems] = useState([]); //plain object as state

  const handleChange = (event) => {
    // mutate the current Map
    setCheckedItems((prev) => {
      const isFounded = prev.find(
        (item) => item === Number(event.target.value)
      );
      if (isFounded) {
        const newItems = prev.filter(
          (item) => item !== Number(event.target.value)
        );
        return newItems;
      } else {
        return [...prev, Number(event?.target?.value)];
      }
    });
  };

  useEffect(() => {
    if (checkedItems.length > 0) {
      let total = 0;
      carsLogsList?.result?.map((item) => {
        checkedItems.map((value) => {
          if (value === item.id) {
            const foundedTotal = item?.total_price;
            total += Number(foundedTotal);
            setValue("total", total);
          }
        });
      });
    } else {
      if (checkedItems.length === 0) {
        setValue("total", 0);
      }
    }
  }, [checkedItems]);

  const breadcrumbItems = [
    { title: t("bonds.voucher"), link: "/bonds-list" },
    {
      title: isShow
        ? t("common.show")
        : selectId
        ? t("common.update")
        : t("common.create"),
      link: pathname,
    },
  ];

  const { data: bondTypes } = BondsQueries.useGetAllBoundsType({
    bond_type: 2,
  });

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate(-1);
    reset();
  };

  const initialSTate = {
    bond_date: formatDate(today),
    total: null,
    bond_type: 0,
    payment_type: null,
    operation_number: "",
  };

  const schema = yup.object({
    total: yup
      .number()
      .min(1, t("bonds.min_totla"))
      .max(999999, t("bonds.max_total"))
      .integer(t("common.integer"))
      .required(t("common.field_required"))
      .typeError(t("common.valid_number")),
    operation_number: yup.string().when("payment_type", {
      is: (val) => Number(val) === 2 || Number(val) === 3, // Convert to number comparison
      then: () => yup.string().required(t("common.field_required")),
      otherwise: () => yup.string().nullable(),
    }),
    payment_type: yup.string().required(t("common.field_required")),
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    setValue,
    watch,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (bondTypes?.result?.length > 0) {
      setValue("bond_type", {
        label:
          i18n.language === "eng"
            ? bondTypes.result[0].title.en
            : bondTypes.result[0].title.ar,
        value: bondTypes.result[0].id,
      });
    }
  }, [bondTypes?.result]);

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingBond && data?.result) {
      data.carLogs?.forEach((item) => {
        setCheckedItems((prev) => {
          if (!prev?.includes(item.id)) {
            return [...(prev || []), item.id]; // Initialize as an empty array if prev is undefined or null
          }
          return prev; // Return the previous state if item.id is already in the array
        });
      });
      // Populate form with role data when loaded
      reset({
        total: data?.result?.total,
        bond_date: data?.result?.bond_date.split(" ")[0],
        operation_number: data?.result?.operation_number,
        payment_type: data?.result?.payment_type,
        bond_type: {
          value: data?.result?.bond_type?.id,
          label:
            i18n.language === "eng"
              ? data?.result?.bond_type?.title.en
              : data?.result?.bond_type?.title.ar,
        },
      });
    }
  }, [selectId, isLoadingBond, data]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const Add = async (data) => {
    if (checkedItems.length === 0) {
      toastr.error(t("bonds.select_car_to_payment"));
      return;
    }
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });
      const response = await BondsAPis.add({
        payload: {
          bond_type_id: data?.bond_type?.value,
          bond_date: data.bond_date + " " + formattedTime,
          total: data.total,
          car_log_id: checkedItems,
          payment_type: Number(data.payment_type),
          operation_number:
            Number(data.payment_type) === 1 ? null : data.operation_number,
        },
      });
      toastr.success(response.message);
      reset({ bond_date: "", client_id: 0, total: null });
      handelCancel();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const update = async (data) => {
    if (checkedItems.length === 0) {
      toastr.error(t("bonds.select_car_to_payment"));
      return;
    }
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });
      const response = await BondsAPis.update({
        payload: {
          bond_type_id: data?.bond_type?.value,
          bond_date: data.bond_date + " " + formattedTime,
          total: data.total,
          car_log_id: checkedItems,
          payment_type: Number(data.payment_type),
          operation_number:
            Number(data.payment_type) === 1 ? null : data.operation_number,
        },
        id: selectId,
      });
      reset({ bond_date: "", client_id: 0, total: null });
      handelCancel();
      toastr.success(response.message);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const currenttoday = getTodayDate();

  const paymentTypeOption = [
    { id: 1, title: t("bonds.cash") },
    { id: 2, title: t("bonds.cheque") },
    { id: 3, title: t("bonds.visa") },
  ];

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={
            isShow
              ? t("common.show") + " " + t("bonds.voucher")
              : selectId
              ? t("common.update") + " " + t("bonds.voucher")
              : t("common.add") + " " + t("bonds.voucher")
          }
          breadcrumbItems={breadcrumbItems}
        />
        <Card>
          <CardBody>
            <Row>
              <Col xs={6}>
                <Row>
                  <form
                    onSubmit={
                      selectId ? handleSubmit(update) : handleSubmit(Add)
                    }
                  >
                    {isLoadingBond ? (
                      <div className="container-loading">
                        <ClipLoader color="#ddd" size={50} />
                      </div>
                    ) : (
                      <Row>
                        <Col xs={4} className="mb-4">
                          <div>
                            <Label className="form-label" htmlFor="total">
                              {t("common.select")} {t("bonds.payment_type")}
                            </Label>
                          </div>
                          <select
                            placeholder="---"
                            disabled={isShow || selectId}
                            className="form-select"
                            {...register("payment_type")} // register the select field
                          >
                            {paymentTypeOption.map((item) => (
                              <option value={item.id}>{item.title}</option>
                            ))}
                          </select>
                          {errors?.payment_type && (
                            <div>
                              <p style={{ color: "#ff3d60" }}>
                                {errors?.payment_type?.message}
                              </p>
                            </div>
                          )}
                        </Col>
                        <Col xs={4} className="mb-4">
                          <div>
                            <Label className="form-label" htmlFor="total">
                              {t("bonds.select_bond_type")}
                            </Label>
                          </div>
                          <Controller
                            name="bond_type"
                            control={control}
                            // defaultValue={[]}
                            render={({ field }) => (
                              <Select
                                isDisabled={true}
                                {...field}
                                isClearable
                                options={optionBondTypesGroup}
                                isMulti={false}
                                styles={{
                                  menuList: (props) => ({
                                    ...props,
                                    paddingBottom: 10,
                                    height: "100px",
                                  }),
                                  menu: (props) => ({
                                    ...props,
                                    height: "100px",
                                  }),
                                }}
                                classNamePrefix="select2-selection"
                              />
                            )}
                          />
                        </Col>
                        <Col>
                          <div className="mb-4">
                            <Label className="form-label" htmlFor="quant">
                              {t("bonds.voucher_date")}
                            </Label>
                            <input
                              name=""
                              {...register(`bond_date`, {
                                required: true,
                              })}
                              disabled={isShow}
                              placeholder="...."
                              type="date"
                              max={currenttoday}
                              className={`form-control ${
                                errors?.bond_date ? `is-invalid` : ``
                              }`}
                            />
                            {errors?.bond_date && (
                              <div className="invalid-feedback">
                                {errors?.bond_date?.message}
                              </div>
                            )}
                          </div>
                        </Col>
                        <Col>
                          <div className="mb-4">
                            <Label className="form-label" htmlFor="total">
                              {t("cars.total_price")}
                            </Label>
                            <input
                              disabled={isShow}
                              name=""
                              {...register(`total`)}
                              placeholder="...."
                              type="number"
                              className={`form-control ${
                                errors?.total ? `is-invalid` : ``
                              }`}
                            />
                            {errors?.total && (
                              <div className="invalid-feedback">
                                {errors?.total?.message}
                              </div>
                            )}
                          </div>
                        </Col>
                        {(Number(watch("payment_type")) === 2 ||
                          Number(watch("payment_type")) === 3) && (
                          <Col xs={6}>
                            <div className="mb-4">
                              <Label className="form-label" htmlFor="quant">
                                {t("bonds.operation_number")}
                              </Label>
                              <input
                                {...register(`operation_number`)}
                                placeholder="...."
                                type="string"
                                className={`form-control ${
                                  errors?.operation_number ? `is-invalid` : ``
                                }`}
                                disabled={isShow}
                              />
                              {errors?.operation_number && (
                                <div>
                                  <p style={{ color: "#ff3d60" }}>
                                    {errors?.operation_number?.message}
                                  </p>
                                </div>
                              )}
                            </div>
                          </Col>
                        )}
                      </Row>
                    )}
                    <div
                      style={{ display: "flex", alignItems: "center", gap: 8 }}
                    >
                      <Button
                        type="button"
                        color="light"
                        className="btn-sm"
                        onClick={handelCancel}
                      >
                        {t("common.close")}
                      </Button>
                      {/* <Can permission={selectId ? "client.update" : "client.store"}> */}
                      {!isShow && (
                        <Button
                          color="primary"
                          className="btn-sm waves-effect waves-light primary-button"
                          type="submit"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <ClipLoader color="white" size={15} />
                          ) : selectId ? (
                            t("common.update")
                          ) : (
                            t("common.add")
                          )}
                        </Button>
                      )}
                      {/* </Can> */}
                    </div>
                  </form>
                </Row>
              </Col>
              <Col xs={6} style={{ height: "75vh", overflowY: "auto" }}>
                <h4>{t("bonds.select_car_to_payment")}</h4>
                <Row className="p-2">
                  {isCarLogLoading && (
                    <div className="container-loading">
                      <ClipLoader color="#ddd" size={50} />
                    </div>
                  )}
                  {!isCarLogLoading &&
                    carsLogsList?.result?.map((item) => (
                      <Col xs={6} key={item.id}>
                        <div className="form-check mb-3">
                          <Label
                            className="form-check-label"
                            style={{
                              width: "100%",
                              cursor: "pointer",
                            }}
                            htmlFor={`checkbox_${item.id}`} // Link label to input
                          >
                            <Input
                              type="checkbox"
                              id={`checkbox_${item.id}`}
                              value={item.id}
                              name={item.name}
                              disabled={isShow}
                              onChange={handleChange}
                              checked={checkedItems.includes(item.id)} // Auto-select if item.id is in checkedItems
                            />

                            <div
                              style={{
                                border: "1px solid #ccc",
                                display: "grid",
                                paddingInline: 10,
                                paddingBlock: 6,
                                borderRadius: 10,
                                gap: 2,
                                width: "100%",
                                boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 4px",
                              }}
                            >
                              <span style={{ fontSize: 16, marginBottom: 4 }}>
                                {t("cars.car_log_type")}:{" "}
                                {i18n.language === "eng"
                                  ? item?.car_log_type?.title?.en
                                  : item?.car_log_type?.title?.ar}
                              </span>
                              <span>
                                {t("bonds.car_name")}: {item?.car?.name}
                              </span>
                              <span>
                                {t("cars.total_price")}: {item?.total_price}
                              </span>
                              <span>
                                {t("bonds.km_value")}: {item?.km}
                              </span>
                            </div>
                          </Label>
                        </div>
                      </Col>
                    ))}
                </Row>
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default ClientActions;
