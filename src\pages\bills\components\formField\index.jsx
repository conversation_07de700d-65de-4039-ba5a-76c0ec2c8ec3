import { Col, Input } from "reactstrap";
import { formatDate } from "../../../../helpers/api_helper";

// Form Fields Components
export const FormField = ({
  field,
  errors,
  isShow,
  control,
  placeholder,
  cols = 2,
  isDisabled,
  onTotalChange,
}) => (
  <Col xs={cols} key={field.id}>
    <Input
      control={control}
      error={errors[field.name]}
      min={
        field.type === "date"
          ? formatDate(
              new Date(new Date().setFullYear(new Date().getFullYear() - 2))
            )
          : undefined
      }
      isDisabled={isDisabled || field.disable || isShow}
      step={field.type === "number" ? "any" : undefined}
      placeholder={placeholder}
      type={field.type}
      name={field.name}
      onChange={field.name === "total" ? onTotalChange : undefined}
    />
  </Col>
);
