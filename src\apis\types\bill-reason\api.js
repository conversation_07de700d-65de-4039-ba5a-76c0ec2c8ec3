import ApiInstance from "../../../constant/api-instance";
import { BILL_REASONS_ROUTES } from "./route";

const getAll = async ({ status, limit, page }) => {
  const { data } = await ApiInstance.get(
    BILL_REASONS_ROUTES.BILL_REASONS_LIST,
    { params: { "filter[status]": status, limit, page } }
  );

  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${BILL_REASONS_ROUTES.BILL_REASONS_LIST}/${id}`
  );
  return data;
};
const create = async (dataToSend) => {
  const { data } = await ApiInstance.post(
    BILL_REASONS_ROUTES.BILL_REASONS_LIST,
    dataToSend
  );
  return data;
};
const update = async ({ id, title, type, status }) => {
  const { data } = await ApiInstance.put(
    `${BILL_REASONS_ROUTES.BILL_REASONS_LIST}/${id}`,
    {
      title,
      type,
      status,
    }
  );
  return data;
};
const deleteOne = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${BILL_REASONS_ROUTES.BILL_REASONS_LIST}/${id}`
  );
  return data;
};
const toggle = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${BILL_REASONS_ROUTES.TOGGLE_BILL_REASON}/${id}`
  );
  return data;
};
export const billReasonsApi = {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  toggle,
};
