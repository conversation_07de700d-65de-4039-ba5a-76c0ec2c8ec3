import ApiInstance from "../../constant/api-instance";
import { DELEGATE_ROUTES } from "./route";

const getAll = async ({ limit, page, status, searchParams }) => {
  const { data } = await ApiInstance.get(DELEGATE_ROUTES.DELEGATE, {
    params: {
      limit,
      page,
      "filter[status]": status,
      ...searchParams,
    },
  });
  return data;
};
const getAvailable = async () => {
  const { data } = await ApiInstance.get(
    DELEGATE_ROUTES.AVAILABLE_CAR_LIST,
    {}
  );
  return data;
};

const getAvailableClientsGroup = async () => {
  const { data } = await ApiInstance.get(
    DELEGATE_ROUTES.AVAILABLE_CLIENT_GROUP,
    {}
  );
  return data;
};

const getTask = async ({ id }) => {
  const { data } = await ApiInstance.get(`${DELEGATE_ROUTES.DELEGATE}/${id}`);
  return data;
};

const active = async ({ id }) => {
  const { data } = await ApiInstance.get(`${DELEGATE_ROUTES.ACTIVE}/${id}`);
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(`${DELEGATE_ROUTES.IN_ACTIVE}/${id}`);
  return data;
};
const add = async (formData) => {
  const { data } = await ApiInstance.post(
    `${DELEGATE_ROUTES.DELEGATE}`,
    formData
  );
  return data;
};
const assignCar = async ({ cardId, delegateId, km, system_status }) => {
  const { data } = await ApiInstance.post(`${DELEGATE_ROUTES.ASSIGN_CAR}`, {
    car_id: cardId,
    delegate_id: delegateId,
    km,
    system_status,
  });
  return data;
};

const assignCLientGroup = async ({ delegate_id, clinent_group_id }) => {
  const { data } = await ApiInstance.post(
    `${DELEGATE_ROUTES.ASSIGN_CLIENT_GROUP}`,
    {
      delegate_id,
      clinent_group_id,
    }
  );
  return data;
};

const update = async ({ formData, id }) => {
  const { data } = await ApiInstance.post(
    `${DELEGATE_ROUTES.DELEGATE}/${id}`,
    formData
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${DELEGATE_ROUTES.DELEGATE}/${id}`
  );
  return data;
};
export const delegateAPis = {
  deleteFu,
  update,
  add,
  getTask,
  active,
  inActive,
  getAll,
  assignCar,
  getAvailable,
  assignCLientGroup,
  getAvailableClientsGroup,
};
