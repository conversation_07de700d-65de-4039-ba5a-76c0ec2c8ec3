import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Modal,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useLocation, useNavigate } from "react-router-dom";
import {
  AdmminAprovaple,
  AdmminAprovapleTranslation,
  CreatingType,
} from "../../constant/constants";
import { useForm } from "react-hook-form";
import { OfferQueries } from "../../apis/offer-price/query";
import { offerAPis } from "../../apis/offer-price/api";
import { handleBackendErrors } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import DeleteModal from "../../components/Common/DeleteModal";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import { HiDotsVertical } from "react-icons/hi";
import { CiShoppingBasket } from "react-icons/ci";
import { IoDuplicate } from "react-icons/io5";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

const Offers = () => {
  const [pageSize, setPageSize] = useState(1);

  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const navigate = useNavigate();
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openApproveModel, setOpenApproveModel] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [openMenu, setOpenMenu] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { t } = useTranslation();
  const { data, isLoading, refetch } = OfferQueries.useGetAll({
    limit: 50,
    page: currentPage,
  });
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setOpenApproveModel(false);
    setValue("delegate_id", null);
    setValue("button_type", 0);
  };

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    navigate("/action-offer-price");
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("offer.quotation"),
      link: pathname,
    },
  ];

  function getKeyByValueCreatingType(value) {
    return Object.keys(CreatingType).find((key) => CreatingType[key] === value);
  }

  const toggleMenu = (id) => {
    setOpenMenu((prev) => (prev === id ? null : id)); // Open menu for current row, close if same row is clicked again
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.reason.admin_approve"),
      accessor: "admin_approval",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("offer.bidder_type"),
      accessor: "bidder_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.start_date"),
      accessor: "start_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.end_date"),
      accessor: "end_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <DropdownMenu.Root>
            <DropdownMenu.Trigger asChild>
              <button
                className="bg-transparent border-0"
                style={{ cursor: "pointer" }}
              >
                <HiDotsVertical size={18} />
              </button>
            </DropdownMenu.Trigger>

            <DropdownMenu.Portal>
              <DropdownMenu.Content
                sideOffset={5}
                align="start"
                className="dropdown-content"
              >
                <div className="text-center">
                  <Can permission={"offer.update"}>
                    <DropdownMenu.Item
                      className="text-primary dropdown-item"
                      onSelect={() => {
                        if (cellProps.is_default !== 1) {
                          navigate(`/action-offer-price?id=${cellProps.id}`);
                        }
                      }}
                    >
                      <FaPenToSquare size={14} className="text-primary" />
                      {t("common.update")}
                    </DropdownMenu.Item>
                  </Can>

                  {cellProps.admin_approvalEnum === "Waiting" && (
                    <DropdownMenu.Item
                      className="text-warning dropdown-item"
                      onSelect={() => {
                        setOpenApproveModel(true);
                        handelSelectId(cellProps.id);
                      }}
                    >
                      <CiShoppingBasket size={14} className="text-warning" />
                      {t("common.approve")}
                    </DropdownMenu.Item>
                  )}

                  <Can permission={"offer.store"}>
                    <DropdownMenu.Item
                      className="text-primary flex dropdown-item "
                      onSelect={() => {
                        if (cellProps.is_default !== 1) {
                          navigate(
                            `/action-offer-price?id=${cellProps.id}?idDuplicate`
                          );
                        }
                      }}
                    >
                      <IoDuplicate size={14} className="text-primary" />
                      {t("common.duplicate")}
                    </DropdownMenu.Item>
                  </Can>

                  <Can permission={"offer.destroy"}>
                    <DropdownMenu.Item
                      className="text-danger dropdown-item "
                      onSelect={() => {
                        handelOpenModal();
                        handelSelectId(cellProps.id);
                      }}
                    >
                      <MdDeleteSweep size={18} className="text-danger" />
                      {t("common.delete")}
                    </DropdownMenu.Item>
                  </Can>

                  <Can permission={"offer.show"}>
                    <DropdownMenu.Item
                      className="text-success dropdown-item "
                      onSelect={() => {
                        navigate(
                          `/action-offer-price/?id=${cellProps.id}?Show=true`
                        );
                      }}
                    >
                      <FaInfoCircle size={14} className="text-success" />
                      {t("common.show")}
                    </DropdownMenu.Item>
                  </Can>
                </div>
              </DropdownMenu.Content>
            </DropdownMenu.Portal>
          </DropdownMenu.Root>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.data?.length > 0
        ? data?.data
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (currentPage - 1) * 10 + index + 1, // 10 is your page size
              admin_approval:
                AdmminAprovapleTranslation(t)[item.admin_approval],
              admin_approvalEnum: AdmminAprovaple[item.admin_approval],
              start_date: item.start_date,
              end_date: item.end_date,
              total: item.total,
              bidder_type: getKeyByValueCreatingType(item.bidder_type),
            }))
            .reverse()
        : [],
    [data?.data]
  );

  const deleteFUn = async () => {
    try {
      setIsDeleting(true);
      const response = await offerAPis.deleteBond({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      console.log("error", error);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const { watch, reset, setError, setValue } = useForm({
    defaultValues: {
      delegate_id: 0,
      reason_id: 0,
      new_transfer: "",
      creating_by: 0,
      button_type: 0,
    },
  });

  const hadnelApprove = async () => {
    try {
      setIsDeleting(true);
      const dataToSend = {
        status: watch("button_type"),
      };
      const response = await offerAPis.Approve({
        id: selectId,
        status: dataToSend,
      });
      handelCLoseModal();
      reset();
      refetch();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("offer.quotation")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("offer.quotation")}
          handleOrderClicks={handelAddBonds}
          isAddOptions={true}
          canPermission={"offer.store"}
        />
        <Card style={{ height: "90%", padding: 20 }}>
          {/* <CardBody> */}
          <TableContainer
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            currentPage={pageSize}
            setPage={setCurrentPage}
            isLoading={isLoading}
            pageCount={data?.meta?.last_page || 1}
          />
          {/* </CardBody> */}
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={deleteFUn}
          itemName={t("offer.quotation")}
          isDeleting={isDeleting}
        />
      </Container>
      <Modal isOpen={openApproveModel} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {t("types.reason.admin_approve")}
        </ModalHeader>
        <ModalBody>
          <form>
            <ModalFooter>
              <Button
                type="button"
                color="danger"
                disabled={isDeleting}
                className="btn-sm"
                onClick={() => {
                  setValue("button_type", 2);
                  hadnelApprove();
                }}
              >
                {watch("button_type") === 2 && isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.reject")
                )}
              </Button>
              <Button
                disabled={isDeleting}
                onClick={() => {
                  setValue("button_type", 1);
                  hadnelApprove();
                }}
                className="btn-sm"
                color="primary"
              >
                {watch("button_type") === 1 && isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.accept")
                )}
              </Button>
            </ModalFooter>
          </form>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default Offers;
