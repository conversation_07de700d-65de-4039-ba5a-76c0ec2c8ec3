import { Col, Row } from "reactstrap";
import ProductModal from "../productModal";
import CustomInput from "../../../../components/Common/Input";
import TextAreaField from "../../../../components/Common/textArea";
import { useTranslation } from "react-i18next";
import CustomSelect from "../../../../components/Common/Select";
import useSetSelectOptions from "../../../../hooks/use-set-select-options";
import { productQueries } from "../../../../apis/products/query";
import { useEffect, useRef } from "react";

const ProductSection = ({
  openAddModal,
  handelCloseAddModal,
  control,
  isShow,
  errors,
  watch,
  selectedBillType,
  productList,
  setProductList,
  setError,
  setValue,
  isEditMode = false,
  selectedProductId = 0,
}) => {
  const { t } = useTranslation();

  // Track which discount field the user edited last to avoid infinite loops
  const lastEditedRef = useRef(null); // 'value' | 'percentage' | null

  const { data: products } = productQueries.useGetAll({
    status: 1,
    billType: selectedBillType,
  });

  const productsOptions = useSetSelectOptions({
    data: products?.result,
    getOption: (item) => ({
      label: item.name,
      value: item.id,
    }),
  });

  const handelAddProductToList = () => {
    try {
      // Capture all form values upfront to avoid stale closures
      const productId = watch("product_id");
      const quant = Number(watch("quant"));
      const productPrice = Number(watch("product_price"));
      const productTotal = Number(watch("product_total"));
      const productGift = Number(watch("product_gift")) || 0;
      const productNote = watch("product_note") || "";
      const discountValue = Number(watch("discount_product_value")) || 0;
      const discountPercentage =
        Number(watch("discount_product_percentage")) || 0;

      // Clear previous errors
      // Basic validations
      if (!productId) {
        setError("product_id", {
          type: "manual",
          message: t("common.field_required"),
        });
        return;
      }

      if (quant <= 0) {
        setError("quant", {
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (!quant) {
        setError("quant", {
          message: t("bills.validation.quant_required"),
        });
        return;
      }

      if (quant > 5000) {
        setError("quant", {
          type: "manual",
          message: t("bills.validation.greater_than_5000"),
        });
        return;
      }

      if (productPrice <= 0) {
        setError("product_price", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (productTotal <= 0) {
        setError("product_total", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      // Validate discount vs total
      if (discountValue && productTotal < discountValue) {
        setError("discount_product_value", {
          type: "manual",
          message: t("bills.discount_validation"),
        });
        return;
      }

      if (discountValue < 0) {
        setError("discount_product_value", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (discountPercentage < 0) {
        setError("discount_product_percentage", {
          type: "manual",
          message: t("bills.validation.greater_than_0"),
        });
        return;
      }

      if (discountPercentage > 100) {
        setError("discount_product_percentage", {
          type: "manual",
          message: t("bills.validation.max_percentage_value"),
        });
        return;
      }

      // Calculate net price based on the discount
      const netPrice = productTotal - discountValue;

      // Create new product object
      const newProduct = {
        product_id: productId.value,
        product_name: productId.label,
        quant: quant,
        notes: productNote,
        gift: productGift,
        total: productTotal,
        price: productPrice,
        net_price: netPrice,
        discount_value: discountValue,
        discount_percentage: discountPercentage,
        // Add a discount field for backward compatibility
        discount: discountValue,
      };

      if (isEditMode && selectedProductId) {
        // In edit mode, update the existing product
        const newProductList = [...productList];
        const indexToUpdate = newProductList.findIndex(
          (item) => Number(item.product_id) === Number(selectedProductId)
        );

        if (indexToUpdate !== -1) {
          // Update the existing product
          newProductList[indexToUpdate] = newProduct;
          setProductList(newProductList);
        } else {
          // Fallback: add as new if not found (shouldn't happen)
          setProductList([...productList, newProduct]);
        }
      } else {
        // In add mode, simply append to the list
        setProductList([...productList, newProduct]);
      }

      // Clear form fields after successful addition/update
      setValue("product_id", null);
      setValue("quant", "");
      setValue("product_price", "");
      setValue("product_total", "");
      setValue("product_gift", "");
      setValue("product_note", "");
      setValue("discount_product_value", "");
      setValue("discount_product_percentage", "");

      // Reset the last edited ref
      lastEditedRef.current = null;

      // Close modal
      handelCloseAddModal();
    } catch (error) {
      console.log("Error adding product:", error);
    }
  };

  const productsFields = [
    {
      id: 0,
      field: (
        <Col xs={4}>
          <CustomSelect
            control={control}
            name="product_id"
            placeholder={t("common.product")}
            isDisabled={isShow}
            options={productsOptions.filter(
              (item) =>
                !productList.some((val) => val.product_id === item.value)
            )}
            error={errors?.product_id}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 1,
      field: (
        <Col xs={4}>
          <CustomInput
            control={control}
            name="quant"
            isDisabled={!watch("product_id") || isShow}
            placeholder={t("common.quant")}
            type="number"
            min={1}
            max={5000}
            error={errors?.quant}
          />
        </Col>
      ),
      showIn: true,
    },
    {
      id: 4,
      field: (
        <Col xs={4}>
          <CustomInput
            control={control}
            name="product_price"
            isDisabled={isShow}
            placeholder={t("common.price")}
            type="number"
            step="any"
            error={errors?.product_price}
          />
        </Col>
      ),
      showIn: true, // Sales, Return Bills
    },
    {
      id: 5,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            name="product_gift"
            isDisabled={isShow}
            placeholder={t("common.gift")}
            type="number"
            error={errors?.product_gift}
          />
        </Col>
      ),
      showIn: true, // Sales Bill
    },
    {
      id: 6,
      field: (
        <Col xs={6}>
          <CustomInput
            control={control}
            name="product_total"
            isDisabled={isShow}
            placeholder={t("common.total")}
            type="number"
            step="any"
            error={errors?.product_total}
          />
        </Col>
      ),
      showIn: true, // Sales, Return Bills
    },
  ];

  // استخراج السعر عند تغيير المنتج
  useEffect(() => {
    const selectedProductId = watch("product_id")?.value;
    // Only auto-set price if we're NOT in edit mode
    // In edit mode, we want to keep the existing price from handelSetUpdate
    if (selectedProductId && !isEditMode) {
      const selectedProduct = products?.result.find(
        (p) => p.id === selectedProductId
      );
      if (selectedProduct) {
        setValue("product_price", selectedProduct.price); // وضع السعر
      }
    }
  }, [watch("product_id"), isEditMode]);

  useEffect(() => {
    const quant = watch("quant");
    const price = watch("product_price");

    // Only auto-calculate total if we have valid quant and price
    // In edit mode, only recalculate if user changes quant or price
    if (!isNaN(quant) && !isNaN(price) && quant > 0 && price > 0) {
      setValue("product_total", Number(quant) * Number(price));
    }
  }, [watch("quant"), watch("product_price")]);

  // Sync discount percentage when discount value or total changes (avoid loops)
  useEffect(() => {
    const totalRaw = watch("product_total");
    const discountValueRaw = watch("discount_product_value");

    const total = Number(totalRaw);
    const discountValue = Number(discountValueRaw);

    // Run only when the user last edited the value field (or on first edit before any flag set)
    if (lastEditedRef.current === "value") {
      const safeTotal = isFinite(total) && total > 0 ? total : 0;
      const clampedValue = Math.max(
        0,
        Math.min(isNaN(discountValue) ? 0 : discountValue, safeTotal)
      );
      if (clampedValue !== discountValue) {
        setValue("discount_product_value", Number(clampedValue.toFixed(2)));
      }
      const nextPerc = safeTotal > 0 ? (clampedValue / safeTotal) * 100 : 0;
      const currPerc = Number(watch("discount_product_percentage"));
      const roundedPerc = Number(nextPerc.toFixed(2));
      if (!isNaN(roundedPerc) && roundedPerc !== currPerc) {
        setValue("discount_product_percentage", roundedPerc);
      }
    }
  }, [watch("discount_product_value"), watch("product_total")]);

  // Sync discount value when discount percentage or total changes (avoid loops)
  useEffect(() => {
    const totalRaw = watch("product_total");
    const discountPercRaw = watch("discount_product_percentage");

    const total = Number(totalRaw);
    const discountPerc = Number(discountPercRaw);

    if (lastEditedRef.current === "percentage") {
      const safePerc = isNaN(discountPerc)
        ? 0
        : Math.max(0, Math.min(discountPerc, 100));
      if (safePerc !== discountPerc) {
        setValue("discount_product_percentage", Number(safePerc.toFixed(2)));
      }
      const nextVal =
        isFinite(total) && total > 0 ? (total * safePerc) / 100 : 0;
      const currVal = Number(watch("discount_product_value"));
      const roundedVal = Number(nextVal.toFixed(2));
      if (!isNaN(roundedVal) && roundedVal !== currVal) {
        setValue("discount_product_value", roundedVal);
      }
    }
  }, [watch("discount_product_percentage"), watch("product_total")]);

  return (
    <ProductModal
      isOpen={openAddModal}
      onClose={handelCloseAddModal}
      onAdd={handelAddProductToList}
      isEditMode={isEditMode}
    >
      <Row className="g-2">
        {productsFields.map((item) => item.showIn && item.field)}
        <Col xs={6}>
          <CustomInput
            control={control}
            name="discount_product_value"
            isDisabled={isShow}
            placeholder={t("bills.discount_value")}
            type="number"
            step="any"
            error={errors?.product_discount_value}
            isOnChange={() => {
              lastEditedRef.current = "value";
            }}
          />
        </Col>
        <Col xs={6}>
          <CustomInput
            control={control}
            name="discount_product_percentage"
            isDisabled={isShow}
            placeholder={t("bills.discount_percentage")}
            type="number"
            step="any"
            error={errors?.product_discount_percentage}
            isOnChange={() => {
              lastEditedRef.current = "percentage";
            }}
          />
        </Col>
        <Col xs={12} className="mt-2">
          <TextAreaField
            placeholder={t("common.note")}
            name="product_note"
            control={control}
            defaultValue=""
            className="mb-2"
            disabled={isShow}
          />
        </Col>
      </Row>
    </ProductModal>
  );
};

export default ProductSection;
