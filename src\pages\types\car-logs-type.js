import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import { CarLogsTypesAPis } from "../../apis/types/car-logs-types/api";
import { carLogsTypesQueries } from "../../apis/types/car-logs-types/query";
import { Link, useLocation } from "react-router-dom";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
import * as yup from "yup";

import "./roles.scss";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import { yupResolver } from "@hookform/resolvers/yup";
const CarLogsType = () => {
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = carLogsTypesQueries.useGetAll({ limit: 10, page: page });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const { data: contractType, isLoading: isLoadingContractType } =
    carLogsTypesQueries.useGet({
      id: selectId,
    });
  const [isShow, setIsShow] = useState(false);
  const { t, i18n } = useTranslation();

  const [isDeleting, setIsDeleting] = useState(false);
  const [openSidebar, setOPenSideBar] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const carLogsType = [
    {
      title: t("types.car_logs.start_use"),
      label: t("types.car_logs.start_use"),
      value: 1,
    },
    {
      title: t("types.car_logs.end_use"),
      label: t("types.car_logs.end_use"),
      value: 2,
    },
    {
      title: t("types.car_logs.fill_fuel"),
      label: t("types.car_logs.fill_fuel"),
      value: 3,
    },
    {
      title: t("types.car_logs.fast_repair"),
      label: t("types.car_logs.fast_repair"),
      value: 4,
    },
    {
      title: t("types.car_logs.give_to_repair"),
      label: t("types.car_logs.give_to_repair"),
      value: 5,
    },
    {
      title: t("types.car_logs.give_from_repair"),
      label: t("types.car_logs.give_from_repair"),
      value: 6,
    },
    {
      title: t("types.car_logs.update_km"),
      label: t("types.car_logs.update_km"),
      value: 7,
    },
  ];

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    watch,
    setError,
  } = useForm({
    defaultValues: {
      title: { en: "", ar: "" },
      log_type: 0,
      status: 1,
    },
    // resolver: yupResolver(schema),
  });

  const statusOptions = [
    { value: 1, label: t("common.active") },
    { value: 2, label: t("common.in_active") },
  ];

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingContractType && contractType?.result) {
      // Populate form with role data when loaded

      const selectedItem = carLogsType.find(
        (item) => item.value === contractType?.result?.log_type
      );

      reset({
        title: {
          en: contractType?.result.title.en
            ? contractType?.result.title.en
            : contractType?.result.title.ar,
          ar: contractType?.result.title.ar
            ? contractType?.result.title.ar
            : contractType?.result.title.en,
        },
        log_type: { label: selectedItem?.label, value: selectedItem?.value },
        status: {
          value: contractType?.result.status,
          label: statusOptions.find(
            (item) => item.value === contractType?.result?.status
          )?.label,
        },
      });
    }
  }, [selectId, isLoadingContractType, contractType]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    reset({ title: "", description: "" });
    setSelectId(null);
    setIsShow(false);
    refetch();
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelCLoseModal = () => {
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
  };

  const UpdateFun = async (data) => {
    try {
      const response = await CarLogsTypesAPis.update({
        title: {
          en: data.title.en ? data.title.en : data.title.ar,
          ar: data.title.ar ? data.title.ar : data.title.en,
        },
        log_type: Number(data.log_type.value),
        status: Number(data.status.value),
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await CarLogsTypesAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await CarLogsTypesAPis.inActive({
        id: id,
      });
      refetch();
      handelCloseSideBar();
      setIsDeleting(false);
      toastr.success(response.message);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    {
      title: t("types.car_logs.car_logs_type"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("car_log_type.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("car_log_type.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.car_logs.car_logs_type"),
      accessor: "log_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              // defaultChecked={cellProps.status === 1 ? true : false}
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
            />
            <></>
          </div>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"car_log_type.update"}>
              <Link
                to="#"
                className="me-3 text-primary"
                onClick={() => {
                  if (cellProps.is_default !== 1) {
                    handelOpenSideBar();
                    // handelSelectId(cellProps.id);
                    setSelectId(cellProps.id);
                  }
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"car_log_type.show"}>
              <Link
                onClick={() => {
                  handelOpenSideBar();
                  handelSelectId(cellProps.id);
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title:
                i18n.language === "eng"
                  ? truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    })
                  : truncateText({
                      text: item.title.ar,
                      maxLengthPercent: 0.3,
                    }),
              status: item.status,
              log_type:
                carLogsType.find((value) => value.value === item.log_type)
                  ?.title || "----",
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.car_logs.car_logs_type")}
          breadcrumbItems={breadcrumbItems}
        />
        <div>
          <Modal
            isOpen={openSidebar}
            toggle={handelCloseSideBar}
            backdrop="static"
          >
            <ModalHeader toggle={handelCloseSideBar}>
              {/* {isShow
                ? t("types.car_logs.car_logs_type")
                : t("common.update") + " " + t("types.car_logs.car_logs_type")} */}
              {isShow
                ? t("common.show") + " " + t("permissions.groups.car_log")
                : selectId
                ? t("common.update") + " " + t("permissions.groups.car_log")
                : t("common.add") + " " + t("permissions.groups.car_log")}
            </ModalHeader>
            <ModalBody>
              <form onSubmit={handleSubmit(UpdateFun)}>
                {isLoadingContractType ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <Row>
                    <Col xs={6}>
                      <div className="mb-4">
                        <CustomInput
                          name="title.en"
                          control={control}
                          label={t("common.title_in_english")}
                          type="text"
                          placeholder={t("common.title_in_english")}
                          disabled={isShow}
                          error={errors.title?.en}
                          rules={{ required: t("common.field_required") }}
                        />
                      </div>
                    </Col>
                    <Col xs={6}>
                      <div className="mb-4">
                        <CustomInput
                          name="title.ar"
                          control={control}
                          label={t("common.title_in_arabic")}
                          type="text"
                          placeholder={t("common.title_in_arabic")}
                          disabled={isShow}
                          error={errors.title?.ar}
                        />
                      </div>
                    </Col>
                    <Col xs={12}>
                      <div className="mb-4">
                        <CustomSelect
                          name="log_type"
                          label={t("types.car_logs.car_logs_type")}
                          control={control}
                          options={carLogsType}
                          isDisabled={selectId || isShow}
                          error={errors.log_type}
                        />
                      </div>
                    </Col>
                    {/* <Col xs={12}>
                      <div className="mb-4">
                        <CustomInput
                          name="status"
                          control={control}
                          label={t("common.status")}
                          options={carLogsType}
                          isMulti={false}
                          placeholder={t("common.status")}
                          disabled={isShow || selectId}
                          error={errors.status}
                        />
                      </div>
                    </Col> */}
                    <Col xs={12}>
                      <div className="mb-4">
                        <CustomSelect
                          name="status"
                          control={control}
                          label={t("common.status")}
                          options={statusOptions}
                          isMulti={false}
                          isDisabled={isShow}
                          error={errors.status}
                          rules={{ required: t("common.field_required") }}
                        />
                      </div>
                    </Col>
                  </Row>
                )}
                <ModalFooter>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCloseSideBar}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={
                        isSubmitting ||
                        !watch("title")?.en ||
                        !watch("title")?.ar
                      }
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : (
                        t("common.update")
                      )}
                    </Button>
                  )}
                </ModalFooter>
              </form>
            </ModalBody>
          </Modal>
        </div>
        <Card>
          <CardBody>
            {/* {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ContractTypes?.result?.length === 0 ? (
              <div className="container-loading">
                <p>{t("common.no-data")}</p>
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
              isLoading={isLoadingUsers}
            />
            {/* )} */}
          </CardBody>
        </Card>
        {openStatsusModal && selectId && (
          <Modal isOpen={openStatsusModal} backdrop="static">
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <ModalFooter>
                <Button type="button" color="light" onClick={handelCLoseModal}>
                  {t("common.no")}
                </Button>
                <Button
                  onClick={() =>
                    statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                  }
                  disabled={isDeleting}
                  type="button"
                  color="primary"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
      </Container>
    </div>
  );
};
export default CarLogsType;
