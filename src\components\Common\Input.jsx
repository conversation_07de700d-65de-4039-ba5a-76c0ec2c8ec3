import React, { useState, useEffect } from "react";
import { Controller } from "react-hook-form";

const CustomInput = ({
  name,
  control,
  label,
  type = "text",
  placeholder,
  isDisabled = false,
  defaultValue = "",
  className = "",
  inputClassName = "",
  error,
  isLoading = false,
  startIcon,
  endIcon,
  firstIconStyle,
  lastIconStyle,
  containerStyle,
  inputStyle,
  onFocusChange,
  disabled,
  disable,
  isOnChange,
  step,
  ...rest
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  useEffect(() => {
    if (defaultValue) {
      setHasValue(true);
    }
  }, [defaultValue]);

  const handleFocus = (e) => {
    setIsFocus(true);
    e.target.parentElement.style.boxShadow =
      "0 0 0 0.2rem rgba(13,110,253,.25)";
    if (onFocusChange) onFocusChange(true, name);
  };

  const handleBlur = (e) => {
    setIsFocus(false);
    e.target.parentElement.style.boxShadow = "none";
    setHasValue(e.target.value ? true : false);
    if (onFocusChange) onFocusChange(false, name);
  };

  const handleChange = (e, onChange) => {
    onChange(e);
    if (isOnChange) isOnChange(e.target.value);
    setHasValue(!!e.target.value);
  };

  return (
    <div className={`${className}`}>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        render={({ field }) => {
          if (field.value && !hasValue) {
            setHasValue(true);
          }

          return (
            <div
              className={`d-flex rounded position-relative ${
                isFocus ? "focused" : ""
              }`}
              style={{
                transition: "all 0.5s ease-in-out",
                border: error
                  ? "2px solid #f00"
                  : isFocus
                  ? "2px solid #0d6efd"
                  : "2px solid #ccd",
                ...containerStyle,
              }}
            >
              {startIcon && <div style={firstIconStyle}>{startIcon}</div>}
              <div
                className="position-absolute floating-label"
                style={{
                  top: isFocus || hasValue ? -10 : 8,
                  insetInlineStart: startIcon || endIcon ? 5 : 10,
                  fontSize: isFocus || hasValue ? 12 : 15,
                  background: "white",
                  display: "flex",
                  alignItems: "center",
                  width:
                    endIcon || startIcon || isFocus || hasValue
                      ? "auto"
                      : "90%",
                  height: isFocus || hasValue ? "fit-content" : "60%",
                  padding: "0 5px",
                  color: isFocus ? "#0d6efd" : "#808080",
                  transition: "all 0.3s ease-in-out",
                  zIndex: 10,
                  pointerEvents: "none",
                }}
              >
                <span>{placeholder || label}</span>
              </div>
              <input
                {...field}
                {...rest}
                step={step}
                type={type}
                disabled={isDisabled || isLoading || disabled || disable}
                className={`form-control border-0 shadow-none ${
                  error ? "is-invalid" : ""
                } ${inputClassName} `}
                style={{
                  fontSize: "0.8rem",
                  height: "37px",
                  background: "none",
                  padding: "10px 12px",
                  position: "relative",
                  zIndex: 1,
                  ...inputStyle,
                }}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onChange={(e) => handleChange(e, field.onChange)}
              />
              {endIcon && !isLoading && (
                <div style={lastIconStyle}>{endIcon}</div>
              )}
            </div>
          );
        }}
      />
      {error && <div className="invalid-feedback d-block">{error.message}</div>}
    </div>
  );
};

export default CustomInput;
