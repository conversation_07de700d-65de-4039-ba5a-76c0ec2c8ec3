import ApiInstance from "../../constant/api-instance";
import { LOCATIONS_ROUTES } from "./route";

const getAll = async ({ limit, page, name }) => {
  const { data } = await ApiInstance.get(`${LOCATIONS_ROUTES.LOCATIONS}`, {
    params: {
      limit,
      page,
      name,
    },
  });
  return data;
};

const getSearchLocation = async ({ limit, page, name, city_id }) => {
  const { data } = await ApiInstance.get(
    `${LOCATIONS_ROUTES.SEARCH}/${city_id}`,
    {
      params: {
        limit,
        page,
        name,
      },
    }
  );
  return data;
};

const getSearchLocationByCities = async ({ city_ids }) => {
  const { data } = await ApiInstance.get(
    `${LOCATIONS_ROUTES.SEARCH}/${city_ids}`,
    {
      params: {
        "city_ids[]": city_ids,
      },
    }
  );
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${LOCATIONS_ROUTES.LOCATIONS}/${id}`,
    {}
  );
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${LOCATIONS_ROUTES.LOCATIONS}`,
    payload
  );
  return data;
};

const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${LOCATIONS_ROUTES.LOCATIONS}/${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${LOCATIONS_ROUTES.LOCATIONS}/${id}`
  );
  return data;
};
export const locationsAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
  getSearchLocation,
  getSearchLocationByCities,
};
