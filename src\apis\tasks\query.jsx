import { useQuery } from "@tanstack/react-query";
import { tasksAPis } from "./api";

const useGetAll = ({ limit, page, searchParams }) => {
  const queryResult = useQuery({
    queryKey: ["all-tasks", limit, page, searchParams],
    queryFn: () => tasksAPis.getAll({ limit, page, searchParams }),
  });
  return queryResult;
};

const useGetAllTransfer = () => {
  const queryResult = useQuery({
    queryKey: ["all-transfer-list"],
    queryFn: () => tasksAPis.transferList(),
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["user", id],
    queryFn: () =>
      id > 0 ? tasksAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const allTasksQueries = {
  useGet,
  useGetAll,
  useGetAllTransfer,
};
