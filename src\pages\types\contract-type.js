import { useTranslation } from "react-i18next";
import { Card, CardBody, Container, Label, Col } from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import toastr from "toastr";
import { ContractTypesAPis } from "../../apis/types/contract-type/api";
import { ContractQueries } from "../../apis/types/contract-type/query";

import "./roles.scss";
import { Link, useLocation } from "react-router-dom";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomTextArea from "../../components/Common/textArea";
import TypesModel from "../../components/Common/types-model";
import { FaPenToSquare } from "react-icons/fa6";
import { FaInfoCircle } from "react-icons/fa";

const ContractType = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = ContractQueries.useGetAllContractTypes({ limit: 10, page: page });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [isShow, setIsShow] = useState(false);

  const {
    data: contractType,
    isLoading: isLoadingContractType,
    isFetching,
    refetch: otherRefetch,
    isRefetching,
  } = ContractQueries.useGetContractType({
    id: selectId,
  });

  const [openSidebar, setOPenSideBar] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
    },
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingContractType && contractType?.result) {
      console.log("asdsad", contractType?.result);
      setValue(
        "titleArabic",
        contractType?.result?.title.ar
          ? contractType?.result?.title.ar
          : contractType?.result?.title.en
      );
      setValue(
        "titleEnglish",
        contractType?.result?.title.en
          ? contractType?.result?.title.en
          : contractType?.result?.title.ar
      );
      setValue(
        "descriptionArabic",
        contractType?.result?.description.ar
          ? contractType?.result?.description.ar
          : ""
      );
      setValue(
        "descriptionEnglish",
        contractType?.result?.description.en
          ? contractType?.result?.description.en
          : ""
      );
    }
  }, [selectId, isLoadingContractType, contractType?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    setSelectId(null);
    setIsShow(false);
    refetch();
    reset();
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const UpdateFun = async (data) => {
    try {
      const response = await ContractTypesAPis.updateContractTypes({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      reset();
      handelCloseSideBar();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    {
      title: t("types.contract_types.contract_type"),
      link: pathname,
    },
  ];
  const formFields = [
    {
      name: "titleEnglish",
      label: t("common.title_in_english"),
      type: "text",
      col: 12,
      disabled: isShow,
      error: errors.titleEnglish,
    },
    {
      name: "titleArabic",
      label: t("common.title_in_arabic"),
      type: "text",
      col: 12,
      disabled: isShow,
      error: errors.titleArabic,
    },
    {
      name: "descriptionEnglish",
      label: t("common.description_in_english"),
      type: "textarea",
      col: 12,
      disabled: isShow,
      error: errors.descriptionEnglish,
    },
    {
      name: "descriptionArabic",
      label: t("common.description_in_arabic"),
      type: "textarea",
      col: 12,
      disabled: isShow,
      error: errors.descriptionArabic,
    },
  ];
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.contract_types.contract_count"),
      accessor: "contracts_count",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.type"),
      accessor: "type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"contract_type.update"}>
              <Link
                to="#"
                className="me-3 text-primary"
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenSideBar();
                    handelSelectId(cellProps.id);
                  }
                }}
              >
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"contract_type.show"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenSideBar();
                    handelSelectId(cellProps.id);
                    setIsShow(true);
                  }
                }}
                className="text-success"
              >
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title:
                i18n.language === "eng"
                  ? truncateText({
                      text:
                        i18n.language === "eng" ? item.title.en : item.title.ar,
                      maxLengthPercent: 0.3,
                    })
                  : truncateText({
                      text: item.title.ar,
                      maxLengthPercent: 0.3,
                    }),
              description:
                truncateText({
                  text: item.description,
                  maxLengthPercent: 0.3,
                }) || "----",
              contracts_count: item.contracts_count || "----",
              type:
                item.type === 1
                  ? t("types.contract_types.sell_contract")
                  : t("types.contract_types.rent_contract"),
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );
  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("types.contract_types.contract_type")}
          breadcrumbItems={breadcrumbItems}
        />
        <Card style={{ height: "90%" }}>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              handleOrderClicks={handelOpenSideBar}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
              isLoading={isLoadingUsers}
            />
          </CardBody>
        </Card>

        <TypesModel
          actionSubmit={UpdateFun}
          title={
            (isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.add")) +
            " " +
            t("types.contract_types.contract_type")
          }
          open={openSidebar}
          handelClose={handelCloseSideBar}
          handleSubmit={handleSubmit}
          content={formFields.map((field) => (
            <Col key={field.name} xs={field.col || 12}>
              {field.type === "textarea" ? (
                <div className="mb-2">
                  <Label className="form-label" htmlFor={field.name}>
                    {field.label}
                  </Label>
                  <CustomTextArea
                    name={field.name}
                    control={control}
                    placeholder={field.label}
                    isShow={field.disabled}
                    error={field.error}
                    rows={4}
                    className={`${field.error ? "is-invalid" : ""}`}
                  />
                </div>
              ) : (
                <div className="mb-3">
                  <CustomInput
                    name={field.name}
                    control={control}
                    label={field.label}
                    type={field.type}
                    disabled={field.disabled}
                    error={field.error}
                    style={{ fontSize: "12px" }}
                  />
                </div>
              )}
            </Col>
          ))}
          isSubmitting={isSubmitting}
          isLoading={isLoadingContractType || isRefetching}
          disableButtons={isSubmitting}
          isUpdate={true}
          showAddButton={!isShow}
        />
      </Container>
    </div>
  );
};
export default ContractType;
