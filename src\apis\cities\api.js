import ApiInstance from "../../constant/api-instance";
import { CITIES_ROUTES } from "./route";

const getAll = async ({ limit, page, name }) => {
  const { data } = await ApiInstance.get(`${CITIES_ROUTES.CITIES}`, {
    params: {
      limit,
      page,
      name,
    },
  });
  return data;
};

const getSearchCity = async ({ limit, page, name, state_id }) => {
  const { data } = await ApiInstance.get(
    `${CITIES_ROUTES.SEARCH}/${state_id}`,
    {
      params: {
        limit,
        page,
        name,
      },
    }
  );
  return data;
};

const getSearchCityStates = async ({ state_ids }) => {
  const { data } = await ApiInstance.get(
    `${CITIES_ROUTES.SEARCH}/${state_ids}`,
    {
      params: {
        state_ids,
      },
    }
  );
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CITIES_ROUTES.CITIES}/${id}`, {});
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(`${CITIES_ROUTES.CITIES}`, payload);
  return data;
};

const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${CITIES_ROUTES.CITIES}/${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${CITIES_ROUTES.CITIES}/${id}`);
  return data;
};
export const citiesAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
  getSearchCity,
  getSearchCityStates,  
};
