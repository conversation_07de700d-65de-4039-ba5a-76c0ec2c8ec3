import { useQuery } from "@tanstack/react-query";
import { clientsAPis } from "./api";

const useGetAll = ({ limit, page, status, searchParams }) => {
  const queryResult = useQuery({
    queryKey: ["all-clients", limit, page, status, searchParams],
    queryFn: () => clientsAPis.getAll({ limit, page, status, searchParams }),
  });
  return queryResult;
};

const useGetClientDependOnDelegate = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["all-delegate-depedn-on-client", id],
    queryFn: () =>
      id > 0
        ? clientsAPis.getAllDelegateDependOnClient({ id })
        : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["clients", id],
    queryFn: () =>
      id > 0 ? clientsAPis.getTask({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

const useGetClientProduct = ({ id, enabled }) => {
  const queryResult = useQuery({
    queryKey: ["all-clients-products"],
    queryFn: () => clientsAPis.getClientProduct({ id }),
    enabled: enabled,
  });
  return queryResult;
};

const useGetOneClient = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["clients-prod", id],
    queryFn: () =>
      id > 0 ? clientsAPis.getClient({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const clientsQueries = {
  useGetAll,
  useGet,
  useGetClientDependOnDelegate,
  useGetClientProduct,
  useGetOneClient,
};
