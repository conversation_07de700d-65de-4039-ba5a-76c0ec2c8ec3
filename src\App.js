import React, { useEffect } from "react";
import { Route, Routes, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

// Import Routes
import { authProtectedRoutes, publicRoutes } from "./routes/";

// layouts
import VerticalLayout from "./components/VerticalLayout/";
import HorizontalLayout from "./components/HorizontalLayout/";
import NonAuthLayout from "./components/NonAuthLayout";

// Import scss
import "./assets/scss/theme.scss";

import "./index.css";

// Fake backend
import { setupInterceptors } from "./constant/api-instance";
import { PermissionsProvider } from "./components/permissions-way/context";

const App = () => {
  const layout = useSelector((state) => state.Layout);

  const getLayout = () => {
    switch (layout.layoutType) {
      case "horizontal":
        return HorizontalLayout;
      default:
        return VerticalLayout;
    }
  };

  const { pathname } = useLocation();

  const Layout = getLayout();

  // const { data } = RolesQueries.useGetAllPermission({ enabled: true });

  const userStore = localStorage.getItem("authUser");
  const parsedStore = userStore ? JSON.parse(userStore) : null;
  const permissions = parsedStore?.user?.roles?.[0]?.permissions || [];

  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("authUser");
    const foundRoute = publicRoutes.find((item) => item.path === pathname);
    if (token && foundRoute) {
      navigate("/dashboard");
    }
  }, []);

  return (
    <InterceptorWrapper>
      {/* <PermissionsProvider permissions={data?.result}> */}
      <PermissionsProvider permissions={permissions}>
        <Routes>
          <React.Fragment>
            {/* Public Routes */}
            {publicRoutes.map((route, idx) => (
              <Route
                path={route.path}
                element={
                  <NonAuthLayout>
                    <route.component />
                  </NonAuthLayout>
                }
                key={idx}
              />
            ))}

            {/* Auth Protected Routes */}
            {authProtectedRoutes.map((route, idx) => (
              <Route
                path={route.path}
                element={
                  <Layout>
                    <route.component />
                  </Layout>
                }
                key={idx}
              />
            ))}
          </React.Fragment>
        </Routes>
      </PermissionsProvider>
    </InterceptorWrapper>
  );
};

export default App;

const InterceptorWrapper = ({ children }) => {
  const navigate = useNavigate();

  useEffect(() => {
    setupInterceptors(navigate); // Set up interceptors after navigate is available
  }, [navigate]);

  return <>{children}</>; // Render the app's children (routes, layout, etc.)
};
