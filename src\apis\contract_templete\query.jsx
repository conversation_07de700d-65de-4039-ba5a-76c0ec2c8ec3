import { useQuery } from "@tanstack/react-query";
import { contractTemplateAPis } from "./api";

const useGetAll = ({ limit, page,templateId,language }) => {
  const queryResult = useQuery({
    queryKey: ["contract-template", limit, page, templateId, language],
    queryFn: () =>
      contractTemplateAPis.getAll({
        limit,
        page,
        templateId,
        language,
      }),
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["template", id],
    queryFn: () =>
      id > 0
        ? contractTemplateAPis.getOne({ id })
        : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const contractTemplateQueries = {
  useGetAll,
  useGet,
};
