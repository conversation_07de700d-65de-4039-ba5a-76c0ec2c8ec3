import { useTranslation } from "react-i18next";
import C<PERSON><PERSON>oader from "react-spinners/ClipLoader";
import {
  <PERSON>ton,
  Modal,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
  <PERSON>,
} from "reactstrap";

const TypesModel = ({
  isLoading,
  open,
  handelClose,
  title,
  handleSubmit,
  actionSubmit,
  content,
  isSubmitting,
  disableButtons,
  showAddButton = true,
  isUpdate = false,
  hideAll,
  customModelStyle,
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      style={{ ...customModelStyle }}
      isOpen={open}
      toggle={handelClose}
      fade={false}
    >
      {!hideAll && <ModalHeader toggle={handelClose}>{title}</ModalHeader>}
      <ModalBody>
        {!hideAll ? (
          <form onSubmit={handleSubmit ? handleSubmit(actionSubmit) : () => {}}>
            {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <Row>{content}</Row>
            )}
            <ModalFooter>
              <Button
                type="button"
                color="light"
                onClick={handelClose}
                className="btn-sm "
                disabled={isSubmitting}
                style={{ height: "32px", width: "54px" }}
              >
                {t("common.close")}
              </Button>
              {showAddButton && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button "
                  type="submit"
                  disabled={disableButtons}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : !isUpdate ? (
                    t("common.add")
                  ) : (
                    t("common.update")
                  )}
                </Button>
              )}
            </ModalFooter>
          </form>
        ) : (
          content
        )}
      </ModalBody>
    </Modal>
  );
};

export default TypesModel;
