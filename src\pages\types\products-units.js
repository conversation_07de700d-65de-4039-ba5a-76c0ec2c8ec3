import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Label,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import { ProductsUnitsQueries } from "../../apis/types/product-units/query";
import toastr from "toastr";
import { ProductsUnitsAPis } from "../../apis/types/product-units/api";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomTextArea from "../../components/Common/textArea";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const ProductsUnites = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = ProductsUnitsQueries.useGetAllProductsUnits({ limit: 10, page: page });

  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [isShow, setIsShow] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
    handelCancel();
    setSelectId(undefined);
    setIsShow(false);
    refetch();
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
  };
  const [openSidebar, setOPenSideBar] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const handelAddBonds = () => {
    handelOpenSideBar();
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      await ProductsUnitsAPis.activeUser({
        id: id,
      });
      refetch();
      toastr.success("Active done ");
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      toastr.error(error?.response?.data?.message);
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      await ProductsUnitsAPis.inActiveUser({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success("in Active done");
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      toastr.error(error?.response?.data?.message);
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("types.units.products_units"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.count"),
      accessor: "products_Count",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: "status",
    //   disableFilters: true,
    //   filterable: false,
    //   accessor: (cellProps) => {
    //     return (
    //       <div className="form-check form-switch">
    //         <Input
    //           type="checkbox"
    //           className="form-check-input"
    //           defaultChecked={cellProps.status === 1 ? true : false}
    //           onClick={() => handelToggleStatus({ cellProps })}
    //         />
    //         <></>
    //       </div>
    //     );
    //   },
    // },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"product_unit.update"}>
              <Link
                to={""}
                className="text-primary"
                onClick={() => {
                  handelOpenSideBar();
                  setSelectId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"product_unit.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    // handelSelectId(cellProps.id);
                    setSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"product_unit.show"}>
              <Link
                onClick={() => {
                  handelOpenSideBar();
                  setSelectId(cellProps.id);
                  setIsShow(true);
                }}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              title: i18n.language === "eng" ? item.title.en : item.title.ar,
              description: item.description,
              products_Count: item.products_count,
              status: item.status,
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await ProductsUnitsAPis.deleteUser({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error(error?.response?.data?.message);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const { data: contractType, isLoading: isLoadingContractType } =
    ProductsUnitsQueries.useGetProductsUnit({
      id: Number(selectId),
    });

  const handelCancel = () => {
    clearErrors();
    reset({
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      status: 1,
    });
    setOPenSideBar(false);
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    watch,
    setError,
    clearErrors,
  } = useForm({
    defaultValues: {
      titleArabic: "",
      titleEnglish: "",
      descriptionArabic: "",
      descriptionEnglish: "",
      status: 1,
    },
    // resolver: yupResolver(schema),
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingContractType && contractType?.result) {
      // Populate form with role data when loaded
      reset({
        titleArabic: truncateText({
          text:
            contractType?.result?.title.ar || contractType?.result?.title.en,
          maxLengthPercent: 0.3,
        }),
        titleEnglish: truncateText({
          text:
            contractType?.result?.title.en || contractType?.result?.title.ar,
          maxLengthPercent: 0.3,
        }),
        descriptionArabic:
          truncateText({
            text: contractType?.result?.description.ar,
            maxLengthPercent: 0.3,
          }) || "",
        descriptionEnglish:
          truncateText({
            text: contractType?.result?.description.en,
            maxLengthPercent: 0.3,
          }) || "",
        status: contractType?.result?.status,
      });
    }
  }, [selectId, isLoadingContractType, contractType, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      clearErrors();
      const response = await ProductsUnitsAPis.updateUser({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        status: data.status,
        id: Number(selectId),
      });
      toastr.success(response.message);
      handelCLoseModal();
      reset();
      refetch();
    } catch (error) {
      // Map the title error to both fields
      if (error?.response?.data?.errors?.title) {
        setError("titleArabic", { message: error.response.data.errors.title });
        setError("titleEnglish", { message: error.response.data.errors.title });
      } else {
        handleBackendErrors({ error, setError });
      }
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    try {
      clearErrors();
      const response = await ProductsUnitsAPis.addUser({
        title: {
          en: data.titleEnglish ? data.titleEnglish : data.titleArabic,
          ar: data.titleArabic ? data.titleArabic : data.titleEnglish,
        },
        description: {
          en: data.descriptionEnglish,
          ar: data.descriptionArabic,
        },
        status: data.status,
      });
      toastr.success(response.message);
      handelCLoseModal();
      refetch();
      reset();
    } catch (error) {
      // Map the title error to both fields
      if (error?.response?.data?.errors?.title) {
        setError("titleArabic", { message: error.response.data.errors.title });
        setError("titleEnglish", { message: error.response.data.errors.title });
      } else {
        handleBackendErrors({ error, setError });
      }
    }
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.units.products_units")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("types.units.products_units")}
          handleOrderClicks={handelAddBonds}
          canPermission={"product_unit.store"}
        />
        <Card>
          <CardBody>
            {/* {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              // isGlobalFilter={true}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              // className="custom-header-css table align-middle table-nowrap"
              // tableClassName="table-centered align-middle table-nowrap mb-0"
              // theadClassName="text-muted table-light"
              isLoading={isLoadingUsers}
            />
            {/* )} */}
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("types.units.products_units")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
      <Modal isOpen={openSidebar} toggle={handelCLoseModal} backdrop="static">
        <h1 className="px-4 pt-3">
          {(isShow
            ? t("common.show")
            : selectId
            ? t("common.update")
            : t("common.add")) +
            " " +
            t("products.product_unit")}
        </h1>
        <ModalBody>
          <form
            onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
          >
            {isLoadingContractType ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <>
                <Row>
                  <Col xs={6}>
                    <CustomInput
                      name="titleEnglish"
                      control={control}
                      label={t("common.title_in_english")}
                      type="text"
                      placeholder={t("common.title_in_english")}
                      disabled={isShow}
                      error={errors?.titleEnglish}
                      rules={{ required: t("common.field_required") }}
                    />
                  </Col>
                  <Col xs={6}>
                    <CustomInput
                      name="titleArabic"
                      control={control}
                      label={t("common.title_in_arabic")}
                      type="text"
                      placeholder={t("common.title_in_arabic")}
                      disabled={isShow}
                      error={errors?.titleArabic}
                      rules={{ required: t("common.field_required") }}
                    />
                  </Col>
                </Row>
                <div className="mb-2">
                  <Label className="form-label">
                    {t("common.description_in_arabic")}
                  </Label>
                  <CustomTextArea
                    name="descriptionArabic"
                    control={control}
                    placeholder={t("common.description_in_arabic")}
                    isShow={isShow}
                    rows={4}
                  />
                </div>
                <div className="mb-4">
                  <Label className="form-label">
                    {t("common.description_in_english")}
                  </Label>
                  <CustomTextArea
                    name="descriptionEnglish"
                    control={control}
                    placeholder={t("common.description_in_english")}
                    isShow={isShow}
                    rows={4}
                  />
                </div>
                {/* <div className="mb-4">
                  <Label className="form-label" htmlFor="total">
                    Status
                  </Label>
                  <select
                    placeholder="status"
                    className="form-control"
                    {...register("status", { required: true })} // register the select field
                  >
                    <option value="1">Active</option>{" "}
                    <option value="2">Not Active</option>{" "}
                  </select>
                </div> */}
              </>
            )}
            <ModalFooter>
              <Button
                type="button"
                color="light"
                onClick={handelCLoseModal}
                className="btn-sm "
                style={{ height: "32px", width: "54px" }}
              >
                {t("common.close")}
              </Button>
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={
                    isSubmitting ||
                    (!watch("titleEnglish") && !watch("titleArabic"))
                  }
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
              )}
            </ModalFooter>
          </form>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default ProductsUnites;
