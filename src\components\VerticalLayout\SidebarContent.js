import React, { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import MetisMenu from "metismenujs";
import { connect } from "react-redux";
import { useTranslation, withTranslation } from "react-i18next";
import Collapse from "reactstrap/lib/Collapse";
import {
  changeLayout,
  changeSidebarTheme,
  changeSidebarType,
  changeLayoutWidth,
  changePreloader,
} from "../../store/actions";
import {
  MdKeyboardArrowUp,
  MdLocalOffer,
  MdOutbond,
  MdOutlineTravelExplore,
} from "react-icons/md";
import { MdKeyboardArrowDown } from "react-icons/md";
import { MdOutlineDashboardCustomize } from "react-icons/md";
import { MdFormatListNumberedRtl } from "react-icons/md";
import { FaCarSide, FaFileContract, FaStoreAlt } from "react-icons/fa";
import { FaRegUser } from "react-icons/fa";
import { FaPersonChalkboard, FaUserGear } from "react-icons/fa6";
import { RiProductHuntFill } from "react-icons/ri";
import { LiaTasksSolid } from "react-icons/lia";
import { IoNewspaperOutline } from "react-icons/io5";

const SidebarContent = (props) => {
  const [activeMenu, setActiveMenu] = useState(null);
  const [isOpen, setIsOpen] = useState(null);
  const [hoveredItem, setHoveredItem] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [tooltipHovered, setTooltipHovered] = useState(false);
  const [submenuTitleHovered, setSubmenuTitleHovered] = useState(false);
  const { pathname } = useLocation();
  const { t, i18n } = useTranslation();

  useEffect(() => {
    new MetisMenu("#side-menu");
  }, []);

  // Check initial collapsed state and set up a mutation observer to watch for changes
  useEffect(() => {
    // Check if body has vertical-collpsed class
    const isBodyCollapsed =
      document.body.classList.contains("vertical-collpsed");
    setIsCollapsed(isBodyCollapsed);

    // Set up mutation observer to watch for changes to the body classList
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          const newIsCollapsed =
            document.body.classList.contains("vertical-collpsed");
          setIsCollapsed(newIsCollapsed);
        }
      });
    });

    observer.observe(document.body, { attributes: true });

    // Clean up observer on component unmount
    return () => {
      observer.disconnect();
    };
  }, []);

  // Toggle sidebar collapse state with smooth animation
  const toggleSidebar = () => {
    // Get references to the sidebar and main content elements
    const sidebar = document.querySelector(".vertical-menu");
    const mainContent = document.querySelector(".main-content");

    // Update state for immediate UI feedback
    setIsCollapsed(!isCollapsed);

    if (!isCollapsed) {
      // Add animation classes before collapsing
      sidebar.classList.add("sidebar-collapsing");
      mainContent.classList.add("content-collapsing");

      // Collapse sidebar with transition
      document.body.classList.add("vertical-collpsed");

      // Wait for CSS transition to complete before changing Redux state and removing animation classes
      setTimeout(() => {
        props.changeSidebarType("icon");
        sidebar.classList.remove("sidebar-collapsing");
        mainContent.classList.remove("content-collapsing");
      }, 300);
    } else {
      // Add animation classes before expanding
      sidebar.classList.add("sidebar-expanding");
      mainContent.classList.add("content-expanding");

      // Expand sidebar with transition
      document.body.classList.remove("vertical-collpsed");

      // Wait for CSS transition to complete before changing Redux state and removing animation classes
      setTimeout(() => {
        props.changeSidebarType("default");
        sidebar.classList.remove("sidebar-expanding");
        mainContent.classList.remove("content-expanding");
      }, 300);
    }
  };

  const hasPermission = (permissionName) => {
    const userStore = localStorage.getItem("authUser");
    const parsedStore = userStore ? JSON.parse(userStore) : null;
    const permissions = parsedStore?.user?.roles?.[0]?.permissions || [];
    return permissions.some((perm) => perm.name === permissionName);
  };

  const hasActiveSubmenu = (item) => {
    if (!item.subMenu) return false;
    return item.subMenu.some(
      (subItem) =>
        hasPermission(subItem.requiredPermission) && subItem.link === pathname
    );
  };

  const handleMenuClick = (menuTitle) => {
    setActiveMenu(menuTitle);
  };

  // Handle mouse enter on menu item
  const handleMouseEnter = (item) => {
    if (isCollapsed) {
      setHoveredItem(item.title);
    }
  };

  // Handle mouse leave from menu item
  const handleMouseLeave = () => {
    if (isCollapsed) {
      setHoveredItem(null);
    }
  };

  // Custom styles for the floating submenu
  const floatingSubmenuStyle = {
    position: "absolute",
    left: "60px",
    backgroundColor: "var(--bs-sidebar-dark-bg, #252b3b)",
    boxShadow: "0 5px 15px rgba(0, 0, 0, 0.2)",
    borderRadius: "0.25rem",
    minWidth: "220px",
    zIndex: 1000,
    padding: "0.5rem 0",
    marginTop: "-60px",
    border: "1px solid rgba(255, 255, 255, 0.07)",
  };

  // Style for the floating submenu title
  const submenuTitleStyle = {
    padding: "10px 15px",
    fontWeight: "bold",
    borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
    marginBottom: "8px",
    color: submenuTitleHovered ? "white" : "#ccc",
    transition: "color 0.2s ease-in-out",
    cursor: "pointer",
  };

  // Style for the tooltip/title when hovering on collapsed menu items
  const tooltipStyle = {
    position: "absolute",
    left: "60px",
    backgroundColor: "var(--bs-sidebar-dark-bg, #252b3b)",
    color: tooltipHovered ? "white" : "#ccc",
    padding: "8px 16px",
    borderRadius: "4px",
    fontSize: "13px",
    fontWeight: "bold",
    whiteSpace: "nowrap",
    zIndex: 1001,
    boxShadow: "0 5px 15px rgba(0, 0, 0, 0.15)",
    marginTop: "-35px",
    border: "1px solid rgba(255, 255, 255, 0.07)",
    transition: "color 0.2s ease-in-out",
    cursor: "pointer",
  };

  const menuItems = [
    {
      title: t("common.dashboard"),
      icon: <MdOutlineDashboardCustomize size={18} />,
      link: "/dashboard",
      requiredPermission: "user.index",
    },
    {
      title: t("common.types"),
      icon: <MdFormatListNumberedRtl size={18} />,
      hasArrow: true,
      requiredPermissions: [
        "contract_type.index",
        "reason.index",
        "bill_type.index",
        "task_type.index",
        "product_type.index",
        "product_unit.index",
        "bond_type.index",
        "store_operation.index",
        "task_type.index",
        "car_log_type.index",
        "billReason.index",
      ],
      subMenu: [
        {
          title: t("types.contract_types.contract_type"),
          link: "/contract-type",
          requiredPermission: "contract_type.index",
        },
        {
          title: t("bonds.voucher_type"),
          link: "/bonds",
          requiredPermission: "bond_type.index",
        },
        {
          title: t("products.product_unit"),
          link: "/products-units",
          requiredPermission: "product_unit.index",
        },
        {
          title: t("products.product_group"),
          link: "/product-types",
          requiredPermission: "product_type.index",
        },
        {
          title: t("tasks.tasks_type"),
          link: "/tasks-types",
          requiredPermission: "task_type.index",
        },
        {
          title: t("types.bill.bill_types"),
          link: "/bill-types",
          requiredPermission: "bill_type.index",
        },
        {
          title: t("types.car_logs.car_logs_type"),
          link: "/car-logs-type",
          requiredPermission: "car_log_type.index",
        },
        {
          title: t("types.store_operation.store_operation"),
          link: "/store-operation",
          requiredPermission: "store_operation.index",
        },
        {
          title: t("bill_reasons.bill_reason"),
          link: "/bill-reasons",
          requiredPermission: "billReason.index",
        },
        {
          title: t("types.reason.reasons"),
          link: "/reasons",
          requiredPermission: "reason.index",
        },
        {
          title: t("types.reason.task_reason"),
          link: "/tas-reasons",
          requiredPermission: "reason.index",
        },
      ],
    },
    {
      title: t("common.cars"),
      icon: <FaCarSide size={18} />,
      hasArrow: true,
      requiredPermissions: ["car_log.index", "car.index"],
      subMenu: [
        {
          title: t("common.cars"),
          link: "/cars",
          requiredPermission: "car.index",
        },
        {
          title: t("cars.car_logs"),
          link: "/car_logs",
          requiredPermission: "car_log.index",
        },
      ],
    },
    {
      title: t("common.users_and_ole"),
      icon: <FaRegUser size={18} />,
      hasArrow: true,
      requiredPermissions: ["user.index", "role.index"],
      subMenu: [
        {
          title: t("common.roles_permissions"),
          link: "/roles",
          requiredPermission: "role.index",
        },
        {
          title: t("common.users"),
          link: "/users",
          requiredPermission: "user.index",
        },
      ],
    },
    {
      title: t("clients.clients"),
      icon: <FaUserGear size={18} />,
      hasArrow: true,
      requiredPermissions: ["client.index", "client_group.index"],
      subMenu: [
        {
          title: t("clients.clients"),
          link: "/clients",
          requiredPermission: "client.index",
        },
        {
          title: t("clients.client_group"),
          link: "/clients-groups",
          requiredPermission: "client_group.index",
        },
      ],
    },
    {
      title: t("common.store"),
      icon: <FaStoreAlt size={18} />,
      hasArrow: true,
      requiredPermissions: [
        "store.get_requests",
        "store.store_transactions.index",
      ],
      subMenu: [
        {
          title: t("store-request.store_request"),
          link: "/store-request",
          requiredPermission: "store.get_requests",
        },
        {
          title: t("store_transactions.store_transactions"),
          link: "/store-transaction",
          requiredPermission: "store.store_transactions.index",
        },
      ],
    },
    {
      title: t("common.delegate"),
      icon: <FaPersonChalkboard size={18} />,
      hasArrow: true,
      requiredPermissions: ["delegate.index", "delegate_report.index"],
      subMenu: [
        {
          title: t("common.delegate"),
          link: "/delegate",
          requiredPermission: "delegate.index",
        },
        {
          title: t("common.delegate_reports"),
          link: "/delegate-reports",
          requiredPermission: "delegate.index",
        },
      ],
    },
    // {
    //   title: t("reports.reports"),
    //   icon: "ri-information-fill",
    //   hasArrow: true,
    //   requiredPermissions: ["car_log.index", "car.index"],
    //   subMenu: [
    //     {
    //       title: t("reports.clients_info"),
    //       link: "/client-info",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.sales"),
    //       link: "/sales",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.bonds"),
    //       link: "/bonds-report",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.total_report"),
    //       link: "/total-report",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.client_visit"),
    //       link: "/client-visit",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.car_expenses"),
    //       link: "/car_expenses",
    //       requiredPermission: "car.index",
    //     },
    //     {
    //       title: t("reports.contract_report"),
    //       link: "/contract-report",
    //       requiredPermission: "car.index",
    //     },
    //   ],
    // },
    {
      title: t("products.products"),
      icon: <RiProductHuntFill size={18} />,
      link: "/products",
      requiredPermission: "product.index",
    },
    {
      title: t("common.tasks"),
      icon: <LiaTasksSolid size={18} />,
      link: "/tasks",
      requiredPermission: "task.index",
    },
    {
      title: t("bills.bills"),
      icon: <IoNewspaperOutline size={18} />,
      link: "/bills",
      requiredPermissions: ["bill.index", "follow_up_log.index"],
    },
    {
      title: t("contracts.contracts"),
      icon: <FaFileContract size={18} />,
      link: "/contract",
      requiredPermission: "contract.index",
    },
    {
      title: t("bonds.Vouchers"),
      icon: <MdOutbond size={18} />,
      link: "/bonds-list",
      requiredPermission: "bond.index",
    },
    {
      title: t("offer.quotation"),
      icon: <MdLocalOffer size={18} />,
      link: "/offer-price",
      requiredPermission: "offer.index",
    },
    {
      title: t("visits.visit"),
      icon: <MdOutlineTravelExplore size={18} />,
      link: "/visits",
      requiredPermission: "visit.index",
    },
  ];

  // Update active menu and open state when pathname changes
  useEffect(() => {
    // Check if any submenu item matches the current pathname and open its parent menu
    menuItems.forEach((item) => {
      if (
        item.subMenu &&
        item.subMenu.some(
          (subItem) =>
            hasPermission(subItem.requiredPermission) &&
            subItem.link === pathname
        )
      ) {
        setIsOpen(item.title);
        setActiveMenu(item.title);
      } else if (item.link === pathname) {
        setActiveMenu(item.title);
      }
    });
  }, [pathname]);

  return (
    <React.Fragment>
      <div id="sidebar-menu">
        <ul className="metismenu list-unstyled" id="side-menu">
          {menuItems.map((item, index) => {
            const hasAccess = Array.isArray(item.requiredPermissions)
              ? item.requiredPermissions.some(hasPermission)
              : hasPermission(item.requiredPermission);

            if (!hasAccess) return null;

            return (
              <li
                key={index}
                className={
                  (activeMenu === item.title && item.hasArrow) ||
                  hasActiveSubmenu(item)
                    ? "mm-active"
                    : ""
                }
                style={{
                  background:
                    (activeMenu === item.title && !item.hasArrow) ||
                    (item.link === pathname && !item.hasArrow)
                      ? "#353f59"
                      : "",
                  position: "relative",
                  transition: "background 0.2s ease-in-out",
                }}
                onMouseEnter={() => handleMouseEnter(item)}
                onMouseLeave={handleMouseLeave}
              >
                <Link
                  to={item.hasArrow ? null : item.link}
                  onClick={(e) => {
                    if (item.hasArrow) {
                      e.preventDefault();
                      handleMenuClick(item.title);
                      setIsOpen(isOpen === item.title ? null : item.title);
                    } else {
                      handleMenuClick(item.title);
                    }
                  }}
                  style={{
                    background: item.link === pathname ? "#353f59" : "",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: isCollapsed ? "center" : "space-between",
                    padding: isCollapsed ? "15px 0" : undefined,
                    paddingLeft:
                      item.link === pathname && !isCollapsed
                        ? "calc(1.2rem - 3px)"
                        : undefined,
                  }}
                  className={`waves-effect ${
                    item.link === pathname ? "mm-active" : ""
                  }`}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: isCollapsed ? "center" : "space-between",
                      alignItems: "center",
                      width: "100%",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {/* <i
                        className={item.icon}
                        style={{
                          fontSize: isCollapsed ? "1.25rem" : "1.1rem",
                          marginRight: isCollapsed ? "0" : "0.5rem",
                          opacity: "0.9",
                        }}
                      ></i> */}
                      {item.icon}
                      {!isCollapsed && (
                        <span className="ms-1">{item.title}</span>
                      )}
                    </div>
                    {item.hasArrow &&
                      !isCollapsed &&
                      // <i
                      //   className={
                      //     isOpen === item.title
                      //       ? "ri-arrow-drop-up-line"
                      //       : "ri-arrow-drop-down-line"
                      //   }
                      // ></i>
                      (isOpen === item.title ? (
                        <MdKeyboardArrowUp />
                      ) : (
                        <MdKeyboardArrowDown />
                      ))}
                  </div>
                </Link>

                {/* Tooltip when collapsed for main menu items without submenu */}
                {isCollapsed && hoveredItem === item.title && !item.subMenu && (
                  <div
                    className="menu-tooltip"
                    style={tooltipStyle}
                    onMouseEnter={() => setTooltipHovered(true)}
                    onMouseLeave={() => setTooltipHovered(false)}
                  >
                    {item.title}
                  </div>
                )}

                {item.subMenu && !isCollapsed && (
                  <Collapse
                    isOpen={isOpen === item.title || hasActiveSubmenu(item)}
                    style={{ paddingInline: "1rem" }}
                  >
                    {item.subMenu.map((subItem, subIndex) => {
                      if (!hasPermission(subItem.requiredPermission))
                        return null;
                      return (
                        <li key={subIndex}>
                          <Link
                            style={{
                              background:
                                subItem.link === pathname ? "#3b4357" : "",
                              fontSize: i18n.language === "eng" ? 10 : 12,
                              fontWeight: "bold",
                              borderRadius: "4px",
                              transition: "background 0.2s ease-in-out",
                              padding: "8px 10px",
                              marginBottom: "2px",
                            }}
                            to={subItem.link}
                            onClick={() => handleMenuClick(subItem.title)}
                          >
                            {subItem.title}
                          </Link>
                        </li>
                      );
                    })}
                  </Collapse>
                )}

                {/* Floating submenu when sidebar is collapsed */}
                {isCollapsed && item.subMenu && hoveredItem === item.title && (
                  <div
                    className="floating-submenu show"
                    style={floatingSubmenuStyle}
                  >
                    <div
                      style={submenuTitleStyle}
                      onMouseEnter={() => setSubmenuTitleHovered(true)}
                      onMouseLeave={() => setSubmenuTitleHovered(false)}
                    >
                      {item.title}
                    </div>
                    {item.subMenu.map((subItem, subIndex) => {
                      if (!hasPermission(subItem.requiredPermission))
                        return null;
                      return (
                        <div key={subIndex} style={{ padding: "0.25rem 1rem" }}>
                          <Link
                            style={{
                              display: "block",
                              padding: "8px 12px",
                              color:
                                subItem.link === pathname ? "white" : "#ccc",
                              borderRadius: "4px",
                              background:
                                subItem.link === pathname ? "#3b4357" : "",
                              fontSize: i18n.language === "eng" ? 12 : 15,
                              fontWeight: "bold",
                              transition:
                                "background 0.2s ease-in-out, color 0.2s ease-in-out",
                            }}
                            className="hoverable-link"
                            to={subItem.link}
                            onClick={() => handleMenuClick(subItem.title)}
                          >
                            {subItem.title}
                          </Link>
                        </div>
                      );
                    })}
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      </div>
    </React.Fragment>
  );
};

const mapStateToProps = (state) => ({
  permissions: state?.auth?.user?.roles?.[0]?.permissions || [],
  ...state.Layout,
});

export default connect(mapStateToProps, {
  changeLayout,
  changeSidebarTheme,
  changeSidebarType,
  changeLayoutWidth,
  changePreloader,
})(withTranslation()(SidebarContent));
