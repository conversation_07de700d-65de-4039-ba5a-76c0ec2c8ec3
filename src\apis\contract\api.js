import ApiInstance from "../../constant/api-instance";
import { CONTRACT_ROUTES } from "./route";

const getAll = async ({ limit, page, client, searchParams }) => {
  const { data } = await ApiInstance.get(CONTRACT_ROUTES.CONTRACT, {
    params: {
      limit,
      page,
      client: client,
      ...searchParams,
    },
  });
  return data;
};

const getAllActiveContract = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CONTRACT_ROUTES.CONTRACT_CLIENTS}/${id}/active-contracts`,
    {}
  );
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CONTRACT_ROUTES.CONTRACT}/${id}`);
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${CONTRACT_ROUTES.CONTRACT}`,
    payload
  );
  return data;
};

const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(
    `${CONTRACT_ROUTES.CONTRACT}/update/${id}`,
    payload
  );
  return data;
};

const active = async ({ id }) => {
  const { data } = await ApiInstance.post(
    `${CONTRACT_ROUTES.ACTIVE}/${id}`,
    {}
  );
  return data;
};

const decline = async ({ id }) => {
  const { data } = await ApiInstance.post(
    `${CONTRACT_ROUTES.DECLINED}/${id}`,
    {}
  );
  return data;
};

const deleteForDelete = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${CONTRACT_ROUTES.CONTRACT}/${id}`
  );
  return data;
};

export const contractAPis = {
  getAllActiveContract,
  getAll,
  getOne,
  add,
  update,
  active,
  decline,
  deleteForDelete,
};
