import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState, useEffect } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
// import { delegateAPis } from "../../apis/delegate/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { DelegateReportStatus } from "../../constant/constants";
import { handleBackendErrors } from "../../helpers/api_helper";
import { delegateReportsQueries } from "../../apis/delegate-reports/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { delegateQueries } from "../../apis/delegate/query";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";
import { delegateReportsAPis } from "../../apis/delegate-reports/api";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";

const DelegateReports = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { pathname } = location;
  const queryParams = new URLSearchParams(location.search);

  const [searchParams, setSearchParams] = useState({});
  const [selectedId, setSelectedId] = useState(0);
  const [pagination, setPagination] = useState(1);

  // Parse URL parameters on component mount
  const { params: initialParams, initialFormValues } = parseUrlParams();

  const { control, reset, register, handleSubmit, watch, setValue } = useForm({
    defaultValues: initialFormValues,
  });

  const { data, isLoading, refetch } = delegateReportsQueries.useGetAll({
    searchParams,
  });

  const { data: delegate } = delegateQueries.useGetAll({ status: 1 });

  const DelegateOptions = useSetSelectOptions({
    data: delegate?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  // Initialize search parameters from URL on component mount
  useEffect(() => {
    setSearchParams(initialParams);
  }, []);

  // Status options for filtering
  const statusOptions = [
    {
      label: t("common.received"),
      value: 1,
    },
    {
      label: t("common.not_received"),
      value: 0,
    },
  ];

  const handleReset = () => {
    reset({
      delegate_id: null,
      total_higher: "",
      total_lower: "",
      search: "",
    });
    setSearchParams({});
    // Update URL to clear filters
    navigate(pathname, { replace: true });
    setPagination(1);
    refetch();
  };

  // Function to update URL with search parameters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Delegate ID filter
    if (params["filter[delegate_id]"]) {
      newUrl.set("delegate_id", params["filter[delegate_id]"]);
    }

    // Total filters
    if (params["filter[total_higher]"]) {
      newUrl.set("total_higher", params["filter[total_higher]"]);
    }

    if (params["filter[total_lower]"]) {
      newUrl.set("total_lower", params["filter[total_lower]"]);
    }

    // Status filter
    if (params["filter[status]"] !== undefined) {
      newUrl.set("status", params["filter[status]"]);
    }

    // General search term
    if (params["filter[search]"]) {
      newUrl.set("search", params["filter[search]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  // Function to parse URL parameters
  function parseUrlParams() {
    try {
      const params = {};
      const initialFormValues = {
        delegate_id: null,
        total_higher: "",
        total_lower: "",
        status: null,
        search: "",
      };

      // Parse delegate_id
      const delegateId = queryParams.get("delegate_id");
      if (delegateId && delegate?.result) {
        params["filter[delegate_id]"] = delegateId;
        const delegateOption = delegate.result.find(
          (d) => d.id.toString() === delegateId
        );
        if (delegateOption) {
          initialFormValues.delegate_id = {
            label: delegateOption.full_name,
            value: delegateOption.id,
          };
        }
      }

      // Parse total ranges
      const totalHigher = queryParams.get("total_higher");
      if (totalHigher) {
        params["filter[total_higher]"] = totalHigher;
        initialFormValues.total_higher = totalHigher;
      }

      const totalLower = queryParams.get("total_lower");
      if (totalLower) {
        params["filter[total_lower]"] = totalLower;
        initialFormValues.total_lower = totalLower;
      }

      // Parse status
      const status = queryParams.get("status");
      if (status !== null) {
        params["filter[status]"] = status;
        initialFormValues.status = statusOptions.find(
          (option) => option.value?.toString() === status
        );
      }

      // Parse search term
      const search = queryParams.get("search");
      if (search) {
        params["filter[search]"] = search;
        initialFormValues.search = search;
      }

      return { params, initialFormValues };
    } catch (error) {
      console.error("Error parsing URL parameters:", error);
      return {
        params: {},
        initialFormValues: {
          delegate_id: null,
          total_higher: "",
          total_lower: "",
          status: null,
          search: "",
        },
      };
    }
  }

  const handleSearch = handleSubmit((data) => {
    // Convert form data to search parameters with filter[fieldname] structure
    const params = {};

    // Delegate filter
    if (data.delegate_id?.value) {
      params["filter[delegate_id]"] = data.delegate_id.value;
    }

    // Total range filters
    if (data.total_higher) {
      params["filter[total_higher]"] = data.total_higher;
    }

    if (data.total_lower) {
      params["filter[total_lower]"] = data.total_lower;
    }

    // Status filter
    if (data.status?.value !== undefined && data.status?.value !== null) {
      params["filter[status]"] = data.status.value;
    }

    // General search
    if (data.search) {
      params["filter[search]"] = data.search;
    }

    // Update search params and trigger refetch
    setSearchParams(params);
    // Update URL with filters
    updateUrlWithFilters(params);
    setPagination(1); // Reset to first page when searching
    refetch();
  });

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const [isDeleting, setIsDeleting] = useState(false);
  const [openModel, setOpenModel] = useState(false);

  const handelCLoseModal = () => {
    setOpenModel(false);
    setSelectedId(0);
  };

  const ActiveUser = async (id, type) => {
    try {
      setIsDeleting(type);
      const response = await delegateReportsAPis.add({
        id: selectedId,
        payload: { status: id },
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting("");
    } catch (error) {
      setIsDeleting("");
      handleBackendErrors({ error });
      console.log("error", error);
    }
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate_name"),
      accessor: "delegate_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.delievered_date"),
      accessor: "delievered_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.recieved_date"),
      accessor: "recieved_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      accessor: "status",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.summary"),
      accessor: "summary",
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            {cellProps.statusNumber === 3 && (
              <Link
                className="text-primary"
                onClick={() => {
                  setOpenModel(true);
                  setSelectedId(cellProps.id);
                }}
              >
                <i className="ri-file-paper-2-line font-size-18"></i>
              </Link>
            )}
            <Link to={`/show-delegate-reports/?id=${cellProps.id}`}>
              <i className=" ri-information-fill font-size-16"></i>
            </Link>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data?.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (pagination - 1) * 10 + index + 1, // 10 is your page size
              delievered_date: item.delievered_date || "---",
              recieved_date: item.recieved_date || "---",
              delegate_name: item.delegate?.full_name || "---",
              status: DelegateReportStatus(t)[item.status] || "---",
              statusNumber: item.status,
              summary:
                (item.summary?.cash ??
                  item.summary?.cheque ??
                  item.summary?.visa) ||
                "---",
            }))
            .reverse()
        : [],
    [data?.result, t]
  );

  const SearchData = [
    {
      id: 0,
      label: t("common.delegate_name"),
      type: "select",
      name: "delegate_id",
      options: DelegateOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
  ];

  const inputsArray = [
    {
      id: 0,
      name: "total_higher",
      type: "number",
      label: t("reports.total_lower"),
      cols: 2,
    },
    {
      id: 1,
      name: "total_lower",
      type: "number",
      label: t("reports.total_higher"),
      cols: 2,
    },
  ];

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("common.delegate_reports")}
          breadcrumbItems={[]}
        />

        <Card style={{ maxHeight: "90%", height: "90%", overflowY: "auto" }}>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              customComponent={
                <div className="d-grid gap-2 w-100">
                  <SearchCard
                    SearchData={SearchData}
                    control={control}
                    hadelReset={handleReset}
                    inputsArray={inputsArray}
                    register={register}
                    handelSearch={handleSearch}
                    watch={watch}
                    setValue={setValue}
                  />
                </div>
              }
              isAddOptions={false}
              isLoading={isLoading}
              hidePagination
              addTitle={t("common.add") + " " + t("common.delegate")}
              iscustomPageSize={true}
              isBordered={true}
              customPageSize={10}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          </CardBody>
        </Card>
        {openModel && (
          <Modal isOpen={openModel} backdrop="static">
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <ModalFooter>
                <Button
                  type="button"
                  disabled={isDeleting === "no"}
                  color="light"
                  className="btn-sm"
                  onClick={() => ActiveUser(2, "no")}
                >
                  {isDeleting === "no" ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.reject")
                  )}
                </Button>
                <Button
                  disabled={isDeleting === "yes"}
                  type="button"
                  color="primary"
                  className="btn-sm"
                  onClick={() => ActiveUser(1, "yes")}
                >
                  {isDeleting === "yes" ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.accept")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
      </Container>
    </div>
  );
};
export default DelegateReports;
