import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalBody,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { clientsGroupsAPis } from "../../apis/client-group/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { clientsGroupsQueries } from "../../apis/client-group/query";
import { handleBackendErrors, hasPermission } from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const ClientsGroups = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);

  const { data, isLoading, refetch } = clientsGroupsQueries.useGetAll({
    limit: 10,
    page: page,
  });
  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const navigate = useNavigate();
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const handelAddBonds = () => {
    navigate("/action-clients-groups");
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await clientsGroupsAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await clientsGroupsAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("clients.client_group"),
      link: pathname,
    },
    // { title: "list", link: pathname },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("client_group.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("client_group.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.client_group"),
      accessor: "group_title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.number_of_clients"),
      accessor: "clients",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              // defaultChecked={cellProps.status === 1 ? true : false}
              // onClick={() => handelToggleStatus({ cellProps })}
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
            />
          </div>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"client_group.update"}>
              <Link
                to={
                  cellProps.is_default !== 1 &&
                  `/action-clients-groups?id=${cellProps.id}`
                }
                className="text-primary"
                onClick={() => {}}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"client_group.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"client_group.show"}>
              <Link
                to={`/action-clients-groups?id=${cellProps.id}?Show=true`}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              group_title: item.group_title || "----",
              clients: item?.client_number || 0,
              status: item.status,
            }))
            .reverse()
        : [],
    [data?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await clientsGroupsAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("clients.client_group")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          canPermission={"client_group.store"}
          addTitle={t("common.add") + " " + t("clients.group")}
          handleOrderClicks={handelAddBonds}
        />
        <Card style={{ maxHeight: "90%", height: "90%", overflowY: "auto" }}>
          <CardBody>
            {/* {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              // isGlobalFilter={true}
              iscustomPageSize={true}
              isBordered={true}
              isLoading={isLoading}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={data?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
            />
            {/* )} */}
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("clients.group")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.close")}
              </Button>
              <Button
                className="btn-sm"
                onClick={DeleteFun}
                type="button"
                color="danger"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                disabled={isDeleting}
                type="button"
                className="btn-sm"
                color="primary"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
    </div>
  );
};
export default ClientsGroups;
