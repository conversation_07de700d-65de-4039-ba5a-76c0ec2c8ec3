import ApiInstance from "../../constant/api-instance";
import { AUT_ROUTES } from "./route";

const logIn = async ({ email, password }) => {
  const { data } = await ApiInstance.post(AUT_ROUTES.LOGIN, {
    email,
    password,
  });

  return data;
};

const updateProfile = async (formData) => {
  const { data } = await ApiInstance.post(
    `${AUT_ROUTES.UPDATE_PROFILE}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

const getUser = async () => {
  const { data } = await ApiInstance.get(`${AUT_ROUTES.GET_PROFILE}`);
  return data;
};

const deleteImage = async ({ ids }) => {
  const { data } = await ApiInstance.post(`${AUT_ROUTES.DELETE_IMAGE}`, {
    images: ids,
  });

  return data;
};

const logOut = async () => {
  await ApiInstance.post(AUT_ROUTES.LOGOUT, {});
};

const updatePassword = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${AUT_ROUTES.UPDATE_PASSWORD}`,
    payload
  );
  return data;
};

export const AuthAPis = {
  logIn,
  logOut,
  deleteImage,
  updateProfile,
  updatePassword,
  getUser,
};
