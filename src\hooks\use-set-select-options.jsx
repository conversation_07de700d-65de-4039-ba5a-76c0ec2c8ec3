import { useEffect, useState } from "react";

const useSetSelectOptions = ({ data, getOption, otherDepend }) => {
  const [optionGroup, setOptionGroup] = useState([]);

  useEffect(() => {
    if (data?.length > 0) {
      if (typeof getOption !== "function") {
        return;
      }
      setOptionGroup(data.map((item) => getOption(item)));
    }
  }, [data, otherDepend]);

  return optionGroup;
};

export default useSetSelectOptions;
