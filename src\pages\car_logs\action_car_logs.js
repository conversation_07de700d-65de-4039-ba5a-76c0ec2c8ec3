import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Col, Container, Label, Row } from "reactstrap";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { carLogsQueries } from "../../apis/car-logs/query";
import { carLogsTypesQueries } from "../../apis/types/car-logs-types/query";
import { UsersQueries } from "../../apis/cars/query";
import { useNavigate } from "react-router-dom";
import { carLogsAPis } from "../../apis/car-logs/api";
import { delegateQueries } from "../../apis/delegate/query";
import { CarsLogsEnum } from "../../constant/constants";
import { handleBackendErrors } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import CustomTextArea from "../../components/Common/textArea";
const CarLogsActions = ({ selectId, isShow, handelCLose, refetch }) => {
  const { data, isLoading } = carLogsQueries.useGetOne({
    id: Number(selectId),
  });
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: carLogs_type } = carLogsTypesQueries.useGetAll({ status: 1 });
  const { data: Cars } = UsersQueries.useGetAllUsers({ status: 1 });

  const { t, i18n } = useTranslation();

  const [optionBondTypesGroup, setOptionBondTypesGroup] = useState([]); // Assuming you have a way to populate this
  const [optionCarLogsType, setOptionCarLogsType] = useState([]); // Assuming you have a way to populate this
  const [optionCars, setOptionCars] = useState([]); // Assuming you have a way to populate this

  const navigate = useNavigate();

  const handelCancel = () => {
    handelCLose();
  };

  const schema = yup
    .object({
      km: yup
        .number()
        .min(1)
        .max(999999, t("cars.validations.km_max"))
        .required(t("common.field_required")),
      car_id: yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.number().required(),
        })
        .required(t("common.field_required")),
    })
    .required();

  const initialSTate = {
    fuel_liters: null,
    liter_price: null,
    total_price: null,
    km: null,
    notes: "",
    delegate_id: null,
    car_id: null,
    // car_log_type_id: optionCarLogsType[2]?.value,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    watch,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (delegates?.result?.length > 0) {
      setOptionBondTypesGroup(
        delegates.result.map((item) => ({
          label: item?.full_name,
          value: item.id,
        }))
      );
    }
  }, [delegates?.result]);

  useEffect(() => {
    if (carLogs_type?.result?.length > 0) {
      if (selectId) {
        setOptionCarLogsType(
          carLogs_type.result.map((item) => ({
            label: item?.title?.en,
            value: item.log_type,
          }))
        );
      } else {
        setOptionCarLogsType(
          carLogs_type.result
            .map((item) => ({
              label:
                i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
              value: item.log_type,
            }))
            .slice(2)
        );
      }
    }
  }, [carLogs_type?.result]);

  useEffect(() => {
    if (Cars?.result?.length > 0) {
      setOptionCars(
        Cars.result.map((item) => ({
          label: item?.name,
          value: item.id,
        }))
      );
    }
  }, [Cars?.result]);

  const formFields = [
    {
      type: "select",
      name: "car_log_type_id",
      label: t("cars.select_car_logs_type"),
      options: optionCarLogsType,
      disabled: Boolean(selectId) || isShow,
      col: 6,
    },
    {
      type: "select",
      name: "delegate_id",
      label: `${t("common.select")} ${t("common.delegate")}`,
      options: optionBondTypesGroup,
      disabled: [1, 2].includes(watch("car_log_type_id")?.value) || isShow,
      col: 6,
    },
    {
      type: "select",
      name: "car_id",
      label: `${t("common.select")} ${t("common.car")}`,
      options: optionCars,
      disabled: [1, 2].includes(watch("car_log_type_id")?.value) || isShow,
      col: 6,
      error: errors.car_id,
    },
    {
      type: "number",
      name: "fuel_liters",
      label: t("cars.FILL_FUEL"),
      show: watch("car_log_type_id")?.value === CarsLogsEnum.FILL_FUEL,
      col: 6,
      error: errors.fuel_liters,
    },
    {
      type: "number",
      name: "liter_price",
      label: t("cars.liter_price"),
      show: watch("car_log_type_id")?.value === CarsLogsEnum.FILL_FUEL,
      col: 6,
      error: errors.liter_price,
    },
    {
      type: "number",
      name: "total_price",
      label: t("cars.total_price"),
      show: [CarsLogsEnum.FILL_FUEL, CarsLogsEnum.FAST_REPAIR].includes(
        watch("car_log_type_id")?.value
      ),
      col: 6,
      error: errors.total_price,
    },
    {
      type: "number",
      name: "km",
      label: t("common.km"),
      col: 6,
      error: errors.km,
    },
    {
      type: "textarea",
      name: "notes",
      label: t("common.note"),
      col: 12,
    },
  ];

  useEffect(() => {
    if (watch("liter_price")) {
      setValue("total_price", watch("liter_price") * watch("fuel_liters"));
    }
  }, [watch("liter_price"), watch("fuel_liters")]);

  useEffect(() => {
    if (optionCarLogsType?.length > 0 && !selectId) {
      setValue("car_log_type_id", {
        label: optionCarLogsType[0].label,
        value: optionCarLogsType[0].value,
      });
    }
  }, [optionCarLogsType, selectId]);

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Populate form with role data when loaded
      reset({
        km: data?.result?.km,
        car_id: { label: data.result.car.name, value: data.result.car.id },
        car_log_type_id: {
          label:
            i18n.language === "eng"
              ? data.result.car_log_type.title.en
              : data.result.car_log_type.title.ar,
          value: data.result.car_log_type.id,
        },
        delegate_id: {
          label: data?.result?.delegate?.full_name,
          value: data?.result?.delegate?.id,
        },
        liter_price: data.result.liter_price,
        notes: data.result.notes,
        total_price: data.result.total_price,
        fuel_liters: data.result.fuel_liters,
      });
    }
  }, [selectId, isLoading, data?.result]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      const selectedDataType = carLogs_type?.result?.find(
        (item) => item.id === Number(data.car_log_type_id.value)
      );
      const dataToSend = {
        ...data,
        fuel_liters: Number(`${data.fuel_liters}.0`),
        liter_price: Number(`${data.liter_price}.0`),
        total_price: Number(`${data.total_price}.0`),
        km: Number(data.km),
        notes: data.notes,
        car_id: data.car_id.value,
        car_log_type_id: data.car_log_type_id.value,
        delegate_id: data?.delegate_id?.value,
        log_type: selectedDataType.log_type,
      };
      const response = await carLogsAPis.updateUser({
        payload: dataToSend,
        id: Number(selectId),
      });
      toastr.success(response.message);
      navigate("/car_logs");
      reset();
      refetch();
      handelCLose();
    } catch (error) {
      console.log("error", error);
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const selectedDataType = carLogs_type?.result?.find(
        (item) => item.id === Number(data.car_log_type_id.value)
      );
      const dataToSend = {
        ...data,
        fuel_liters: Number(`${data.fuel_liters}.0`),
        liter_price: Number(`${data.liter_price}.0`),
        total_price: Number(`${data.total_price}.0`),
        km: Number(data.km),
        notes: data.notes,
        car_id: data.car_id.value,
        car_log_type_id: data.car_log_type_id.value,
        delegate_id: data?.delegate_id?.value,
        log_type: selectedDataType.log_type,
      };
      const response = await carLogsAPis.addUser({ payload: dataToSend });
      toastr.success(response.message);
      navigate("/car_logs");
      reset(); // Reset form after successful submission
      handelCLose();
      refetch();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  return (
    <>
      <Container fluid>
        <Row>
          <form
            onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
          >
            {isLoading ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <Row className="g-2">
                {formFields.map((field) => {
                  if (field.show === false) return null;

                  return (
                    <Col key={field.name} xs={field.col || 6}>
                      {field.type === "select" ? (
                        // <div className="mb-4">
                        <CustomSelect
                          name={field.name}
                          control={control}
                          label={field.label}
                          options={field.options}
                          isDisabled={field.disabled}
                          error={field.error}
                        />
                      ) : // </div>
                      field.type === "textarea" ? (
                        <div className="mb-2">
                          {/* <Label className="form-label" htmlFor={field.name}>
                            {field.label}
                          </Label> */}
                          <CustomTextArea
                            name={field.name}
                            control={control}
                            isShow={isShow}
                            placeholder={field.label}
                            rows={4}
                            error={errors[field.name]}
                          />
                        </div>
                      ) : (
                        <CustomInput
                          name={field.name}
                          control={control}
                          label={field.label}
                          type={field.type}
                          isDisabled={field.disabled || isShow}
                          error={field.error}
                        />
                      )}
                    </Col>
                  );
                })}
              </Row>
            )}

            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: 8,
                justifyContent: "end",
              }}
            >
              <Button
                type="button"
                color="light"
                onClick={handelCancel}
                className="btn-sm "
                style={{ height: "32px", width: "54px" }}
              >
                {t("common.close")}
              </Button>
              {/* <Can permission={selectId ? "client.update" : "client.store"}> */}
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
              )}
              {/* </Can> */}
            </div>
          </form>
        </Row>
      </Container>
    </>
  );
};
export default CarLogsActions;
