{"name": "nazox", "version": "2.0.0", "private": true, "dependencies": {"@fullcalendar/bootstrap": "^6.1.8", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@react-google-maps/api": "^2.19.3", "@tanstack/react-query": "^5.56.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^13.1.9", "@toast-ui/react-chart": "^4.6.1", "@vtaits/react-color-picker": "^0.1.1", "apexcharts": "^3.41.0", "availity-reactstrap-validation": "^2.6.1", "axios": "^1.7.7", "axios-mock-adapter": "^1.18.1", "bootstrap": "5.3.0", "canvas": "^2.11.2", "chart.js": "^4.3.0", "chartist": "^1.3.0", "dotenv": "^16.3.1", "draft-js": "^0.11.7", "echarts": "^5.4.2", "echarts-for-react": "^2.0.15-beta.1", "firebase": "^7.11.0", "google-maps-react": "^2.0.6", "i18next": "^23.2.11", "i18next-browser-languagedetector": "^7.1.0", "leaflet": "^1.9.4", "lodash.clonedeep": "^4.5.0", "mdbreact": "^5.2.0", "metismenujs": "^1.4.0", "mkdirp": "^3.0.1", "moment": "2.29.4", "moment-timezone": "^0.5.28", "node-sass": "^9.0.0", "nouislider-react": "^3.4.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-bootstrap-editable": "^0.6.1", "react-bootstrap-table-next": "^4.0.3", "react-bootstrap-table2-editor": "^1.4.0", "react-bootstrap-table2-paginator": "^2.1.2", "react-chartist": "^0.14.4", "react-chartjs-2": "^5.2.0", "react-circle-slider": "^1.6.2", "react-color": "^2.19.3", "react-countdown": "^2.3.5", "react-cropper": "^2.3.3", "react-data-table-component": "^7.5.3", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.14.4", "react-dropzone": "^14.2.3", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.2", "react-icons": "^5.5.0", "react-image-lightbox": "^5.1.1", "react-images-uploading": "^3.1.7", "react-input-mask": "^2.0.4", "react-jvectormap": "^0.0.16", "react-leaflet": "^2.8.0", "react-map-gl": "^7.1.7", "react-modal-video": "^1.2.7", "react-owl-carousel": "^2.3.3", "react-perfect-scrollbar": "^1.5.8", "react-rangeslider": "^2.2.0", "react-rating": "^2.0.5", "react-rating-tooltip": "^1.3.1", "react-redux": "^8.1.1", "react-router-dom": "^6.26.2", "react-script": "^2.0.5", "react-scripts": "5.0.1", "react-select": "^5.7.3", "react-sparklines": "^1.7.0", "react-spinners": "^0.14.1", "react-star-ratings": "^2.3.0", "react-switch": "^7.0.0", "react-table": "^7.8.0", "react-toastr": "^3.0.0", "react-uploader": "^3.43.0", "reactstrap": "^9.2.0", "redux": "^4.2.1", "redux-form": "^8.3.7", "redux-saga": "^1.2.3", "simplebar-react": "^2.3.3", "styled-components": "^6.0.4", "toastr": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts --max_old_space_size=8000 build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"moment": "2.24.0"}, "devDependencies": {"ajv": "^8.17.1"}}