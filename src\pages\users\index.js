import { withTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import {
  formatDate,
  handleBackendErrors,
  today,
  truncateText,
} from "../../helpers/api_helper";
import "./user.scss";
import { UsersQueries } from "../../apis/users/query";
import { USERSAPis } from "../../apis/users/api";
import { RolesQueries } from "../../apis/roles/query";
import { Link } from "react-router-dom";
import { Can } from "../../components/permissions-way/can";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import SearchCard from "../../components/Reports/search-card";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { FaPenToSquare } from "react-icons/fa6";
import { MdDeleteSweep } from "react-icons/md";
import { FaEyeSlash, FaInfoCircle, FaRegEye } from "react-icons/fa";

const Users = ({ t }) => {
  const [pageSize, setPageSize] = useState(1);
  const [searchParams, setSearchParams] = useState({});
  const {
    data: Users,
    isLoading: isLoadingUsers,
    refetch,
  } = UsersQueries.useGetAllUsers({ limit: 10, page: pageSize, searchParams });

  const [isShowPassword, setIsShowPassword] = useState(false);

  const togglePassword = () => {
    setIsShowPassword((prev) => !prev);
  };

  const { data: Roles } = RolesQueries.useGetAllRoles({
    limit: 10000,
    page: 1,
  });
  const [selectId, setSelectId] = useState(null);

  const { data: Role, isLoading: isLoadingRole } = UsersQueries.useGetUser({
    id: selectId,
  });

  const handleReset = () => {
    reset({
      name: "",
      residency_end_date_to: "",
      contract_end_date_to: "",
    });
    setSearchParams({});
  };

  const [openSidebar, setOPenSideBar] = useState(false);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [isShow, setIsShow] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setOpenStatsusModal(false);
    setSelectId(null);
    setIsShow(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelOpenSideBar = () => {
    setOPenSideBar(true);
  };

  const validationSchema = Yup.object().shape({
    password: selectId
      ? null
      : Yup.string().required(t("common.field_required")),

    confirm_password: selectId
      ? null
      : Yup.string()
          .oneOf([Yup.ref("password"), null], t("common.passwords_must_match"))
          .required(t("common.field_required")),

    full_name: Yup.string().required(t("common.field_required")),

    email: Yup.string()
      .nullable()
      .notRequired()
      .email(t("clients.validations.email"))
      .required(t("common.field_required")),

    roles: Yup.object()
      .shape({
        label: Yup.string().required(),
        value: Yup.number().required(),
      })
      .required(t("common.field_required")),
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    control,
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      full_name: "",
      email: "",
      password: "",
      confirm_password: "",
      roles: null,
      residency_end_date_to: formatDate(today),
      contract_end_date_to: formatDate(today),
      residency_type: null,
      name: "",
    },
    resolver: yupResolver(validationSchema),
  });

  const searchFields = watch([
    "contract_end_date_to",
    "residency_end_date_to",
    "name",
  ]);

  // Fixed useEffect to properly handle filter values
  const handelSearch = () => {
    // Create a reference to the current timeout
    const timeoutId = setTimeout(() => {
      const params = {};

      // Check if search fields have values and add them to params
      if (searchFields[0]) {
        params["filter[contract_end_date_to]"] = searchFields[0];
      }

      if (searchFields[1]) {
        params["filter[residency_end_date_to]"] = searchFields[1];
      }

      if (searchFields[2]) {
        params["filter[full_name]"] = searchFields[2];
      }

      // Only update if there's a change in filters
      const isEqual =
        Object.keys(params).length === Object.keys(searchParams).length &&
        Object.keys(params).every((key) => params[key] === searchParams[key]);

      if (!isEqual) {
        setSearchParams(params);
      }
    }, 500); // 500ms debounce delay

    // Cleanup timeout on component unmount or when dependencies change
    return () => clearTimeout(timeoutId);
  };

  const localStorageData = localStorage.getItem("authUser");
  const parseData = localStorageData && JSON.parse(localStorageData);
  const isAdmin = parseData?.is_admin === 1;

  const statusOptions = [
    { value: 1, label: t("common.company") },
    { value: 2, label: t("common.residence") },
  ];

  // Transform roles data for select component
  const rolesOptions = useMemo(() => {
    return (
      Roles?.result?.map((role) => ({
        value: role.id,
        label: role.title,
      })) || []
    );
  }, [Roles?.result]);

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingRole && Role?.result) {
      // Populate form with role data when loaded
      reset({
        email: Role.result.email,
        full_name: Role.result.full_name,
        password: Role.result.password,
        roles: {
          value: Role.result.roles[0]?.id,
          label: Role.result.roles[0]?.title,
        },
        confirm_password: Role.result.password,
        residence_expiry_date: Role.result.residency_end_date?.split("T")[0],
        contract_end_date: Role.result.contract_end_date?.split("T")[0],
        contract_start_date: Role.result.contract_start_date?.split("T")[0],
        residency_type: {
          value: Role.result.residency_type,
          label: statusOptions.find(
            (item) => item.value === Role.result?.residency_type
          )?.label,
        },
      });
    }
  }, [selectId, isLoadingRole, Role]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCloseSideBar = () => {
    setOPenSideBar(false);
    reset({
      email: "",
      full_name: "",
      password: "",
      roles: null,
      residency_end_date_to: formatDate(today),
      contract_end_date_to: formatDate(today),
      residency_type: null,
    });
    setSelectId(null);
    setIsDeleting(false);
    setIsShow(false);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };
  // Submit form
  const onSubmit = async (data) => {
    try {
      const response = await USERSAPis.addUser({
        email: data.email,
        full_name: data.full_name,
        password: data.password,
        role_id: [Number(data.roles.value)],
        residency_end_date: data.residence_expiry_date,
        contract_end_date: data.contract_end_date,
        contract_start_date: data.contract_start_date,
        residency_type: data?.residency_type?.value,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
      reset();
    } catch (error) {
      console.log("error", error);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const UpdateFun = async (data) => {
    try {
      const response = await USERSAPis.updateUser({
        payload: {
          email: data.email,
          full_name: data.full_name,
          role_id: [Number(data.roles.value)],
          confirm_password: data.confirm_password,
          residency_end_date: data.residence_expiry_date,
          contract_end_date: data.contract_end_date,
          contract_start_date: data.contract_start_date,
          residency_type: data?.residency_type?.value,
        },
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      reset();
      handelCloseSideBar();
    } catch (error) {
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await USERSAPis.deleteUser({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });

      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await USERSAPis.activeUser({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await USERSAPis.inActiveUser({
        id: id,
      });
      refetch();
      setIsDeleting(false);
      toastr.success(response.message);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    { title: t("common.users"), link: "/Users" },
    // { title: "US", link: "#" },
  ];

  const currentUserEmail = JSON.parse(localStorage?.getItem("authUser"))?.user
    ?.email;

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: t("common.image"),
    //   accessor: "image",
    //   disableFilters: true,
    //   filterable: false,
    // },
    {
      Header: t("common.full_name"),
      accessor: "fullName",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.email"),
      accessor: "email",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.roles"),
      accessor: "role",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Can permission={"user.activate" || "user.disactivate"}>
              <Input
                type="checkbox"
                disabled={currentUserEmail === cellProps.email}
                className="form-check-input cursor-pointer"
                checked={cellProps.status === 1}
                onClick={() => handelToggleStatus({ cellProps })}
              />
            </Can>
          </div>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          cellProps.is_default === 0 && (
            <div style={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Can permission={"user.update"}>
                <Link
                  to="#"
                  className="text-primary"
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenSideBar();
                      handelSelectId(cellProps.id);
                    }
                  }}
                >
                  {/* <i className=" mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              </Can>
              <Can permission={"user.destroy"}>
                <Link
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenModal();
                      handelSelectId(cellProps.id);
                    }
                  }}
                  to="#"
                  className="text-danger"
                >
                  {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                  <MdDeleteSweep size={18} />
                </Link>
              </Can>
              <Can permission={"user.show"}>
                <Link
                  onClick={() => {
                    if (cellProps.isDefault !== 1) {
                      handelOpenSideBar();
                      handelSelectId(cellProps.id);
                      setIsShow(true);
                    }
                  }}
                  to="#"
                  className="text-success"
                >
                  {/* <i className="ri-information-fill font-size-16"></i> */}
                  <FaInfoCircle size={14} />
                </Link>
              </Can>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      Users?.result?.length > 0
        ? Users.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (pageSize - 1) * 10 + index + 1, // 10 is your page size
              fullName: truncateText({
                text: item.full_name,
                maxLengthPercent: 0.3,
              }),
              email: truncateText({
                text: item.email,
                maxLengthPercent: 0.3,
              }),
              role: truncateText({
                text: item.roles[0]?.title,
                maxLengthPercent: 0.3,
              }),
              status: item.status,
              is_default: item.is_default,
              image: item.image || "---",
            }))
            .reverse()
        : [],
    [Users?.result, t]
  );

  const inputsArray = [
    {
      id: 1,
      name: "name",
      type: "text",
      label: t("common.name"),
    },
    {
      id: 2,
      name: "contract_end_date_to",
      type: "date",
      label: t("contracts.contract_end_date"),
    },
    {
      id: 3,
      name: "residency_end_date_to",
      type: "date",
      label: t("common.residence_expiry_date"),
    },
  ];

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("common.users")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions
          addTitle={t("common.add") + " " + t("common.user")}
          handleOrderClicks={handelOpenSideBar}
          canPermission="user.store"
        />
        <div>
          <Modal fade={false} isOpen={openSidebar} backdrop="static">
            <ModalHeader toggle={handelCloseSideBar}>
              {isShow
                ? t("common.show") + " " + t("common.user")
                : selectId > 0
                ? t("common.update") + " " + t("common.user")
                : t("common.add") + " " + t("common.user")}
            </ModalHeader>
            <ModalBody>
              <form
                onSubmit={
                  selectId ? handleSubmit(UpdateFun) : handleSubmit(onSubmit)
                }
              >
                {isLoadingRole ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row className="g-3">
                      <Col xs={6}>
                        <CustomInput
                          name="full_name"
                          control={control}
                          error={errors.full_name}
                          disabled={isShow}
                          placeholder={t("common.full_name")}
                          type="text"
                        />
                      </Col>

                      <Col xs={6}>
                        <CustomInput
                          name="email"
                          control={control}
                          error={errors.email}
                          disabled={isShow}
                          placeholder={t("common.email")}
                          type="text"
                        />
                      </Col>

                      {/* {!selectId && ( */}
                      <Col xs={6}>
                        <CustomInput
                          name="password"
                          control={control}
                          error={errors.password}
                          isDisabled={isAdmin || isShow}
                          placeholder={t("common.password")}
                          type={isShowPassword ? "text" : "password"}
                          endIcon={
                            <div
                              style={{
                                height: "100%",
                                width: 20,
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                cursor: "pointer",
                              }}
                              onClick={togglePassword}
                            >
                              {!isShowPassword ? (
                                <FaEyeSlash size={13} />
                              ) : (
                                <FaRegEye size={13} />
                              )}
                            </div>
                          }
                        />
                      </Col>
                      {/* )} */}

                      {/* {!selectId && ( */}
                      <Col xs={6}>
                        <CustomInput
                          name="confirm_password"
                          control={control}
                          error={errors.confirm_password}
                          isDisabled={isAdmin || isShow}
                          placeholder={t("common.confirm_password")}
                          type={isShowPassword ? "text" : "password"}
                          endIcon={
                            <div
                              style={{
                                height: "100%",
                                width: 20,
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                cursor: "pointer",
                              }}
                              onClick={togglePassword}
                            >
                              {/* <i className=" ri-eye-off-line"></i> */}
                              {!isShowPassword ? (
                                <FaEyeSlash size={13} />
                              ) : (
                                <FaRegEye size={13} />
                              )}
                            </div>
                          }
                        />
                      </Col>
                      {/* )} */}

                      <Col xs={6}>
                        <CustomInput
                          name="contract_start_date"
                          control={control}
                          error={errors.contract_start_date}
                          disabled={isShow}
                          placeholder={t("contracts.contract_start_date")}
                          type="date"
                        />
                      </Col>

                      <Col xs={6}>
                        <CustomInput
                          name="contract_end_date"
                          control={control}
                          error={errors.contract_end_date}
                          disabled={isShow}
                          placeholder={t("contracts.contract_end_date")}
                          type="date"
                        />
                      </Col>
                      <Col xs={6}>
                        <CustomSelect
                          name="residency_type"
                          control={control}
                          label={t("common.type_of_accommodation")}
                          options={statusOptions}
                          isMulti={false}
                          isDisabled={isShow}
                          error={errors.residency_type}
                          rules={{ required: t("common.field_required") }}
                        />
                      </Col>
                      <Col xs={6}>
                        <CustomInput
                          name="residence_expiry_date"
                          control={control}
                          error={errors.residence_expiry_date}
                          disabled={isShow}
                          placeholder={t("common.residence_expiry_date")}
                          type="date"
                        />
                      </Col>

                      <Col xs={12}>
                        <CustomSelect
                          name="roles"
                          control={control}
                          error={errors.roles}
                          options={rolesOptions}
                          label={t("common.roles")}
                          isDisabled={isShow}
                        />
                      </Col>
                    </Row>

                    <ModalFooter>
                      <div className="button-items">
                        <Button
                          onClick={handelCloseSideBar}
                          color="light"
                          className="waves-effect me-1 btn-sm"
                        >
                          {t("common.close")}
                        </Button>
                        {!isShow && (
                          <Button
                            color="primary"
                            className="waves-effect waves-light primary-button btn-sm"
                            type="submit"
                          >
                            {isSubmitting ? (
                              <ClipLoader color="white" size={15} />
                            ) : selectId > 0 ? (
                              t("common.update")
                            ) : (
                              t("common.add")
                            )}
                          </Button>
                        )}
                      </div>
                    </ModalFooter>
                  </>
                )}
              </form>
            </ModalBody>
          </Modal>
        </div>
        <Card>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              pageCount={Users?.meta?.last_page}
              currentPage={pageSize}
              isLoading={isLoadingUsers}
              pageSize={pageSize}
              setPage={setPageSize}
              customComponent={
                <SearchCard
                  SearchData={[]}
                  control={control}
                  hadelReset={handleReset}
                  inputsArray={inputsArray}
                  register={register}
                  watch={watch}
                  setValue={setValue}
                  handelSearch={handelSearch}
                  // onChange={(values) => {
                  //   console.log("Search values changed:", values);
                  // }}
                />
              }
            />
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("common.user")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                type="button"
                color="light"
                onClick={handelCLoseModal}
                className="btn-sm"
              >
                {t("common.close")}
              </Button>
              <Button
                onClick={DeleteFun}
                type="button"
                color="danger"
                className="btn-sm"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
        {openStatsusModal && selectId && (
          <Modal isOpen={openStatsusModal} backdrop="static">
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <ModalFooter>
                <Button
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                  className="btn-sm"
                >
                  {t("common.no")}
                </Button>
                <Button
                  onClick={() =>
                    statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                  }
                  disabled={isDeleting}
                  type="button"
                  color="primary"
                  className="btn-sm"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
      </Container>
    </div>
  );
};
export default withTranslation()(Users);
