import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Col, Row } from "reactstrap";
import { useTranslation } from "react-i18next";
import { handleBackendErrors } from "../../helpers/api_helper";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { sectionsAPis } from "../../apis/sections/api";
import CustomInput from "../../components/Common/Input";
import { sectionsQueries } from "../../apis/sections/query";

const SectionsActions = ({
  isShow,
  selectId,
  handelClose,
  refetch,
  templateId,
  cusomComponent,
  setShow,
}) => {
  const { t } = useTranslation();
  const taskId = selectId;
  const schema = yup
    .object({
      title: yup.string().required(t("common.required")),
    })
    .required();

  const initialSTate = {
    name: "",
    language: null,
    contract_type_id: null,
    is_active: 0,
    is_editable: 0,
    order: 0,
  };

  const {
    handleSubmit,
    reset,
    register,
    formState: { isSubmitting, errors },
    setError,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const { data, isLoading } = sectionsQueries.useGet({
    id: Number(taskId),
  });

  useEffect(() => {
    if (taskId && !isLoading && data?.result) {
      const item = data.result;
      reset({
        title: item?.title,
        is_active: item?.is_active || 0,
        is_editable: item?.is_editable || 0,
        order: Number(item.order),
      });
    }
  }, [selectId, isLoading, data]);

  useEffect(() => {
    if (!isLoading && selectId) {
      setShow(true);
    }
  }, [isLoading, selectId]);

  const UpdateFun = async (data) => {
    try {
      const dataForSend = {
        title: data.title,
        is_active: data.is_active ? 1 : 0,
        is_editable: data.is_editable ? 1 : 0,
        order: Number(data.order),
      };
      const response = await sectionsAPis.update({
        payload: dataForSend,
        id: selectId,
      });
      handelClose();
      toastr.success(response.message);
      refetch();
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const dataForSend = {
        title: data.title,
        is_active: data.is_active ? 1 : 0,
        is_editable: data.is_editable ? 1 : 0,
        order: Number(data.order),
      };
      const response = await sectionsAPis.add({
        payload: dataForSend,
        id: templateId,
      });
      toastr.success(response.message);
      refetch();
      reset();
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div>
      <h1 style={{ fontSize: 16 }} className="mb-4">
        {isShow
          ? t("common.show") + " " + t("common.section")
          : selectId
          ? t("common.update") + " " + t("common.section")
          : t("common.add") + " " + t("common.section")}
      </h1>
      {isLoading ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <Row className="g-3">
          <Col xs={12}>
            <CustomInput
              control={control}
              error={errors?.title?.message}
              isDisabled={isShow}
              placeholder={t("common.title")}
              name="title"
            />
          </Col>

          <Col xs={12} className="d-flex align-items-center gap-2">
            <CustomInput
              control={control}
              error={errors?.order?.message}
              isDisabled={isShow}
              placeholder={t("common.order")}
              name="order"
              type="number"
            />
            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="is_active"
                disabled={isShow}
                {...register("is_active")}
              />
              <label className="form-check-label" htmlFor="is_active">
                {t("common.is_active")}
              </label>
            </div>

            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="is_editable"
                disabled={isShow}
                {...register("is_editable")}
              />
              <label className="form-check-label" htmlFor="is_editable">
                {t("common.is_editable")}
              </label>
            </div>
          </Col>
        </Row>
      )}
      {cusomComponent}
      <div className="d-flex justify-content-end gap-2 mt-3">
        <Button color="light" onClick={handelClose} className="btn-sm">
          {t("common.cancel")}
        </Button>
        {!isShow && (
          <Button
            color="primary"
            onClick={handleSubmit(selectId ? UpdateFun : addFun)}
            disabled={isSubmitting}
            className="btn-sm"
          >
            {isSubmitting ? (
              <span className="spinner-border spinner-border-sm" />
            ) : selectId ? (
              t("common.update")
            ) : (
              t("common.add")
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SectionsActions;
