import ApiInstance from "../../../constant/api-instance";
import { PRODUCT_TYPES_ROUTES } from "./route";

const getAll = async ({ limit, page, status }) => {
  const { data } = await ApiInstance.get(PRODUCT_TYPES_ROUTES.PRODUCT_TYPES, {
    params: {
      limit,
      page,
      "filter[status]": status,
    },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${PRODUCT_TYPES_ROUTES.PRODUCT_TYPES}/${id}`
  );
  return data;
};
const active = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${PRODUCT_TYPES_ROUTES.ACTIVE}/${id}`
  );
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${PRODUCT_TYPES_ROUTES.IN_ACTIVE}/${id}`
  );
  return data;
};
const add = async (dataToSend) => {
  const { data } = await ApiInstance.post(
    `${PRODUCT_TYPES_ROUTES.PRODUCT_TYPES}`,
    dataToSend
  );
  return data;
};
const update = async ({ dataToSend, id }) => {
  const { data } = await ApiInstance.post(
    `${PRODUCT_TYPES_ROUTES.PRODUCT_TYPES}/${id}`,
    dataToSend
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${PRODUCT_TYPES_ROUTES.PRODUCT_TYPES}/${id}`
  );
  return data;
};
export const ProductTypesAPis = {
  deleteFu,
  update,
  add,
  getOne,
  active,
  inActive,
  getAll,
};
