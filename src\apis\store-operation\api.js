import ApiInstance from "../../constant/api-instance";
import { STORE_OPERATIONS_ROUTES } from "./route";

const getAll = async ({ limit, page, id_default }) => {
  const { data } = await ApiInstance.get(
    STORE_OPERATIONS_ROUTES.STORE_OPERATIONS,
    {
      params: {
        limit,
        page,
        id_default,
      },
    }
  );
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${STORE_OPERATIONS_ROUTES.STORE_OPERATIONS}/${id}`
  );
  return data;
};

const add = async ({ type, title }) => {
  const { data } = await ApiInstance.post(
    `${STORE_OPERATIONS_ROUTES.STORE_OPERATIONS}`,
    { type, title }
  );
  return data;
};

const update = async ({ id, type, title }) => {
  const { data } = await ApiInstance.put(
    `${STORE_OPERATIONS_ROUTES.STORE_OPERATIONS}/${id}`,
    {
      type,
      title,
    }
  );
  return data;
};

const deleteApi = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${STORE_OPERATIONS_ROUTES.STORE_OPERATIONS}/${id}`
  );
  return data;
};

export const storeOperationsApis = {
  update,
  add,
  getOne,
  getAll,
  deleteApi,
};
