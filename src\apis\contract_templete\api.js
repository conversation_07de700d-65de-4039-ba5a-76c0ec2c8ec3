import ApiInstance from "../../constant/api-instance";
import { CONTRACT_TEMPLATE_ROUTES } from "./route";

const getAll = async ({ limit, page, templateId, language }) => {
  const { data } = await ApiInstance.get(CONTRACT_TEMPLATE_ROUTES.TEMPLATE, {
    params: {
      limit,
      page,
      "filter[contract_type_id]": templateId,
      "filter[language]": language,
    },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CONTRACT_TEMPLATE_ROUTES.TEMPLATE}/${id}`
  );
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${CONTRACT_TEMPLATE_ROUTES.TEMPLATE}`,
    payload
  );
  return data;
};
const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${CONTRACT_TEMPLATE_ROUTES.TEMPLATE}/${id}`,
    payload
  );
  return data;
};

const deleteFu = async ({ id }) => {
  await ApiInstance.delete(`${CONTRACT_TEMPLATE_ROUTES.TEMPLATE}/${id}`);
};

export const contractTemplateAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
};
