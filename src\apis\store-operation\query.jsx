import { useQuery } from "@tanstack/react-query";
import { storeOperationsApis } from "./api";

const useGetAllOperation = ({ limit, page ,id_default}) => {
  const queryResult = useQuery({
    queryKey: ["all-store-operations", limit, page, id_default],
    queryFn: () => storeOperationsApis.getAll({ limit, page, id_default }),
  });
  return queryResult;
};

const useGetStoreOperation = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["store-operation", id],
    queryFn: () =>
      id > 0
        ? storeOperationsApis.getOne({ id })
        : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const useStoreOperationQueries = {
  useGetStoreOperation,
  useGetAllOperation,
};
