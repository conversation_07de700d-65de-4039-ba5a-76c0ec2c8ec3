import { Filter } from "../Common/filters";

// Table Header Component
const TableHeader = ({ headerGroups, generateSortingIndicator }) => (
  <thead
    className="sticky-top table-light table-nowrap"
    style={{ zIndex: 10, top: -4 }}
  >
    {headerGroups.map((headerGroup) => (
      <tr key={headerGroup.id} {...headerGroup.getHeaderGroupProps()}>
        {headerGroup.headers
          .filter((column) => column.Header !== "")
          .map((column) => (
            <th key={column.id}>
              <div className="mb-2" {...column.getSortByToggleProps()}>
                {column.render("Header")}
                {generateSortingIndicator(column)}
              </div>
              <Filter column={column} />
            </th>
          ))}
      </tr>
    ))}
  </thead>
);

export default TableHeader;
