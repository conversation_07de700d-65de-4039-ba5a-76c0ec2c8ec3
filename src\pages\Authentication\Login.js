import { <PERSON>, <PERSON>, <PERSON><PERSON>, Container, Label } from "reactstrap";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { AuthAPis } from "../../apis/auth/api";
import <PERSON><PERSON><PERSON>oader from "react-spinners/ClipLoader";
import Logo from "../../assets/logo/logo.png";
import { handleBackendErrors } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import { useState } from "react";
import { FaEyeSlash, FaRegEye } from "react-icons/fa";

const Login = () => {
  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    setError,
    control,
  } = useForm({
    defaultValues: { email: "", password: "" },
  });
  const [isShowPassword, setIsShowPassword] = useState(false);

  const togglePassword = () => {
    setIsShowPassword((prev) => !prev);
  };
  const navigate = useNavigate();

  const onSubmit = async (data) => {
    try {
      const response = await AuthAPis.logIn({
        email: data.email,
        password: data.password,
      });
      localStorage.setItem("authUser", JSON.stringify(response));
      // localStorage.setItem("token", );
      navigate("/dashboard");
    } catch (error) {
      // console.log("Error:", error);
      handleBackendErrors({ error, setError });
    }
  };

  const { t } = useTranslation();

  return (
    <div>
      <Container fluid className="p-0">
        <div
          style={{
            justifyContent: "center",
            width: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Row
            className="g-0"
            style={{
              width: "40vw",
              height: "100vh",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Col lg={12}>
              <div
                className="p-4 d-flex align-items-center"
                style={{ background: "#fff", borderRadius: 8 }}
              >
                <div className="w-100">
                  <Row className="justify-content-center">
                    <Col lg={9}>
                      <div>
                        <div className="text-center">
                          <div>
                            <Link to="/" className="">
                              <img
                                src={Logo}
                                alt=""
                                // height="20"
                                height={60}
                                width={60}
                                className="auth-logo logo-dark mx-auto"
                              />
                              <img
                                src={Logo}
                                alt=""
                                height={60}
                                width={60}
                                className="auth-logo logo-light mx-auto"
                              />
                            </Link>
                          </div>

                          <h4 className="font-size-18 mt-4">
                            {t("login.welcome")}
                          </h4>
                          <p className="text-muted">{t("login.helper_text")}</p>
                        </div>

                        <div className="p-2 mt-4">
                          <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="auth-form-group-custom mb-4">
                              <CustomInput
                                control={control}
                                error={errors.email}
                                label={t("common.email")}
                                name={"email"}
                              />
                            </div>

                            <div className="auth-form-group-custom">
                              <CustomInput
                                control={control}
                                error={errors.password}
                                label={t("common.password")}
                                name={"password"}
                                endIcon={
                                  <div
                                    style={{
                                      height: "100%",
                                      width: 20,
                                      display: "flex",
                                      justifyContent: "center",
                                      alignItems: "center",
                                      cursor: "pointer",
                                    }}
                                    onClick={togglePassword}
                                  >
                                    {/* <i className=" ri-eye-off-line"></i> */}
                                    {/* <FaRegEye size={18}/> */}
                                    {!isShowPassword ? (
                                      <FaEyeSlash size={13} />
                                    ) : (
                                      <FaRegEye size={13} />
                                    )}
                                  </div>
                                }
                                type={isShowPassword ? "text" : "password"}
                              />
                            </div>

                            <div className="mt-4 text-center">
                              <Button
                                color="primary"
                                className="w-md waves-effect waves-light btn-sm"
                                type="submit"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? (
                                  <ClipLoader
                                    color="white"
                                    size={15}
                                    aria-label="Loading Spinner"
                                    data-testid="loader"
                                  />
                                ) : (
                                  t("common.login")
                                )}
                              </Button>
                            </div>
                          </form>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </Container>
    </div>
  );
};

export default Login;
