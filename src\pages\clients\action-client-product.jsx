import { <PERSON><PERSON>, Col, Row } from "reactstrap";
import { useForm } from "react-hook-form";
import toastr from "toastr";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import ClipLoader from "react-spinners/ClipLoader";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  formatDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import { clientsAPis } from "../../apis/clients/api";
import TextAreaField from "../../components/Common/textArea";
import { clientsQueries } from "../../apis/clients/query";
import { productQueries } from "../../apis/products/query";

const ClientActions = ({
  isShow,
  selectId,
  handelCloseAddModal,
  setOpenAddModal,
  refetch,
  clientId,
}) => {
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [productList, setProductList] = useState([]);

  const { t } = useTranslation();

  const handelCancel = () => {
    reset();
    handelCloseAddModal();
  };

  const schema = yup
    .object({
      start_date: yup
        .date()
        .required(t("common.field_required"))
        .max(yup.ref("end_date"), t("common.date_error")),
      end_date: yup
        .date()
        .required(t("common.field_required"))
        .min(yup.ref("start_date"), t("common.end_date_error")),
      card_number: yup.string().required(t("common.field_required")),
      notes: yup.string().nullable(),
    })
    .required();

  const initialSTate = {
    products: [], // Add image default value
    notes: null,
    card_number: null,
    start_date: formatDate(today),
    end_date: formatDate(
      new Date(new Date().setDate(new Date().getDate() + 1))
    ),
    product_code: null,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    control,
    watch,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const {
    data: products,
    isLoading,
    isRefetching,
  } = clientsQueries.useGetOneClient({
    id: selectId,
  });

  const { data: _products } = productQueries.useGetAll({
    status: 1,
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && products?.data) {
      let _productsList = [];
      products?.data.map((item) =>
        _productsList.push({
          id: item?.product?.id,
          product_code: item?.product_code,
          product_id: item?.product?.id,
          productName: item?.product?.name,
        })
      );

      setProductList(_productsList);
      // Populate form with role data idwhen loaded
      reset({
        card_number: products?.data[0]?.warranty_card?.card_number,
        start_date: products?.data[0]?.warranty_card?.start_date,
        end_date: products?.data[0]?.warranty_card?.end_date,
        notes: products?.data[0]?.warranty_card?.notes,
      });
    }
  }, [selectId, isLoading, products]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("offer.product_validation"));
      return;
    }
    if (watch("products")?.value || watch("product_code")) {
      toastr.error(t("store-request.val_error"));
      return;
    }
    try {
      const filterDataToSend = {
        card_number: data.card_number,
        start_date: new Date(data.start_date).toISOString().split("T")[0],
        end_date: new Date(data.end_date).toISOString().split("T")[0],
        notes: data.notes,
        products: productList,
        client_id: clientId,
      };
      const response = await clientsAPis.updateClient({
        payload: filterDataToSend,
        id: selectId,
      });
      toastr.success(response.message);
      handelCloseAddModal();
      setOpenAddModal(false);
      refetch();
      reset();
    } catch (error) {
      // toastr.error("There are errors");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("offer.product_validation"));
      return;
    }

    if (watch("products")?.value || watch("product_code")) {
      toastr.error(t("store-request.val_error"));
      return;
    }

    try {
      const filterDataToSend = {
        card_number: data.card_number,
        start_date: new Date(data.start_date).toISOString().split("T")[0],
        end_date: new Date(data.end_date).toISOString().split("T")[0],
        notes: data.notes,
        products: productList,
        client_id: clientId,
      };
      const response = await clientsAPis.addClient({
        payload: filterDataToSend,
      });
      toastr.success(response.message);
      handelCloseAddModal();
      setOpenAddModal(false);
      refetch();
      reset(); // Reset form after successful submission
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const [setSelected, setSelectedId] = useState(0);
  const [productName, setProductName] = useState("");

  const isUpdate = useRef(null);

  useEffect(() => {
    if (_products?.result?.length > 0) {
      setOptionGroup(
        _products?.result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  }, [_products?.result, productList]);

  const handelAddProduct = () => {
    // Create new product to add to the list
    const newProduct = {
      product_id: watch("products")?.value || setSelected,
      id: new Date().getTime(), // Using timestamp as ID
      productName: watch("products")?.label || productName,
      product_code: watch("product_code"),
    };

    setProductList((prev) => [...prev, newProduct]);
    setProductName("");
    setSelectedId(0);
    setValue("products", null);
    setValue("product_code", "");
    isUpdate.current = null;
  };

  const handelRemoveFromList = (id) => {
    setProductList((prev) => prev.filter((item) => item.id !== id));
  };

  const handelUpdate = ({ id }) => {
    isUpdate.current = true;
    const selectedProduct = productList.find((item) => item.id === id);

    setValue("products", {
      label: selectedProduct.productName,
      value: selectedProduct.id,
    });
    setValue("product_code", selectedProduct.product_code);
    setProductList((prev) => prev.filter((item) => item.id !== id));
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("tasks.product_code"),
      accessor: "product_code",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <>
            {!isShow && (
              <div className="d-flex align-items-center">
                <div
                  className="me-3 text-primary"
                  onClick={() => {
                    if (cellProps.id > 0) {
                      handelUpdate({
                        id: cellProps.id,
                        isNew: false,
                      });
                    } else {
                      handelUpdate({
                        isNew: true,
                        id: 0,
                        value: cellProps.name,
                      });
                    }
                  }}
                  style={{ cursor: "pointer" }}
                >
                  <i className="mdi mdi-pencil pointer-event font-size-14"></i>
                </div>
                <div
                  onClick={() => handelRemoveFromList(cellProps.id)}
                  className="text-danger"
                  style={{ cursor: "pointer" }}
                >
                  <i className="mdi mdi-trash-can font-size-14 "></i>
                </div>
              </div>
            )}
          </>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      productList?.length > 0
        ? productList
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              product_code: item.product_code,
              name: item.productName,
            }))
            .reverse()
        : [],
    [productList]
  );

  return (
    <div>
      <Row>
        <form
          onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
        >
          {isLoading || isRefetching ? (
            <div className="container-loading">
              <ClipLoader color="#ddd" size={50} />
            </div>
          ) : (
            <div
            // style={{ maxHeight: 450, overflowY: "auto", overflowX: "hidden" }}
            >
              <Row>
                <Col xs={6}>
                  <CustomInput
                    name="start_date"
                    control={control}
                    label={t("common.start_date")}
                    className="mb-2"
                    type="date"
                    isDisabled={isShow}
                    error={errors.start_date}
                  />
                </Col>
                <Col xs={6}>
                  <CustomInput
                    name="end_date"
                    control={control}
                    label={t("common.end_date")}
                    type="date"
                    isDisabled={isShow}
                    error={errors.end_date}
                  />
                </Col>
                <Col xs={6}>
                  <CustomInput
                    name="card_number"
                    control={control}
                    className="mb-2"
                    label={t("tasks.card_number")}
                    type="string"
                    isDisabled={isShow}
                    error={errors.card_number}
                  />
                </Col>
                <Col xs={6}>
                  <TextAreaField
                    name="notes"
                    control={control}
                    placeholder={t("common.note")}
                    rows={1}
                    disabled={isShow}
                    error={errors?.notes}
                  />
                </Col>
                <Col
                  xs={12}
                  style={{
                    border: "1px solid #ccc",
                    width: "98%",
                    margin: "auto",
                    padding: 10,
                    marginBottom: 10,
                    borderRadius: 5,
                  }}
                >
                  {!isShow && (
                    <div
                      style={{
                        display: "grid",
                        gap: 12,
                      }}
                    >
                      <Row>
                        {!isShow && (
                          <Col xs={6}>
                            <CustomSelect
                              name="products"
                              control={control}
                              label={t("common.product")}
                              defaultValue={[]}
                              isMulti={false}
                              options={optionGroup}
                            />
                          </Col>
                        )}
                        <Col xs={6}>
                          <CustomInput
                            name="product_code"
                            control={control}
                            label={t("tasks.product_code")}
                            type="string"
                          />
                        </Col>
                      </Row>
                      <Button
                        type="button"
                        color="primary"
                        className="btn btn-sm btn-primary"
                        style={{ width: "fit-content" }}
                        onClick={handelAddProduct}
                        disabled={
                          !watch("products")?.value || !watch("product_code")
                        }
                      >
                        {t("common.add_to_table")}
                      </Button>
                    </div>
                  )}
                  <div style={{ height: "30vh" }}>
                    <TableContainer
                      hideSHowGFilter
                      columns={columns || []}
                      data={rowData || []}
                      isPagination={false}
                      iscustomPageSize={false}
                      isBordered={true}
                      isSmall
                      customHeight={"100%"}
                      hidePagination
                      isAddOptions={false}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          )}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              justifyContent: "space-between",
              position: "sticky",
              bottom: 0,
            }}
          >
            <div className="d-flex align-items-center gap-2">
              <Button
                type="button"
                color="light"
                className="btn-sm"
                style={{ height: "32px", width: "54px" }}
                onClick={() => {
                  handelCancel();
                  setOpenAddModal(false);
                }}
              >
                {t("common.close")}
              </Button>
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.save")
                  )}
                </Button>
              )}
            </div>
          </div>
        </form>
      </Row>
    </div>
  );
};
export default ClientActions;
