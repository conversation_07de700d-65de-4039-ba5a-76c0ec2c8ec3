import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  ModalB<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mo<PERSON>Header,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import { ProductTypesQueries } from "../../apis/types/product-types/query";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { ProductTypesAPis } from "../../apis/types/product-types/api";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import { Can } from "../../components/permissions-way/can";
import TypesModel from "../../components/Common/types-model";
import ProductsUnitsType from "./action-product-types";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";
const TasksTypes = () => {
  const { t, i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingTasksTypes,
    refetch,
  } = ProductTypesQueries.useGetAll({ limit: 10, page: page });

  const { pathname } = useLocation();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [open, setOpen] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setstausIsActive(false);
    setOpenStatsusModal(false);
    setIsShow(false);
    setOpen(false);
    refetch();
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);
  const handelSelectId = (id) => {
    setSelectId(id);
    setOpen(true);
  };

  const handelAddBonds = () => {
    setOpen(true);
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const response = await ProductTypesAPis.active({
        id: id,
      });
      refetch();
      toastr.success(response.message);
      handelCLoseModal();
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const response = await ProductTypesAPis.inActive({
        id: id,
      });
      refetch();
      handelCLoseModal();
      setIsDeleting(false);
      toastr.success(response.message);
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handelCloseSideBar = () => {
    setOpen(false);
  };

  const breadcrumbItems = [
    // { title: "list", link: pathname },
    {
      title: t("types.product_types.product_groups"),
      link: pathname,
    },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("product_type.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("product_type.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };
  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.title"),
      width: 50,
      accessor: "title",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.count"),
      accessor: "products_count",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.status"),
      disableFilters: true,
      filterable: false,
      accessor: (cellProps) => {
        return (
          <div className="form-check form-switch">
            <Input
              type="checkbox"
              className="form-check-input"
              // defaultChecked={cellProps.status === 1 ? true : false}
              checked={cellProps.status === 1}
              onClick={() => handelToggleStatus({ cellProps })}
            />
            <></>
          </div>
        );
      },
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2">
            <Can permission={"product_type.update"}>
              <Link
                className="text-primary"
                // onClick={() => {}}
                onClick={() => {
                  handelSelectId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"product_type.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    // handelSelectId(cellProps.id);
                    setSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            <Can permission={"product_type.show"}>
              <Link
                // to={`/product-types-action?id=${cellProps.id}?Show=true`}
                className="text-success"
                onClick={() => {
                  handelSelectId(cellProps.id);
                  setIsShow(true);
                }}
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size

              title:
                i18n.language === "eng"
                  ? truncateText({ text: item.title.en, maxLengthPercent: 0.1 })
                  : truncateText({
                      text: item.title.ar,
                      maxLengthPercent: 0.3,
                    }) || "----",
              description:
                truncateText({
                  text: item.description,
                  maxLengthPercent: 0.3,
                }) || "----",
              products_count: item.products_count || 0,
              status: item.status,
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await ProductTypesAPis.deleteFu({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      // toastr.error(error?.response?.data?.message);
      handleBackendErrors({ error });

      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("types.product_types.product_groups")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={
            t("common.add") + " " + t("types.product_types.product_groups")
          }
          handleOrderClicks={handelAddBonds}
          canPermission={"product_type.store"}
        />
        <Card>
          <CardBody>
            {/* {isLoadingTasksTypes ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              isPagination={true}
              // isGlobalFilter={true}
              iscustomPageSize={true}
              isBordered={true}
              pageSize={10}
              pageIndex={page}
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={page}
              setPage={setPage}
              hasTotal
              total={ContractTypes?.meta?.products_total_count}
              isLoading={isLoadingTasksTypes}
            />
            {/* )} */}
          </CardBody>
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("types.product_types.product_groups")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.close")}
              </Button>
              <Button onClick={DeleteFun} type="button" color="danger">
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      </Container>
      {openStatsusModal && selectId && (
        <Modal isOpen={openStatsusModal} backdrop="static">
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.Attention")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button type="button" color="light" onClick={handelCLoseModal}>
                {t("common.no")}
              </Button>
              <Button
                onClick={() =>
                  statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                }
                type="button"
                color="primary"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.yes")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
      )}
      <TypesModel
        open={open}
        handelClose={handelCloseSideBar}
        hideAll={true}
        content={
          <div>
            <h1 style={{ fontSize: 16 }} className="mb-4">
              {isShow
                ? t("common.show") +
                  " " +
                  t("types.product_types.product_groups")
                : selectId
                ? t("common.update") +
                  " " +
                  t("types.product_types.product_groups")
                : t("common.add") +
                  " " +
                  t("types.product_types.product_groups")}
            </h1>
            <ProductsUnitsType
              handelClose={handelCLoseModal}
              isShow={isShow}
              selectId={selectId}
            />
          </div>
        }
      />
    </div>
  );
};
export default TasksTypes;
