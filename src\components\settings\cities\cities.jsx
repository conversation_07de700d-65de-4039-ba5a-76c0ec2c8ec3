import { Link } from "react-router-dom";
import <PERSON><PERSON><PERSON>oader from "react-spinners/ClipLoader";
import TableContainer from "../../Common/TableContainer";
import { useEffect, useMemo, useState } from "react";
import { citiesQueries } from "../../../apis/cities/query";
import {
  Button,
  Col,
  Label,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Row,
} from "reactstrap";
import toastr from "toastr";
import { Controller, useForm } from "react-hook-form";
import { handleBackendErrors } from "../../../helpers/api_helper";
import { citiesAPis } from "../../../apis/cities/api";
import { statesQueries } from "../../../apis/states/query";
import Select from "react-select";
import { useTranslation } from "react-i18next";
import { Can } from "../../permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const Cities = () => {
  const [selectedId, setSelectedId] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteMdal, setOpendeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const {
    data: cities,
    isLoading: isLoadingStates,
    refetch,
  } = citiesQueries.useGetAll({});

  const { data: states } = statesQueries.useGetAll({});

  const { data: citie, isLoading: isLoadingState } = citiesQueries.useGet({
    id: selectedId,
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    watch,
    register,
    setError,
    control,
  } = useForm({ defaultValues: { name: "" } });

  const handelCLoseModal = () => {
    setOpendeleteModal(false);
    setSelectedId(0);
    setOpenModal(false);
    reset({ name: "", state_id: null });
  };
  const { t } = useTranslation();
  const columns = useMemo(() => [
    {
      Header: "#",
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.states"),
      accessor: "state",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },

    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-start">
            <Can permission={"setting.update"}>
              <Link
                className="text-primary"
                onClick={() => {
                  handelAdd();
                  setSelectedId(cellProps.id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"setting.destroy"}>
              <Link
                onClick={() => {
                  setSelectedId(cellProps.id);
                  setOpendeleteModal(true);
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      cities?.result?.length > 0
        ? cities.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: index + 1,
              name: item?.name || "----",
              state: item?.state?.name || "----",
            }))
            .reverse()
        : [],
    [cities?.result, states?.result]
  );

  useEffect(() => {
    if (selectedId && citie?.result) {
      // const selectState = states.result.find(
      //   (item) => item.id === citie.result.state_id
      // );
      reset({
        name: citie?.result?.name,
        state_id: {
          label: citie?.result?.state?.name,
          value: citie?.result?.state?.id,
        },
      });
    }
  }, [citie?.result]);

  const handelAdd = () => {
    setOpenModal(true);
  };

  const addFun = async (data) => {
    try {
      const response = await citiesAPis.add({
        payload: { name: data.name, state_id: data.state_id.value },
      });
      toastr.success(response.message);
      handelCLoseModal();
      refetch();
      reset();
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };
  const UpdateFun = async (data) => {
    try {
      const response = await citiesAPis.update({
        payload: { name: data.name, state_id: data.state_id.value },
        id: selectedId,
      });
      toastr.success(response.message);
      refetch();
      reset();
      handelCLoseModal();
    } catch (errors) {
      handleBackendErrors({ error: errors, setError });
    }
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await citiesAPis.deleteFu({
        id: selectedId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      reset();
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  useEffect(() => {
    if (states?.result?.length > 0) {
      setOptionGroup(
        states.result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  }, [states?.result]);

  return (
    <div>
      {isLoadingStates ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <TableContainer
          hideSHowGFilter={false}
          columns={columns || []}
          data={rowData || []}
          isPagination={false}
          hidePagination
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("common.cities")}
          handleOrderClicks={handelAdd}
          customComponent={<div>{t("common.cities")}</div>}
          canPermission="client.store"
        />
      )}

      <Modal isOpen={openModal} toggle={handelCLoseModal} backdrop="static">
        <ModalHeader toggle={handelCLoseModal}>
          {selectedId
            ? t("common.update") + " " + t("common.cities")
            : t("common.add") + " " + t("common.cities")}
        </ModalHeader>
        <ModalBody>
          <Row>
            <form
              onSubmit={
                selectedId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
              }
            >
              {isLoadingState ? (
                <div className="container-loading">
                  <ClipLoader color="#ddd" size={50} />
                </div>
              ) : (
                <>
                  <Row>
                    <Col xs={12} className="mb-2">
                      <div>
                        <Label className="form-label" htmlFor="total">
                          {t("common.select")} {t("common.states")}
                        </Label>
                      </div>
                      <Controller
                        name="state_id"
                        control={control}
                        defaultValue={[]}
                        render={({ field }) => (
                          <Select
                            {...field}
                            isClearable
                            placeholder="---"
                            options={optionGroup}
                            isMulti={false}
                            // isDisabled={isShow}
                            styles={{
                              menuList: (props) => ({
                                ...props,
                                paddingBottom: 10,
                                height: "100px",
                              }),
                              menu: (props) => ({
                                ...props,
                                height: "100px",
                              }),
                            }}
                          />
                        )}
                      />
                    </Col>
                    <Col xs={12}>
                      <div className="mb-2">
                        <Label
                          className="form-label"
                          htmlFor="title-in-english"
                        >
                          {t("common.name_of_city")}
                        </Label>
                        <input
                          {...register("name", { required: true })}
                          placeholder="----"
                          type="text"
                          className={`form-control ${
                            errors.name ? "is-invalid" : ""
                          }`}
                        />
                        {errors.name && (
                          <div className="invalid-feedback">
                            {errors.name.message}
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </>
              )}
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <Button type="button" color="light" onClick={handelCLoseModal}>
                  {t("common.close")}
                </Button>
                {/* {!isShow && ( */}
                <Button
                  color="primary"
                  className="waves-effect waves-light primary-button"
                  type="submit"
                  disabled={isSubmitting || (!watch("name") && !watch("name"))}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectedId ? (
                    t("common.update")
                  ) : (
                    t("common.add")
                  )}
                </Button>
                {/* )} */}
              </div>
            </form>
          </Row>
        </ModalBody>
      </Modal>
      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("common.city")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button type="button" color="light" onClick={handelCLoseModal}>
              {t("common.close")}
            </Button>
            <Button onClick={DeleteFun} type="button" color="danger">
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};

export default Cities;
