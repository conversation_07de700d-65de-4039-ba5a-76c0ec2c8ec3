import ApiInstance from "../../constant/api-instance";
import { PRODUCT_ROUTES } from "./route";

const getAll = async ({
  limit,
  page,
  status,
  ProductTypeId,
  searchParams,
  billType,
  contractType,
}) => {
  const { data } = await ApiInstance.get(PRODUCT_ROUTES.DELEGATE, {
    params: {
      limit,
      page,
      "filter[status]": status,
      "filter[product_type_ids][]": ProductTypeId,
      "filter[bill_type_id]": billType ? billType : null,
      "filter[contract_type_id]": contractType ? contractType : null,
      ...searchParams,
    },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${PRODUCT_ROUTES.DELEGATE}/${id}`);
  return data;
};
const getProductUnites = async ({ status }) => {
  const { data } = await ApiInstance.get(`${PRODUCT_ROUTES.PRODUCT_UNITS}`, {
    params: { status },
  });
  return data;
};
const getProductTypes = async ({ status }) => {
  const { data } = await ApiInstance.get(`${PRODUCT_ROUTES.PRODUCT_Types}`, {
    params: {
      "filter[status]": status,
    },
  });
  return data;
};

const active = async ({ id }) => {
  const { data } = await ApiInstance.get(`${PRODUCT_ROUTES.ACTIVE}/${id}`);
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(`${PRODUCT_ROUTES.IN_ACTIVE}/${id}`);
  return data;
};

const add = async (formData) => {
  const { data } = await ApiInstance.post(
    `${PRODUCT_ROUTES.DELEGATE}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};
const update = async ({ formData, id }) => {
  const { data } = await ApiInstance.post(
    `${PRODUCT_ROUTES.DELEGATE}/${id}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

const deleteFu = async ({ id }) => {
  await ApiInstance.delete(`${PRODUCT_ROUTES.DELEGATE}/${id}`);
};
export const productAPis = {
  deleteFu,
  update,
  add,
  getAll,
  getOne,
  getProductUnites,
  getProductTypes,
  active,
  inActive,
};
