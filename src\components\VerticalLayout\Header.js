import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import { But<PERSON> } from "reactstrap";
import { useTranslation, withTranslation } from "react-i18next";
import ProfileMenu from "../CommonForBoth/TopbarDropdown/ProfileMenu";
import { toggleRightSidebar, changeSidebarType } from "../../store/actions";
import Logo from "../../assets/logo/logo.png";
import logosmdark from "../../assets/images/logo-sm-dark.png";
import logodark from "../../assets/images/logo-dark.png";

const Header = ({ toggleMenuCallback, toggleSidebar, isCollapsed }) => {
  const [isSearch, setIsSearch] = useState(false);
  const { i18n } = useTranslation();
  const dispatch = useDispatch();
  const { layoutType, leftSideBarType } = useSelector((state) => state.Layout);
  const { pathname } = useLocation();

  const toggleRightbar = () => {
    dispatch(toggleRightSidebar());
  };

  // Toggle sidebar collapse with smooth animation
  const toggleMenu = () => {
    // Get references to the sidebar and main content elements
    const sidebar = document.querySelector(".vertical-menu");
    const mainContent = document.querySelector(".main-content");

    const isCollapsed = document.body.classList.contains("vertical-collpsed");

    if (isCollapsed) {
      // Add animation classes before expanding
      sidebar.classList.add("sidebar-expanding");
      mainContent.classList.add("content-expanding");

      // If already collapsed, expand it
      document.body.classList.remove("vertical-collpsed");

      // Wait for CSS transition to complete before changing Redux state and removing animation classes
      setTimeout(() => {
        dispatch(changeSidebarType("default"));
        sidebar.classList.remove("sidebar-expanding");
        mainContent.classList.remove("content-expanding");
      }, 300);
    } else {
      // Add animation classes before collapsing
      sidebar.classList.add("sidebar-collapsing");
      mainContent.classList.add("content-collapsing");

      // If expanded, collapse it
      document.body.classList.add("vertical-collpsed");

      // Wait for CSS transition to complete before changing Redux state and removing animation classes
      setTimeout(() => {
        dispatch(changeSidebarType("icon"));
        sidebar.classList.remove("sidebar-collapsing");
        mainContent.classList.remove("content-collapsing");
      }, 300);
    }
  };

  const toggleFullscreen = () => {
    if (
      !document.fullscreenElement &&
      !document.mozFullScreenElement &&
      !document.webkitFullscreenElement
    ) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(
          Element.ALLOW_KEYBOARD_INPUT
        );
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen();
      }
    }
  };

  const handelToggleLanguage = () => {
    if (i18n.language === "eng") {
      i18n.changeLanguage("ar");
    } else {
      i18n.changeLanguage("eng");
    }
  };

  return (
    <header id="page-topbar">
      <div className="navbar-header">
        <div
          className="d-flex"
          style={{
            height: "100%",
          }}
        >
          <div
            className={pathname !== "/settings" ? "navbar-brand-box" : ""}
            style={{ paddingInlineStart: isCollapsed ? 20 : 27 }}
          >
            <Link to="/dashboard">
              <div
                className="logo-lg"
                style={{
                  display: "flex",
                  height: "100%",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <img src={Logo} alt="" height="20" />
                {!isCollapsed && (
                  <span
                    style={{
                      fontSize: 15,
                      color: pathname === "/settings" ? "#121212" : "#fff",
                      fontWeight: "bold",
                      paddingInlineStart: 4,
                    }}
                  >
                    Secnt World
                  </span>
                )}
              </div>
            </Link>
          </div>
          {/* <div
            style={{
              height: "100%",
              width: 100,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={toggleMenu}
          >
            <i className="ri-menu-2-fill" style={{ fontSize: "20px" }}></i>
          </div> */}
          {pathname !== "/settings" && (
            <Button
              size="sm"
              color="none"
              type="button"
              onClick={toggleMenu}
              className="header-item waves-effect"
              id="vertical-menu-btn"
              style={{
                background: "rgba(255, 255, 255, 0.05)",
                borderRadius: "4px",
                width: "36px",
                height: "36px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                transition: "all 0.3s ease-in-out",
                margin: "0 5px",
                padding: 0,
              }}
              title={
                document.body.classList.contains("vertical-collpsed")
                  ? "Expand Sidebar"
                  : "Collapse Sidebar"
              }
            >
              <i
                className={
                  document.body.classList.contains("vertical-collpsed")
                    ? "ri-menu-unfold-line"
                    : "ri-menu-fold-line"
                }
                style={{ fontSize: "18px" }}
              ></i>
            </Button>
          )}
        </div>

        <div className="d-flex">
          <div className="dropdown d-inline-block d-lg-none ms-2">
            <button
              type="button"
              onClick={() => setIsSearch(!isSearch)}
              className="btn header-item noti-icon waves-effect"
              id="page-header-search-dropdown"
            >
              <i className="ri-search-line"></i>
            </button>
          </div>

          <Button
            style={{ height: "fit-content", alignSelf: "center" }}
            color="primary"
            onClick={handelToggleLanguage}
          >
            {i18n.language === "ar" ? "English" : "العربية"}
          </Button>
          <div className="dropdown d-none d-lg-inline-block ms-1">
            {/* <Button
              color="none"
              type="button"
              className="header-item noti-icon waves-effect"
              onClick={toggleFullscreen}
            >
              <i className="ri-fullscreen-line"></i>
            </Button> */}
          </div>
          <ProfileMenu />
        </div>
      </div>
    </header>
  );
};

export default withTranslation()(Header);
