import { useQuery } from "@tanstack/react-query";
import { contractAPis } from "./api";

const useGetAllBills = ({ limit, page, searchParams }) => {
  const queryResult = useQuery({
    queryKey: ["all-contract", limit, page, searchParams],
    queryFn: () => contractAPis.getAll({ limit, page, searchParams }),
  });
  return queryResult;
};

const useGetAllListBillsForDelete = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["get-active-contract", id],
    queryFn: () =>
      id > 0
        ? contractAPis.getAllActiveContract({ id })
        : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });
  return queryResult;
};

const useGetOneContract = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["get-contract", id],
    queryFn: () =>
      id > 0 ? contractAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });
  return queryResult;
};

export const ContracyQueries = {
  useGetAllListBillsForDelete,
  useGetAllBills,
  useGetOneContract,
};
