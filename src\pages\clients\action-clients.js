import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Card, CardBody, Col, Container, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { clientsAPis } from "../../apis/clients/api";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useTranslation } from "react-i18next";
import { clientsQueries } from "../../apis/clients/query";
import { useLocation, useNavigate } from "react-router-dom";
import { delegateQueries } from "../../apis/delegate/query";
import { handleBackendErrors } from "../../helpers/api_helper";
import { clientsGroupsQueries } from "../../apis/client-group/query";
import { statesQueries } from "../../apis/states/query";
import { citiesQueries } from "../../apis/cities/query";
import { locationQueries } from "../../apis/locations/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import AddLocationModel from "../../components/settings/location/add-modal";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import ImageUpload from "../../components/Common/ImageUpload.jsx";
import "leaflet/dist/leaflet.css";
import { Map, TileLayer, Marker, Popup } from "react-leaflet";
import L from "leaflet";
import { FaEyeSlash, FaRegEye } from "react-icons/fa";

delete L.Icon.Default.prototype._getIconUrl;

L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
  iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
  shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
});

const ClientActions = () => {
  const [position, setPosition] = useState([51.505, -0.09]); // Default position (London)
  const [selectedLocation, setSelectedLocation] = useState(null);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [images, setImages] = useState([]);
  const [openAddModel, setOpenAddModel] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const { t } = useTranslation();
  const { pathname } = useLocation();

  const statsusOption = [
    { label: t("common.active"), value: 1 },
    { label: t("common.in_active"), value: 2 },
  ];

  const schema = yup
    .object({
      company_name: yup.string().required(t("common.field_required")),

      website_url: yup
        .string()
        .nullable()
        .notRequired()
        .url(t("clients.validations.url")),

      email: yup
        .string()
        .nullable()
        .notRequired()
        .email(t("clients.validations.email")),

      next_visit_day: yup
        .number()
        .nullable()
        .transform((value, originalValue) => {
          return originalValue === "" ? null : value;
        })
        .min(0, t("clients.validations.visit_number_min"))
        .max(28, t("clients.validations.visit_number_max")),

      visit_number: yup
        .number()
        .nullable()
        .transform((value, originalValue) => {
          return originalValue === "" ? null : value;
        })
        .min(0, t("clients.validations.visit_number"))
        .max(3, t("clients.validations.visit_number"))
        .notRequired(),
      password: yup.string().nullable(),

      confirm_password: yup
        .string()
        .oneOf([yup.ref("password"), null], t("common.passwords_must_match")),
    })
    .required();

  const initialSTate = {
    full_name: "",
    email: "",
    password: "",
    phone_number: "",
    website_url: "",
    company_description: "",
    address: "",
    status: statsusOption[0],
    visit_number: null,
    next_visit_day: null,
    bound_total: null,
    delegate_id: null,
    client_group_id: null,
    location_id: null,
    state_id: null,
    city_id: null,
    image: null,
    latitude: null,
    longitude: null,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    setError,
    watch,
    control,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  // Add useEffect to get current position
  useEffect(() => {
    if (!selectId) {
      // Only for new forms
      if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            setPosition([lat, lng]);
            setValue("latitude", lat);
            setValue("longitude", lng);
            setSelectedLocation([lat, lng]); // Set the marker on the map
            toastr.info("تم تحديد موقعك الحالي تلقائياً");
          },
          (error) => {
            console.error("Error getting location:", error);
            // Set default position if geolocation fails
            setPosition([51.505, -0.09]); // Default position (London)
            setValue("latitude", 51.505);
            setValue("longitude", -0.09);
            setSelectedLocation([51.505, -0.09]);
          }
        );
      } else {
        // Set default position if geolocation is not available
        setPosition([51.505, -0.09]); // Default position (London)
        setValue("latitude", 51.505);
        setValue("longitude", -0.09);
        setSelectedLocation([51.505, -0.09]);
      }
    }
  }, [selectId, setValue]);

  const { data, isLoading } = clientsQueries.useGet({
    id: Number(selectId),
  });

  // Add useEffect to handle location from backend
  useEffect(() => {
    if (selectId && data?.result) {
      if (data?.result?.location_lat && data?.result?.location_lng) {
        // If there's existing location data, use it
        const lat = parseFloat(data.result.location_lat);
        const lng = parseFloat(data.result.location_lng);
        setPosition([lat, lng]);
        setSelectedLocation([lat, lng]);
      } else {
        // If no location data, get current location after a short delay
        setTimeout(() => {
          if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const currentLat = position.coords.latitude;
                const currentLng = position.coords.longitude;
                setPosition([currentLat, currentLng]);
                setValue("latitude", currentLat);
                setValue("longitude", currentLng);
                setSelectedLocation([currentLat, currentLng]);
              },
              (error) => {
                console.error("Error getting location:", error);
              }
            );
          }
        }, 1000);
      }
    }
  }, [data, selectId, setValue]);

  const handleMapClick = (e) => {
    if (!isShow) {
      const { lat, lng } = e.latlng;
      setSelectedLocation([lat, lng]);
      // Update form values
      setValue("latitude", lat);
      setValue("longitude", lng);
    }
  };

  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: clientGroups } = clientsGroupsQueries.useGetAll({ status: 1 });
  const { data: states } = statesQueries.useGetSearchState({});
  const { data: citie } = citiesQueries.useGetSearchCity({
    state_id: watch("state_id")?.value,
  });

  const { data: locations, refetch } = locationQueries.useGetSearchLocations({
    city_id: watch("city_id")?.value,
  });

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate("/clients");
    reset();
  };

  const GroupOptions = useSetSelectOptions({
    data: clientGroups?.result,
    getOption: (item) => ({
      label: item.group_title, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const stateOptions = useSetSelectOptions({
    data: states?.result,
    getOption: (item) => ({
      label: item.name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const CitiesOptions = useSetSelectOptions({
    data: citie?.result,
    getOption: (item) => ({
      label: item.name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const LocationsOptions = useSetSelectOptions({
    data: locations?.result,
    getOption: (item) => ({
      label: item.name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const fieldsNames = [
    {
      id: 0,
      name: "full_name",
      label: t("common.full_name"),
      isRequired: true,
      error: errors.full_name,
      showWhen: true,
    },
    {
      id: 1,
      name: "company_name",
      label: t("clients.company_name"),
      isRequired: true,
      error: errors.company_name,
      showWhen: true,
    },
    {
      id: 2,
      name: "email",
      label: t("common.email"),
      isRequired: true,
      error: errors.email,
      showWhen: true,
    },
    {
      id: 4,
      name: "phone_number",
      label: t("common.phone_number"),
      type: "number",
      isRequired: true,
      error: errors.phone_number,
      showWhen: true,
    },
    {
      id: 6,
      name: "visit_number",
      label: t("common.visit_number"),
      type: "number",
      isRequired: true,
      error: errors.visit_number,
      showWhen: true,
    },
    {
      id: 7,
      name: "next_visit_day",
      label: t("clients.next_visit_day"),
      type: "number",
      isRequired: true,
      error: errors.next_visit_day,
      showWhen: true,
    },
    {
      id: 5,
      name: "website_url",
      label: t("clients.website_url"),
      isRequired: true,
      error: errors.website_url,
      showWhen: true,
    },
    {
      id: 11,
      name: "client_group_id",
      label: t("clients.group"),
      error: errors.client_group_id,
      isSelect: true,
      options: GroupOptions,
      showWhen: true,
    },
    {
      id: 9,
      name: "state_id",
      label: t("common.state"),
      error: errors.state_id,
      isSelect: true,
      options: stateOptions,
      showWhen: true,
    },
    {
      id: 8,
      name: "city_id",
      label: t("common.city"),
      error: errors.city_id,
      isSelect: true,
      options: CitiesOptions,
      showWhen: watch("state_id")?.value,
    },
    {
      id: 10,
      name: "location_id",
      label: t("common.location"),
      error: errors.phone_number,
      isSelect: true,
      options: LocationsOptions,
      showWhen: watch("city_id")?.value,
      additionalElement: !isShow && (
        <Button
          onClick={() => {
            setOpenAddModel(true);
          }}
          color="primary"
        >
          <i class="ri-add-line"></i>{" "}
        </Button>
      ),
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "company_description",
      label: t("clients.company_description"),
      isRequired: false,
    },
    {
      id: 1,
      name: "address",
      label: t("clients.address"),
      isRequired: false,
    },
  ];

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Set image preview if image exists
      if (data.result.image) {
        setImages([{ data_url: data.result.image }]);
      }
      // Populate form with role data when loaded
      reset({
        full_name: data?.result.full_name || " ",
        email: data?.result.email,
        // password: data?.result.password || null,
        phone_number: data?.result.phone_number || " ",
        website_url: data?.result.website_url || null,
        company_description: data?.result.description || " ",
        company_name: data?.result.company_name || " ",
        address: data?.result.address || " ",
        status: {
          label: statsusOption.find(
            (item) => item.value === data?.result.status
          )?.label,
          value: data?.result.status,
        },
        client_group_id: data?.result?.client_group
          ? {
              label: data?.result?.client_group?.group_title,
              value: data?.result?.client_group?.id,
            }
          : null,
        location_id: data?.result?.location
          ? {
              label: data?.result?.location?.name,
              value: data?.result?.location?.id,
            }
          : null,
        state_id: data?.result?.location?.city?.state
          ? {
              label: data?.result?.location?.city?.state?.name,
              value: data?.result?.location?.city?.state?.id,
            }
          : null,
        city_id: data?.result?.location?.city
          ? {
              label: data?.result?.location?.city?.name,
              value: data?.result?.location?.city?.id,
            }
          : null,
        delegate_id: data?.result?.delegate
          ? {
              label: data?.result?.delegate?.full_name,
              value: data?.result?.delegate?.id,
            }
          : null,
        visit_number: data?.result.visit_number || null,
        next_visit_day: data?.result.next_visit_day || null,
        bound_total: data?.result.bound_total || null,
        image: data?.result?.image,
        latitude: data?.result?.location_lat
          ? parseFloat(data.result.location_lat)
          : null,
        longitude: data?.result?.location_lng
          ? parseFloat(data.result.location_lng)
          : null,
      });

      // Set map position based on coordinates
      const lat = data?.result?.location_lat;
      const lng = data?.result?.location_lng;

      if (lat && lng) {
        const latNum = parseFloat(lat);
        const lngNum = parseFloat(lng);
        setPosition([latNum, lngNum]);
        setSelectedLocation([latNum, lngNum]);
      } else {
        // If no location data, get current location after a short delay
        setTimeout(() => {
          if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const currentLat = position.coords.latitude;
                const currentLng = position.coords.longitude;
                setPosition([currentLat, currentLng]);
                setValue("latitude", currentLat);
                setValue("longitude", currentLng);
                setSelectedLocation([currentLat, currentLng]);
              },
              (error) => {
                console.error("Error getting location:", error);
              }
            );
          }
        }, 1000);
      }
    }
  }, [selectId, isLoading, data?.result, setValue]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      const formData = new FormData();

      const jsonData = {};

      // Helper function to conditionally add fields
      const appendIfNotNull = (key, value) => {
        if (value !== null && value !== undefined && value !== "") {
          jsonData[key] = value;
        }
      };

      console.log("data", data);

      appendIfNotNull("full_name", data.full_name);
      appendIfNotNull("password", data.password);
      appendIfNotNull("phone_number", data.phone_number);
      appendIfNotNull("website_url", data.website_url);
      appendIfNotNull("password", data.password);
      appendIfNotNull("company_description", data.company_description);
      appendIfNotNull("next_visit_day", data.next_visit_day);
      appendIfNotNull("visit_number", data.visit_number);
      appendIfNotNull("first_transaction_date", data.first_transaction_date);
      appendIfNotNull("status", data.status.value);
      appendIfNotNull("delegate_id", data?.delegate_id?.value);
      appendIfNotNull("bound_total", data.bound_total);
      appendIfNotNull("address", data.address);
      appendIfNotNull("email", data.email);
      appendIfNotNull("company_name", data.company_name);
      appendIfNotNull("client_group_id", data.client_group_id?.value);
      appendIfNotNull("location_id", data.location_id?.value);
      appendIfNotNull("city_id", data.city_id?.value);
      appendIfNotNull("state_id", data.state_id?.value);
      appendIfNotNull("location_lat", data.latitude);
      appendIfNotNull("location_lng", data.longitude);

      if (images?.length > 0) {
        appendIfNotNull("image", images[0]?.file); // Assuming you have a file input and it's in `data.file`
      }

      const response = await clientsAPis.update({
        formData: jsonData,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const formData = new FormData();

      // Helper function to conditionally append values to formData
      const appendIfNotNull = (key, value) => {
        if (value !== null && value !== undefined && value !== "") {
          formData.append(key, value);
        }
      };

      // Append fields conditionally
      appendIfNotNull("full_name", data.full_name);
      appendIfNotNull("password", data.password);
      appendIfNotNull("phone_number", data.phone_number);
      appendIfNotNull("website_url", data.website_url);
      appendIfNotNull("company_description", data.company_description);
      appendIfNotNull("first_transaction_date", data.first_transaction_date);
      appendIfNotNull("next_visit_day", data.next_visit_day);
      appendIfNotNull("visit_number", data.visit_number);
      appendIfNotNull("status", data.status.value);
      appendIfNotNull("delegate_id", data?.delegate_id?.value);
      appendIfNotNull("bound_total", data.bound_total);
      appendIfNotNull("address", data.address);
      appendIfNotNull("email", data.email);
      appendIfNotNull("password", data.password);
      appendIfNotNull("company_name", data.company_name);
      appendIfNotNull("client_group_id", data.client_group_id?.value);
      appendIfNotNull("location_id", data.location_id?.value);
      appendIfNotNull("city_id", data.city_id?.value);
      appendIfNotNull("state_id", data.state_id?.value);
      appendIfNotNull("location_lat", data.latitude);
      appendIfNotNull("location_lng", data.longitude);

      if (images?.length > 0) {
        formData.append("image", images[0]?.file); // Assuming you have a file input and it's in `data.file`
      }
      const response = await clientsAPis.add(formData);
      toastr.success(response.message);
      navigate(-1);
      reset(); // Reset form after successful submission
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  const handelCLoseModal = () => {
    setOpenAddModel(false);
  };

  const [isShowPassword, setIsShowPassword] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const togglePassword = () => {
    setIsShowPassword((prev) => !prev);
  };

  const getCurrentLocation = () => {
    if ("geolocation" in navigator) {
      setIsGettingLocation(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          setPosition([lat, lng]);
          setSelectedLocation([lat, lng]);
          setValue("latitude", lat);
          setValue("longitude", lng);
          setIsGettingLocation(false);
          toastr.success("تم تحديد موقعك الحالي بنجاح");
        },
        (error) => {
          console.error("Error getting location:", error);
          setIsGettingLocation(false);
          toastr.error("فشل في الحصول على الموقع الحالي");
        }
      );
    } else {
      toastr.error("متصفحك لا يدعم تحديد الموقع");
    }
  };

  const handleImageChange = (imageList) => {
    setImages(imageList);
  };

  const togglePreview = () => {
    setShowPreview(!showPreview);
  };

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("clients.clients")}
          titleOfSection={t("clients.clients")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
          titleLink="/clients"
          currentPageLink={pathname}
        />
        <Card
          style={{
            height: "80vh",
            overflowY: "auto",
            padding: 20,
            overflowX: "hidden",
          }}
        >
          <Row className="g-2">
            <form
              onSubmit={
                selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
              }
            >
              {isLoading ? (
                <div className="container-loading">
                  <ClipLoader color="#ddd" size={50} />
                </div>
              ) : (
                <>
                  <Row>
                    {fieldsNames.map((item) =>
                      item.showWhen && item.isSelect ? (
                        <Col
                          xs={12}
                          md={isShow ? 2 : 3}
                          className="mb-3"
                          key={item.id}
                        >
                          <div className="d-flex gap-2 align-items-center w-100">
                            <CustomSelect
                              name={item.name}
                              control={control}
                              options={item.options}
                              label={item.label}
                              isDisabled={isShow}
                              error={item.error}
                              className="flex-grow-1"
                            />
                            {item.additionalElement}
                          </div>
                        </Col>
                      ) : (
                        item.showWhen && (
                          <Col
                            xs={12}
                            md={isShow ? 2 : 3}
                            className="mb-3"
                            key={item.id}
                          >
                            <CustomInput
                              name={item.name}
                              control={control}
                              label={item.label}
                              type={item.type}
                              isDisabled={isShow}
                              error={item.error}
                              className="w-100"
                            />
                          </Col>
                        )
                      )
                    )}
                    <Col xs={12} md={isShow ? 2 : 3} className="mb-3">
                      <CustomSelect
                        name="status"
                        control={control}
                        options={statsusOption}
                        label={t("common.status")}
                        isDisabled={isShow}
                        className="w-100"
                      />
                    </Col>
                    <Col xs={12} md={isShow ? 2 : 3} className="mb-3">
                      <CustomInput
                        name="bound_total"
                        control={control}
                        label={t("clients.voucher_total")}
                        type="number"
                        isDisabled={isShow}
                        className="w-100"
                      />
                    </Col>
                    <Col xs={12} md={isShow ? 2 : 3} className="mb-3">
                      <CustomSelect
                        name="delegate_id"
                        control={control}
                        options={delegates?.result?.map((item) => ({
                          label: item.full_name,
                          value: item.id,
                        }))}
                        label={t("common.delegate")}
                        isDisabled={isShow}
                        className="w-100"
                      />
                    </Col>

                    <Col md={isShow ? 2 : 3} xs={12} className="mb-3">
                      <CustomInput
                        name="password"
                        control={control}
                        error={errors.password}
                        isDisabled={isShow}
                        placeholder={t("common.password")}
                        type={isShowPassword ? "text" : "password"}
                        endIcon={
                          <div
                            style={{
                              height: "100%",
                              width: 20,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              cursor: "pointer",
                            }}
                            onClick={togglePassword}
                          >
                            {/* <i clas/sName=" ri-eye-off-line"></i> */}
                            {/* <FaRegEye size={18} /> */}
                            {!isShowPassword ? (
                              <FaEyeSlash size={13} />
                            ) : (
                              <FaRegEye size={13} />
                            )}
                          </div>
                        }
                      />
                    </Col>

                    <Col md={isShow ? 2 : 3} xs={12} className="mb-3">
                      <CustomInput
                        name="confirm_password"
                        control={control}
                        error={errors.confirm_password}
                        isDisabled={isShow}
                        placeholder={t("common.confirm_password")}
                        type={isShowPassword ? "text" : "password"}
                        endIcon={
                          <div
                            style={{
                              height: "100%",
                              width: 20,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              cursor: "pointer",
                            }}
                            onClick={togglePassword}
                          >
                            {/* <i className=" ri-eye-off-line"></i> */}
                            {/* <FaRegEye size={18} /> */}
                            {!isShowPassword ? (
                              <FaEyeSlash size={13} />
                            ) : (
                              <FaRegEye size={13} />
                            )}
                          </div>
                        }
                      />
                    </Col>
                  </Row>
                  <Row>
                    {" "}
                    {textAreaField.map((field) => (
                      <Col key={field.id} xs={6} className="mb-3">
                        <CustomInput
                          name="bound_total"
                          id={field.name}
                          control={control}
                          label={t("clients.voucher_total")}
                          type="text"
                          placeholder={field.label}
                          isDisabled={isShow}
                          className="w-100"
                          {...register(`${field.name}`)}
                        />
                      </Col>
                    ))}
                  </Row>

                  <Row>
                    <Col xs={6} className="mb-2">
                      <ImageUpload
                        images={images}
                        onChange={handleImageChange}
                        maxNumber={1}
                        isMultiple={false}
                        isDisabled={isShow}
                        onError={(error) =>
                          handleBackendErrors({ error, setError })
                        }
                        label={t("common.image")}
                      />
                    </Col>
                  </Row>

                  {/* Map Section */}
                  <Row className="mb-3">
                    <Col xs={12}>
                      <div className="d-flex justify-content-between align-items-center mb-2">
                        <label className="form-label" style={{ fontSize: 16 }}>
                          {t("common.map")}
                        </label>
                        {!isShow && (
                          <Button
                            type="button"
                            color="info"
                            size="sm"
                            onClick={getCurrentLocation}
                            disabled={isGettingLocation}
                          >
                            {isGettingLocation ? (
                              <ClipLoader color="#fff" size={15} />
                            ) : (
                              <i className="ri-map-pin-line me-1"></i>
                            )}
                            الموقع الحالي
                          </Button>
                        )}
                      </div>
                      {/* Coordinates Display
                      <Row className="mb-2">
                        <Col xs={6}>
                          <CustomInput
                            name="latitude"
                            control={control}
                            label="خط العرض (Latitude)"
                            type="number"
                            isDisabled={isShow}
                            placeholder="اختر موقع من الخريطة"
                            readOnly
                          />
                        </Col>
                        <Col xs={6}>
                          <CustomInput
                            name="longitude"
                            control={control}
                            label="خط الطول (Longitude)"
                            type="number"
                            isDisabled={isShow}
                            placeholder="اختر موقع من الخريطة"
                            readOnly
                          />
                        </Col>
                      </Row> */}
                      <div style={{ height: "50vh", width: "100%" }}>
                        <Map
                          center={position}
                          zoom={13}
                          attributionControl={false}
                          style={{ height: "100%", width: "100%" }}
                          onClick={handleMapClick}
                        >
                          <TileLayer
                            attribution='&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
                            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                          />
                          {selectedLocation &&
                            selectedLocation.length === 2 && (
                              <Marker position={selectedLocation}>
                                <Popup>الموقع المحدد</Popup>
                              </Marker>
                            )}
                        </Map>
                      </div>
                    </Col>
                  </Row>
                </>
              )}

              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <Button
                  type="button"
                  color="light"
                  onClick={handelCancel}
                  className="btn-sm "
                  style={{ height: "32px", width: "54px" }}
                >
                  {t("common.close")}
                </Button>
                {!isShow && (
                  <Button
                    color="primary"
                    className="btn-sm waves-effect waves-light primary-button"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <ClipLoader color="white" size={15} />
                    ) : selectId ? (
                      t("common.update")
                    ) : (
                      t("common.add")
                    )}
                  </Button>
                )}
              </div>
            </form>
          </Row>
        </Card>
      </Container>

      <AddLocationModel
        handelCLoseModal={handelCLoseModal}
        openModal={openAddModel}
        city_id={watch("city_id")?.value}
        refetch={refetch}
        setValue={setValue}
      />
    </div>
  );
};
export default ClientActions;
