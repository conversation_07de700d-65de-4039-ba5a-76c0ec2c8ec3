import ApiInstance from "../../constant/api-instance";
import { TASKS_ROUTES } from "./route";

const getAll = async ({ limit, page, searchParams }) => {
  const { data } = await ApiInstance.get(TASKS_ROUTES.TASKS, {
    params: {
      limit,
      page,
      ...searchParams,
    },
  });
  return data;
};
const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${TASKS_ROUTES.TASKS}/${id}`);
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.TASKS}`, {
    ...payload,
  });
  return data;
};

const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(`${TASKS_ROUTES.TASKS}/${id}`, {
    ...payload,
  });
  return data;
};

const cancel = async ({ id }) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.CANCEL}${id}`);
  return data;
};

const deleteFun = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${TASKS_ROUTES.TASKS}/${id}`);
  return data;
};

const approve = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.APPROVE}/${id}`, {
    ...payload,
  });
  return data;
};

const transfer = async ({ payload, id }) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.TRANSFER}/${id}`, {
    ...payload,
  });
  return data;
};

const done= async ({ payload, id }) => {
  const { data } = await ApiInstance.post(`${TASKS_ROUTES.DONE}/${id}`, {
    ...payload,
  });
  return data;
};

const transferList = async () => {
  const { data } = await ApiInstance.get(TASKS_ROUTES.TRANSFER_LIST);
  return data;
};

export const tasksAPis = {
  getAll,
  getOne,
  update,
  cancel,
  add,
  approve,
  transfer,
  deleteFun,
  transferList,
  done,
};
