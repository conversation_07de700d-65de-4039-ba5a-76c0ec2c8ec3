import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState, useEffect } from "react";
import toastr from "toastr";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { BillQueries } from "../../apis/bills/query";
import { billAPis } from "../../apis/bills/api";
import {
  formatDate,
  handleBackendErrors,
  hasPermission,
  today,
} from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";
import { Can } from "../../components/permissions-way/can";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import { delegateQueries } from "../../apis/delegate/query";
import { clientsQueries } from "../../apis/clients/query";
import DeleteModal from "../../components/Common/DeleteModal";
import StatusModal from "../../components/Common/StatusModal";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
import { MaintainsBillStatus } from "../../constant/constants";
import ClipLoader from "react-spinners/ClipLoader";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const Bills = () => {
  const [searchParams, setSearchParams] = useState({});
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const [dateError, setDateError] = useState(null);
  const { t, i18n } = useTranslation();

  const { control, watch, register, setValue, reset, handleSubmit } = useForm({
    defaultValues: {
      bill_type_ids: null,
      client_ids: null,
      delegate_ids: null,
      date_from: formatDate(today),
      date_to: formatDate(today),
      bill_number: "",
      notes: "",
    },
  });

  useEffect(() => {
    // Only apply default filter if no search params exist and no URL params
    if (Object.keys(searchParams).length === 0) {
      const todayFormatted = formatDate(today);
      const defaultParams = {
        "filter[bill_date][from]": todayFormatted,
        "filter[bill_date][to]": todayFormatted,
      };
      setSearchParams(defaultParams);
    }
  }, []);

  // Update pagination state - replace with separate states
  const [billsPagination, setBillsPagination] = useState(1);
  const [billsForDeletePagination, setBillsForDeletePagination] = useState({
    page: 1,
    limit: 10,
  });
  const [followUpPagination, setFollowUpPagination] = useState({
    page: 1,
    limit: 10,
  });

  // Validate date_from and date_to whenever they change
  useEffect(() => {
    const date_from = watch("date_from");
    const date_to = watch("date_to");

    if (date_from && date_to) {
      const fromDate = new Date(date_from);
      const toDate = new Date(date_to);

      if (fromDate > toDate) {
        setDateError(t("common.date_error"));
        toastr.error(
          t("common.date_error") || "Date from cannot be greater than date to"
        );
      } else {
        setDateError(null);
      }
    }
  }, [watch("date_from"), watch("date_to"), t]);

  // Manual search function with handleSubmit
  const handleSearch = handleSubmit((formData) => {
    // If date validation error exists, don't update search params
    if (dateError) {
      return;
    }

    const params = {};

    // Handle bill type multi-select
    if (formData.bill_type_ids) {
      if (Array.isArray(formData.bill_type_ids)) {
        if (formData.bill_type_ids.length > 0) {
          formData.bill_type_ids.forEach((type) => {
            if (!params["filter[bill_type_ids][]"]) {
              params["filter[bill_type_ids][]"] = [type.value];
            } else {
              params["filter[bill_type_ids][]"].push(type.value);
            }
          });
        }
      } else if (formData.bill_type_ids.value) {
        params["filter[bill_type_ids][]"] = [formData.bill_type_ids.value];
      }
    }

    // Handle client multi-select
    if (formData.client_ids) {
      if (Array.isArray(formData.client_ids)) {
        if (formData.client_ids.length > 0) {
          formData.client_ids.forEach((client) => {
            if (!params["filter[client_ids][]"]) {
              params["filter[client_ids][]"] = [client.value];
            } else {
              params["filter[client_ids][]"].push(client.value);
            }
          });
        }
      } else if (formData.client_ids.value) {
        params["filter[client_ids][]"] = [formData.client_ids.value];
      }
    }

    // Handle delegate multi-select
    if (formData.delegate_ids) {
      if (Array.isArray(formData.delegate_ids)) {
        if (formData.delegate_ids.length > 0) {
          formData.delegate_ids.forEach((delegate) => {
            if (!params["filter[delegate_ids][]"]) {
              params["filter[delegate_ids][]"] = [delegate.value];
            } else {
              params["filter[delegate_ids][]"].push(delegate.value);
            }
          });
        }
      } else if (formData.delegate_ids.value) {
        params["filter[delegate_ids][]"] = [formData.delegate_ids.value];
      }
    }

    // Date filters
    if (formData.date_from) {
      params["filter[bill_date][from]"] = formData.date_from;
    }
    if (formData.date_to) {
      params["filter[bill_date][to]"] = formData.date_to;
    }

    // Bill number
    if (formData.bill_number) {
      params["filter[bill_number]"] = formData.bill_number;
    }

    // notes
    if (formData.notes) {
      params["filter[notes]"] = formData.notes;
    }

    // Update search params
    setSearchParams(params);
    // Update URL with new filters
    updateUrlWithFilters(params);
    // Reset to first page
    setBillsPagination(1);
    // Trigger a refetch with new parameters
    refetch();
  });

  const handleReset = () => {
    // Reset form fields
    reset({
      bill_type_ids: null,
      client_ids: null,
      delegate_ids: null,
      date_from: "",
      date_to: "",
      bill_number: "",
      notes: "",
    });

    // Clear date validation errors
    setDateError(null);
    // Clear search params
    setSearchParams({});
    // Clear URL params
    navigate(pathname, { replace: true });
    // Reset pagination
    setBillsPagination(1);
    // Refetch data
    refetch();
  };

  // Function to update URL with search parameters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Delegate filter
    if (params["filter[delegate_ids]"]) {
      newUrl.set("delegate_ids", params["filter[delegate_ids]"]);
    }

    // Bill type filter
    if (params["filter[bill_type_ids]"]) {
      newUrl.set("bill_type_ids", params["filter[bill_type_ids]"]);
    }

    // Client filter
    if (params["filter[client_ids]"]) {
      newUrl.set("client_ids", params["filter[client_ids]"]);
    }

    // Bill number
    if (params["filter[bill_number]"]) {
      newUrl.set("bill_number", params["filter[bill_number]"]);
    }

    // Bill date
    if (params["filter[bill_date][from]"]) {
      newUrl.set("date_from", params["filter[date][from]"]);
    }
    if (params["filter[bill_date][to]"]) {
      newUrl.set("date_to", params["filter[date][to]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  const { data, isLoading, refetch } = BillQueries.useGetAllBills({
    limit: 10,
    page: billsPagination,
    searchParams,
  });
  const {
    data: billsForDelete,
    isLoading: isLodingBillsForDelete,
    refetch: refetchBillsForDelete,
  } = BillQueries.useGetAllListBillsForDelete({
    limit: billsForDeletePagination.limit,
    page: billsForDeletePagination.page,
  });
  const {
    data: followUp,
    isLoading: isLodingBillsFollowUp,
    refetch: refetchFollowUp,
  } = BillQueries.useGetAllFollowUpList({
    limit: followUpPagination.limit,
    page: followUpPagination.page,
  });

  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setOpenStatsusModal(false);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);

  const [selectedStatus, setSelectedStatus] = useState(0);

  const handelSelectId = (id) => {
    setSelectId(id);
  };
  const [open, setOpen] = useState(false);
  const [openStatus, setOpenStatus] = useState(false);

  const handelAddBonds = () => {
    // navigate("/action-bills");
    setOpen(true);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const [selectedType, setSelectedType] = useState(() => {
    const savedTab = localStorage.getItem("bills_selected_tab");
    return savedTab !== null ? Number(savedTab) : 0;
  });

  // Handle tab switching with page reset
  const handelSelectedType = (name) => {
    setSelectedType(name);
    localStorage.setItem("bills_selected_tab", name);
    setBillsPagination(1);
  };

  const columns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_number"),
      accessor: "bill_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_date"),
      accessor: "bill_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_type"),
      accessor: "bill_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.client"),
      accessor: "client",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.response_name"),
      accessor: "res_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-center">
            <Can permission={"bill.update"}>
              <Link
                to={
                  // cellProps.bill_type_number === 7
                  //   ? `/action-bills-contract?id=${cellProps.id}`
                  //   : cellProps.bill_type_number === 9
                  //   ? `/action-bills-sample?id=${cellProps.id}`
                  //   : cellProps.bill_type_number === 4 ||
                  //     cellProps.bill_type_number === 3
                  //   ? `/action-bills-pick-up?id=${cellProps.id}`
                  //   : `/action-bills?id=${cellProps.id}`
                  `/action-sales-bills?id=${cellProps.id}`
                }
                className="text-primary"
                onClick={() => {}}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </Link>
            </Can>
            <Can permission={"bill.destroy"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
            {cellProps.bill_type_number === 3 && (
              <Can permission={"bill.change_maintenance_status"}>
                <div
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    handelStatusOpen();
                    handelSelectId(cellProps.id);
                    setSelectedStatus(cellProps.maintenance_status);
                  }}
                  className="text-danger"
                >
                  {/* <i className="ri-exchange-line font-size-16"></i> */}
                  <FaInfoCircle size={14} />
                </div>
              </Can>
            )}
            <Can permission={"bill.show"}>
              <Link
                to={
                  // cellProps.bill_type_number === 7
                  //   ? `/action-bills-contract?id=${cellProps.id}?Show=true`
                  //   : cellProps.bill_type_number === 9
                  //   ? `/action-bills-sample?id=${cellProps.id}?Show=true`
                  //   : cellProps.bill_type_number === 4 ||
                  //     cellProps.bill_type_number === 3
                  //   ? `/action-bills-pick-up?id=${cellProps.id}?Show=true`
                  //   : `/action-bills?id=${cellProps.id}?Show=true`
                  `/action-sales-bills?id=${cellProps.id}?Show=true`
                }
                className="text-success"
              >
                <i className=" ri-information-fill font-size-16"></i>
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const listForDeleteColumns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_number"),
      accessor: "bill_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_date"),
      accessor: "bill_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_type"),
      accessor: "bill_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.client"),
      accessor: "client",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.response_name"),
      accessor: "res_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-center">
            <Can permission={"bill.for_delete.delete"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);
  const followUpLlistColumns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_number"),
      accessor: "bill_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_date"),
      accessor: "bill_date",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.bill_type"),
      accessor: "bill_type",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.client"),
      accessor: "client",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.response_name"),
      accessor: "res_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.total"),
      accessor: "total",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <div className="d-flex align-items-center gap-2 justify-content-center">
            <Can permission={"follow_up_log.destroy"}>
              <Link
                to={`/follow-up-bills/?id=${cellProps.id}?Show=true`}
                className="text-success"
              >
                {/* <i className=" ri-information-fill font-size-16"></i> */}
                <FaInfoCircle size={14} />
              </Link>
            </Can>
            <Can permission={"follow_up_log.show"}>
              <Link
                onClick={() => {
                  if (cellProps.isDefault !== 1) {
                    handelOpenModal();
                    handelSelectId(cellProps.id);
                  }
                }}
                to="#"
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ]);

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (billsPagination - 1) * 10 + index + 1, // Corrected key
              client: item?.client?.company_name || "----",
              res_name: item.res_name || "----",
              bill_number: <div dir="ltr">{item.bill_number}</div>,
              delegate: item?.delegate?.full_name || "----",
              bill_date: item.bill_date.split(" ")[0],
              total: item.total || "----",
              bill_type:
                i18n.language === "en"
                  ? item.bill_type?.title?.en || "---"
                  : item.bill_type?.title?.ar || "---",
              bill_type_number: item.bill_type?.type,
              maintenance_status: item.maintenance_status,
            }))
            .reverse()
        : [],
    [data?.result, i18n?.language, billsPagination.page]
  );

  const listForDeleteRowData = useMemo(
    () =>
      billsForDelete?.result?.length > 0
        ? billsForDelete.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (billsForDeletePagination.page - 1) * 10 + index + 1, // 10 is your page size
              client: item?.client?.company_name || "----",
              res_name: item.res_name || "----",
              bill_number: <div dir="ltr"> {item.bill_number}</div>,
              delegate: item?.delegate?.full_name || "----",
              total: item.total || "----",
              bill_date: item.bill_date.split(" ")[0],
              // bill_type: t(`bills.${type[item?.bill_type?.type]}`) || "---",
              bill_type:
                i18n.language === "en"
                  ? item.bill_type?.title?.en || "---"
                  : item.bill_type?.title?.ar || "---",
              bill_type_number: item.bill_type?.type,
            }))
            .reverse()
        : [],
    [billsForDelete?.result, i18n?.language, billsForDeletePagination.page]
  );
  const followUpListRowData = useMemo(
    () =>
      followUp?.result?.length > 0
        ? followUp.result
            .map((item, index) => ({
              id: item.id, // Incremental ID starting from 1
              id_toShow: (followUpPagination.page - 1) * 10 + index + 1, // 10 is your page size
              client: item?.client?.company_name || "----",
              res_name: item.res_name || "----",
              bill_number: <div dir="ltr"> {item.bill_number}</div>,
              delegate: item?.delegate || "----",
              total: item.total || "----",
              bill_date: item.bill_date.split("T")[0],
              // bill_type: t(`bills.${type[item?.bill_type?.type]}`) || "---",
              bill_type:
                i18n.language === "en"
                  ? item.bill_type?.title?.en || "---"
                  : item.bill_type?.title?.ar || "---",
              bill_type_number: item.bill_type?.type,
            }))
            .reverse()
        : [],
    [followUp?.result, i18n?.language, followUpPagination.page]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const respons = await billAPis.deleteBill({
        id: selectId,
      });
      refetch();
      toastr.success(respons.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
    // Call API with selected permissions (data.permissions)
  };

  const DeleteFollowUpFun = async () => {
    try {
      setIsDeleting(true);
      const response = await billAPis.deleteFollowUp({
        id: selectId,
      });
      refetch();
      refetchFollowUp();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toa?str.error("There are error");
      handleBackendErrors({ error });

      // console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const DeleteListForDelet = async () => {
    try {
      setIsDeleting(true);
      const response = await billAPis.deleteForDelete({
        id: selectId,
      });
      refetchBillsForDelete();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
    // Call API with selected permissions (data.permissions)
  };

  const tabsList = [
    { id: 0, title: t("bills.bills"), permissions: "bill.index" },
    {
      id: 1,
      title: t("bills.bills_delete"),
      permissions: "bill.for_delete.index",
    },
    { id: 2, title: t("bills.follow_up"), permissions: "follow_up_log.index" },
  ];

  // Handle tab selection when permissions change
  useEffect(() => {
    const filteredTabs = tabsList.filter((item) =>
      hasPermission(item.permissions)
    );
    if (filteredTabs.length > 0) {
      const currentTab = tabsList[selectedType];
      if (!currentTab || !hasPermission(currentTab.permissions)) {
        const firstAvailableTab = filteredTabs[0];
        if (firstAvailableTab) {
          handelSelectedType(firstAvailableTab.id);
        }
      }
    }
  }, [selectedType]);

  const Tabs = () => {
    // Filter tabs based on permissions
    const filteredTabs = tabsList.filter((item) =>
      hasPermission(item.permissions)
    );

    // If no tabs have permission, return null
    if (filteredTabs.length === 0) {
      return null;
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: 10,
        }}
      >
        {filteredTabs.map((item) => (
          <p
            style={{
              background: item.id === selectedType ? "rgb(28 187 140)" : "",
              color: item.id === selectedType ? "#fff" : "",
              paddingInline: 20,
              paddingBlock: 10,
              borderRadius: 40,
              border: "1px solid #ccc",
              cursor: "pointer",
            }}
            onClick={() => handelSelectedType(item.id)}
            key={item.id}
          >
            {item.title}
          </p>
        ))}
      </div>
    );
  };

  // Options for selects
  const { data: bill_types } = billTypesQueries.useGetAll({
    searchParams: { status: 1 },
  });

  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const DelegateOptions = useSetSelectOptions({
    data: delegates?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  const billTypesOptions = useSetSelectOptions({
    data: bill_types?.result,
    getOption: (item) => ({
      label: i18n.language === "en" ? item.title.en : item.title.ar,
      value: item.id,
    }),
  });

  const clientsOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: item.full_name ? item.full_name : "" + "/" + item.company_name,
      value: item.id,
    }),
  });

  const SearchData = [
    {
      id: 0,
      label: t("reports.Bills_type"),
      type: "select",
      name: "bill_type_ids",
      options: billTypesOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 1,
      label: t("common.client"),
      type: "select",
      name: "client_ids",
      options: clientsOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
    {
      id: 2,
      label: t("common.delegate"),
      type: "select",
      name: "delegate_ids",
      options: DelegateOptions,
      isMulti: true,
      cols: 2,
      component: CustomFilterSearch,
      hasButtonSearch: true,
    },
  ];

  const inputsArray = [
    {
      id: 0,
      name: "bill_number",
      type: "number",
      label: t("bills.bill_number"),
      cols: 2,
    },
    {
      id: 1,
      name: "date_from",
      type: "date",
      label: t("common.date_from"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
    {
      id: 2,
      name: "date_to",
      type: "date",
      label: t("common.date_to"),
      cols: 2,
      error: dateError ? { message: dateError } : null,
    },
    {
      id: 3,
      name: "notes",
      type: "text",
      label: t("common.notes"),
      cols: 2,
    },
  ];

  const handelOPen = () => {
    setOpen(!open);
  };

  const handelStatusOpen = () => {
    setOpenStatus(!open);
  };

  // const types = [
  //   { label: t("types.bill.sales_bill"), value: 6 },
  //   { label: t("types.bill.return_bill"), value: 2 },
  //   { label: t("types.bill.change_oil"), value: 5 },
  //   { label: t("types.bill.contract_bill"), value: 7 },
  //   { label: t("types.bill.pickup_maintenance_bill"), value: 3 },
  //   { label: t("types.bill.deliver_maintenance_bill"), value: 4 },
  //   { label: t("types.bill.sample_bill"), value: 1 },
  //   { label: t("types.bill.return_sample_bill"), value: 9 },
  // ];

  // const {}=
  const { data: bilTypes } = billTypesQueries.useGetAll({});

  const types =
    bilTypes?.result
      ?.map((item) => ({
        label: i18n.language === "en" ? item.title?.en : item.title?.ar,
        value: item.id,
        type: item.type,
      }))
      .filter((item) => item.type !== 8) || [];

  const hadnelChangeStatus = async () => {
    try {
      setIsDeleting(true);
      const response = await billAPis.changeStatus({ id: selectId });
      handelStatusOpen();
      setSelectId(0);
      setSelectedStatus(0);
      setOpenStatus(false);
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
    }
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("bills.bills")}
          canPermission={"bill.store"}
          handleOrderClicks={handelAddBonds}
          disabledAddTitle={false}
          addTitle={t("common.add") + " " + t("bills.bills")}
          isAddOptions={true}
        />
        <Card style={{ height: "80vh", padding: 20 }}>
          <div className="w-100">
            <SearchCard
              SearchData={SearchData}
              control={control}
              hadelReset={handleReset}
              inputsArray={inputsArray}
              register={register}
              handelSearch={handleSearch}
              watch={watch}
              setValue={setValue}
              hasButtonSearch={true}
              customHeight={"100%"}
            />
            <Tabs />
          </div>

          {/* Bills Table - Tab 0 */}
          {selectedType === 0 && (
            <TableContainer
              hideSHowGFilter={false}
              columns={columns}
              data={rowData || []}
              setPage={setBillsPagination}
              pageCount={data?.meta?.last_page || 1}
              currentPage={billsPagination}
              isLoading={isLoading}
              customHeight={"100%"}
            />
          )}

          {/* Bills For Delete Table - Tab 1 */}
          {selectedType === 1 && (
            <TableContainer
              hideSHowGFilter={false}
              columns={listForDeleteColumns}
              data={listForDeleteRowData || []}
              customHeight={"100%"}
              pageCount={
                billsForDelete?.meta?.last_page
                  ? parseInt(billsForDelete.meta.last_page)
                  : 1
              }
              currentPage={billsForDeletePagination.page}
              setPage={(newPage) => {
                const validPage =
                  isNaN(newPage) || newPage < 1 ? 1 : parseInt(newPage);
                setBillsForDeletePagination((prev) => ({
                  ...prev,
                  page: validPage,
                }));
                // Refetch with updated page
                refetchBillsForDelete({
                  limit: billsForDeletePagination.limit,
                  page: validPage,
                });
              }}
              isLoading={isLodingBillsForDelete}
            />
          )}

          {/* Follow Up Table - Tab 2 */}
          {selectedType === 2 && (
            <TableContainer
              hideSHowGFilter={false}
              columns={followUpLlistColumns}
              data={followUpListRowData || []}
              pageCount={
                followUp?.meta?.last_page
                  ? parseInt(followUp.meta.last_page)
                  : 1
              }
              customHeight={"100%"}
              currentPage={followUpPagination.page}
              setPage={(newPage) => {
                const validPage =
                  isNaN(newPage) || newPage < 1 ? 1 : parseInt(newPage);
                setFollowUpPagination((prev) => ({
                  ...prev,
                  page: validPage,
                }));
                // Refetch with updated page
                refetchFollowUp({
                  limit: followUpPagination.limit,
                  page: validPage,
                });
              }}
              isLoading={isLodingBillsFollowUp}
            />
          )}
          {/* </CardBody> */}
        </Card>
        <DeleteModal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          onDelete={
            selectedType === 2
              ? DeleteFollowUpFun
              : selectedType === 1
              ? DeleteListForDelet
              : DeleteFun
          }
          itemName={t("bills.bills")}
          isDeleting={isDeleting}
        />
        {openStatsusModal && selectId && (
          <StatusModal
            isOpen={openStatsusModal}
            toggle={handelCLoseModal}
            onConfirm={() => {}}
            title="common.Attention"
            isLoading={isDeleting}
          />
        )}

        <Modal isOpen={open} toggle={handelOPen} backdrop="static">
          <ModalHeader toggle={handelOPen}>
            {t("common.select") + " " + t("reports.Bills_type")}
          </ModalHeader>
          <ModalBody>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: 10,
              }}
            >
              {types.map((item) => (
                <div xs={4} className="w-100" key={item.value}>
                  <Button
                    type="button"
                    color="primary"
                    className="rounded-1 mb-2 me-2 w-100 btn-sm p-2"
                    onClick={() => {
                      // if (item.type === 7) {
                      //   navigate(`/action-bills-contract?type=${item.type}`);
                      //   return;
                      // }
                      // if (item.type === 9) {
                      //   navigate(`/action-bills-sample?type=${item.type}`);
                      // } else {
                      //   item.type === 4 || item.type === 3
                      //     ? navigate(`/action-bills-pick-up?type=${item.type}`)
                      //     : navigate(`/action-bills?type=${item.type}`);
                      // }
                      navigate("/action-sales-bills?type=6");
                    }}
                    // disabled={item?.disable}
                  >
                    {item.label}
                  </Button>
                </div>
              ))}
            </div>
          </ModalBody>
        </Modal>
        <Modal
          isOpen={openStatus}
          toggle={() => setOpenStatus(false)}
          backdrop="static"
        >
          <ModalHeader toggle={() => setOpenStatus(false)}>
            {t("bills.change_maintenance_status")}
          </ModalHeader>
          <ModalBody>
            <p>
              {(() => {
                switch (selectedStatus) {
                  case MaintainsBillStatus.RECEIVED_FROM_CLIENT:
                    return t("bills.transfer_to_maintenance");
                  case MaintainsBillStatus.RECEIVED_BY_TECHNICIAN:
                    return t("bills.maintenance_done");
                  case MaintainsBillStatus.FINISHED_BY_TECHNICIAN:
                    return t("bills.move_to_delegate");
                  case MaintainsBillStatus.DELIVERED_TO_DELEGATE:
                    return t("bills.products_with_delegate");
                  case MaintainsBillStatus.DELIVERED_TO_CUSTOMER:
                    return t("bills.customer_was_delevier");
                  default:
                    return t("bills.customer_was_delevier");
                }
              })()}
            </p>
          </ModalBody>
          {selectedStatus !== MaintainsBillStatus.DELIVERED_TO_DELEGATE &&
            selectedStatus !== MaintainsBillStatus.DELIVERED_TO_CLIENT && (
              <ModalFooter>
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={() => setOpenStatus(false)}
                >
                  {t("common.no")}
                </Button>
                <Button
                  onClick={hadnelChangeStatus}
                  disabled={isDeleting}
                  type="button"
                  color="primary"
                  className="btn-sm"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            )}
        </Modal>
      </Container>
    </div>
  );
};
export default Bills;
