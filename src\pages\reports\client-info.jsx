import { useTranslation } from "react-i18next";
import { Card, CardBody, Container, But<PERSON> } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useMemo, useState, useCallback, useEffect } from "react";
import TableContainer from "../../components/Common/TableContainer";
import { reportsQueries } from "../../apis/reports/query";
import ColumnVisibilityModal from "../../components/Reports/visibality-column";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";
import { billTypesQueries } from "../../apis/types/bill-type/query";
import { delegateQueries } from "../../apis/delegate/query";
import { clientsQueries } from "../../apis/clients/query";
import { citiesQueries } from "../../apis/cities/query";
import { locationQueries } from "../../apis/locations/query";
import { clientsGroupsQueries } from "../../apis/client-group/query";
import { productQueries } from "../../apis/products/query";
import { useLocation, useNavigate } from "react-router-dom";
import CustomFilterSearch from "../../components/Common/CustomFilterSearch";
//import SearchToolbar from "../../components/Reports/searchCard";

// Move ColumnVisibilityModal outside of ClientInf

const ClientInfo = () => {
  const { t, i18n } = useTranslation();
  const locationHook = useLocation();
  const navigate = useNavigate();
  const { pathname } = locationHook;
  const queryParams = new URLSearchParams(locationHook.search);

  const breadcrumbItems = [
    { title: t("reports.clients_info"), link: "/client-info" },
  ];
  const [searchParams, setSearchParams] = useState({});
  const [pagination, setPagination] = useState(1);

  // Fetch data first
  const { data: bill_types } = billTypesQueries.useGetAll({});
  const { data: delegate } = delegateQueries.useGetAll({ status: 1 });
  const { data: client } = clientsQueries.useGetAll({ status: 1 });
  const { data: city } = citiesQueries.useGetAll({});
  const { data: locations } = locationQueries.useGetAll({});
  const { data: client_group } = clientsGroupsQueries.useGetAll({ status: 1 });
  const { data: product } = productQueries.useGetAll({status:1});
  const { data: product_types } = productQueries.useGetAllProductTypes({
    status: 1,
  });

  // Function to parse URL parameters
  function parseUrlParams() {
    try {
      const params = {};
      const initialFormValues = {
        delegate: null,
        type: null,
        client_group: null,
        bill: null,
        city: null,
        locations: null,
        product_type: null,
        product_name: null,
        bill_date: "",
        search: "",
        sales_filter: "",
      };

      // Parse delegate
      const delegateId = queryParams.get("delegate");
      if (delegateId && delegate?.result) {
        params["filter[delegate]"] = delegateId;
        const delegateOption = delegate.result.find(
          (d) => d.id.toString() === delegateId
        );
        if (delegateOption) {
          initialFormValues.delegate = {
            label: delegateOption.full_name,
            value: delegateOption.id,
          };
        }
      }

      // Parse client
      const clientId = queryParams.get("client");
      if (clientId && client?.result) {
        params["filter[type]"] = clientId;
        const clientOption = client.result.find(
          (c) => c.id.toString() === clientId
        );
        if (clientOption) {
          initialFormValues.type = {
            label: clientOption.full_name,
            value: clientOption.id,
          };
        }
      }

      // Parse client group
      const clientGroupId = queryParams.get("client_group");
      if (clientGroupId && client_group?.result) {
        params["filter[client_group]"] = clientGroupId;
        const clientGroupOption = client_group.result.find(
          (cg) => cg.id.toString() === clientGroupId
        );
        if (clientGroupOption) {
          initialFormValues.client_group = {
            label: clientGroupOption.group_title,
            value: clientGroupOption.id,
          };
        }
      }

      // Parse bill type
      const billTypeId = queryParams.get("bill");
      if (billTypeId && bill_types?.result) {
        params["filter[bill]"] = billTypeId;
        const billTypeOption = bill_types.result.find(
          (bt) => bt.id.toString() === billTypeId
        );
        if (billTypeOption) {
          initialFormValues.bill = {
            label:
              i18n.language === "en"
                ? billTypeOption.title.en
                : billTypeOption.title.ar,
            value: billTypeOption.id,
          };
        }
      }

      // Parse city
      const cityId = queryParams.get("city");
      if (cityId && city?.result) {
        params["filter[city]"] = cityId;
        const cityOption = city.result.find((c) => c.id.toString() === cityId);
        if (cityOption) {
          initialFormValues.city = {
            label: cityOption.name,
            value: cityOption.id,
          };
        }
      }

      // Parse location
      const locationId = queryParams.get("locations");
      if (locationId && locations?.result) {
        params["filter[locations]"] = locationId;
        const locationOption = locations.result.find(
          (l) => l.id.toString() === locationId
        );
        if (locationOption) {
          initialFormValues.locations = {
            label: locationOption.name,
            value: locationOption.id,
          };
        }
      }

      // Parse product type
      const productTypeId = queryParams.get("product_type");
      if (productTypeId && product_types?.result) {
        params["filter[product_type]"] = productTypeId;
        const productTypeOption = product_types.result.find(
          (pt) => pt.id.toString() === productTypeId
        );
        if (productTypeOption) {
          initialFormValues.product_type = {
            label:
              i18n.language === "en"
                ? productTypeOption.title.en
                : productTypeOption.title.ar,
            value: productTypeOption.id,
          };
        }
      }

      // Parse product
      const productId = queryParams.get("product_name");
      if (productId && product?.result) {
        params["filter[product_name]"] = productId;
        const productOption = product.result.find(
          (p) => p.id.toString() === productId
        );
        if (productOption) {
          initialFormValues.product_name = {
            label: productOption.name,
            value: productOption.id,
          };
        }
      }

      // Parse sales filter
      const salesFilter = queryParams.get("sales_filter");
      if (salesFilter) {
        params["filter[sales_filter]"] = salesFilter;
        initialFormValues.sales_filter = salesFilter;
      }

      // Parse bill date
      const billDate = queryParams.get("bill_date");
      if (billDate) {
        params["filter[bill_date]"] = billDate;
        initialFormValues.bill_date = billDate;
      }

      // Parse search term
      const search = queryParams.get("search");
      if (search) {
        params["filter[global_search]"] = search;
        initialFormValues.search = search;
      }

      return { params, initialFormValues };
    } catch (error) {
      console.error("Error parsing URL parameters:", error);
      return {
        params: {},
        initialFormValues: {
          delegate: null,
          type: null,
          client_group: null,
          bill: null,
          city: null,
          locations: null,
          product_type: null,
          product_name: null,
          bill_date: "",
          search: "",
          sales_filter: "",
        },
      };
    }
  }

  // Get initial params after data is loaded
  const [initialParamsLoaded, setInitialParamsLoaded] = useState(false);
  const [initialParams, setInitialParams] = useState({});
  const [initialFormValues, setInitialFormValues] = useState({
    delegate: null,
    type: null,
    client_group: null,
    bill: null,
    city: null,
    locations: null,
    product_type: null,
    product_name: null,
    bill_date: "",
    search: "",
    sales_filter: "",
  });

  // Initialize form with default values
  const {
    control,
    reset,
    formState: { isSubmitting },
    register,
    handleSubmit,
    watch,
    setValue,
    getValues,
  } = useForm({
    defaultValues: initialFormValues,
  });

  // Load initial params after data is loaded
  useEffect(() => {
    // Only run once when the data is available
    if (
      !initialParamsLoaded &&
      delegate?.result &&
      client?.result &&
      client_group?.result &&
      bill_types?.result &&
      city?.result &&
      locations?.result &&
      product_types?.result &&
      product?.result
    ) {
      const { params, initialFormValues } = parseUrlParams();
      setInitialParams(params);
      setInitialFormValues(initialFormValues);
      setSearchParams(params);
      reset(initialFormValues);
      setInitialParamsLoaded(true);
    }
  }, [
    delegate?.result,
    client?.result,
    client_group?.result,
    bill_types?.result,
    city?.result,
    locations?.result,
    product_types?.result,
    product?.result,
    initialParamsLoaded,
  ]);

  const { data, isLoading } = reportsQueries.useGetClientsInfo({
    searchParams,
  });

  // Function to update URL with search parameters
  const updateUrlWithFilters = (params) => {
    const newUrl = new URLSearchParams();

    // Delegate filter
    if (params["filter[delegate]"]) {
      newUrl.set("delegate", params["filter[delegate]"]);
    }

    // Client filter
    if (params["filter[type]"]) {
      newUrl.set("client", params["filter[type]"]);
    }

    // Client group filter
    if (params["filter[client_group]"]) {
      newUrl.set("client_group", params["filter[client_group]"]);
    }

    // Bill type filter
    if (params["filter[bill]"]) {
      newUrl.set("bill", params["filter[bill]"]);
    }

    // City filter
    if (params["filter[city]"]) {
      newUrl.set("city", params["filter[city]"]);
    }

    // Location filter
    if (params["filter[locations]"]) {
      newUrl.set("locations", params["filter[locations]"]);
    }

    // Product type filter
    if (params["filter[product_type]"]) {
      newUrl.set("product_type", params["filter[product_type]"]);
    }

    // Product filter
    if (params["filter[product_name]"]) {
      newUrl.set("product_name", params["filter[product_name]"]);
    }

    // Sales filter
    if (params["filter[sales_filter]"]) {
      newUrl.set("sales_filter", params["filter[sales_filter]"]);
    }

    // Bill date filter
    if (params["filter[bill_date]"]) {
      newUrl.set("bill_date", params["filter[bill_date]"]);
    }

    // General search term
    if (params["filter[global_search]"]) {
      newUrl.set("search", params["filter[global_search]"]);
    }

    // Replace current URL without reloading the page
    navigate(`${pathname}?${newUrl.toString()}`, { replace: true });
  };

  const handleSearch = handleSubmit((data) => {
    const params = {};

    // Handle select fields (react-select format)
    Object.keys(data).forEach((key) => {
      if (key === "search") {
        if (data[key]) {
          params["filter[global_search]"] = data[key];
        }
      } else if (data[key]?.value) {
        // For single select
        params[`filter[${key}]`] = data[key].value;
      } else if (Array.isArray(data[key])) {
        // For multi-select
        params[`filter[${key}]`] = data[key]
          .map((item) => item.value)
          .join(",");
      } else if (data[key]) {
        // For regular inputs
        params[`filter[${key}]`] = data[key];
      } else if (data[key] === "product_name") {
        params[`filter[${key}]`] = data[key].label;
      }
    });

    // Update search params and trigger refetch
    setSearchParams(params);
    // Update URL with filters
    updateUrlWithFilters(params);
    setPagination(1); // Reset to first page when searching
  });

  const handleReset = () => {
    reset({
      delegate: null,
      type: null,
      client_group: null,
      bill: null,
      city: null,
      locations: null,
      product_type: null,
      product_name: null,
      bill_date: "",
      search: "",
      sales_filter: "",
    });
    setSearchParams({});
    // Update URL to clear filters
    navigate(pathname, { replace: true });
    setPagination(1);
  };

  // State for column visibility modal
  const [columnModalOpen, setColumnModalOpen] = useState(false);

  // Initialize all columns as visible
  const [visibleColumns, setVisibleColumns] = useState({
    id_toShow: true,
    date: true,
    operation_type: true,
    delegate: true,
    client: true,
    city: true,
    location: true,
    product_name: true,
    quant: true,
    gift: true,
    price: true,
    total: true,
    notes: true,
    client_group: true,
    res_number: true,
    product_type: true,
    symbol: true,
    product_unit: true,
  });

  // Toggle column visibility
  const toggleColumnVisibility = useCallback((columnId) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [columnId]: !prev[columnId],
    }));
  }, []);

  // Toggle modal
  const toggleColumnModal = useCallback(() => {
    setColumnModalOpen((prev) => !prev);
  }, []);

  const columns = useMemo(
    () => [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.id_toShow,
      },
      {
        Header: t("common.operation_date"),
        accessor: "date",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.date,
      },
      {
        Header: t("common.operation_type"),
        accessor: "operation_type",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.operation_type,
      },
      {
        Header: t("common.delegate_name"),
        accessor: "delegate",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.delegate,
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.client,
      },
      {
        Header: t("common.city"),
        accessor: "city",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.city,
      },
      {
        Header: t("common.location"),
        accessor: "location",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.location,
      },
      {
        Header: t("common.product"),
        accessor: "product_name",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.product_name,
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.quant,
      },
      {
        Header: t("common.gift"),
        accessor: "gift",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.gift,
      },
      {
        Header: t("common.total_price_unit"),
        accessor: "price",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.price,
      },
      {
        Header: t("common.total_price"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.total,
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.notes,
      },
      {
        Header: t("clients.client_group"),
        accessor: "client_group",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.client_group,
      },
      {
        Header: t("reports.res_number"),
        accessor: "res_number",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.res_number,
      },
      {
        Header: t("types.product_types.product_type"),
        accessor: "product_type",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.product_type,
      },
      {
        Header: t("reports.symbol"),
        accessor: "symbol",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.symbol,
      },
      {
        Header: t("products.product_unit"),
        accessor: "product_unit",
        disableFilters: true,
        filterable: false,
        isVisible: visibleColumns.product_unit,
      },
    ],
    [visibleColumns, t]
  );

  // Filter columns based on visibility
  const visibleColumnsArray = useMemo(
    () => columns.filter((column) => column.isVisible),
    [columns]
  );

  const rowData = useMemo(
    () =>
      data?.result?.length > 0
        ? data.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              date: item.date?.split(" ")[0] || "----",
              operation_type: item.bill_number || "----",
              delegate: item.delegate || "----",
              client: item.client || "----",
              city: item.city || "----",
              location: item.location || "----",
              product_name: item.product_name || "----",
              quant: item.quant || 0,
              gift: item.gift || 0,
              price: `${item.price}` || "----",
              total: item.total ? `${item.total}  ` : "----",
              notes: item.notes || "----",
              client_group: item.client_group || "----",
              res_number: item.res_number || "----",
              product_type: item.product_type || "----",
              symbol: item.symbol || "----",
              product_unit: item.product_unit || "----",
            }))
            .reverse()
        : [],
    [data?.result, t]
  );

  const DelegateOptions = useSetSelectOptions({
    data: delegate?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  const clientsOptions = useSetSelectOptions({
    data: client?.result,
    getOption: (item) => ({ label: item.full_name, value: item.id }),
  });

  const cityOptions = useSetSelectOptions({
    data: city?.result,
    getOption: (item) => ({ label: item.name, value: item.id }),
  });

  const locationOptions = useSetSelectOptions({
    data: locations?.result,
    getOption: (item) => ({ label: item.name, value: item.id }),
  });

  const clientGroupsOptions = useSetSelectOptions({
    data: client_group?.result,
    getOption: (item) => ({ label: item.group_title, value: item.id }),
  });

  const productTypesOptions = useSetSelectOptions({
    data: product_types?.result,
    getOption: (item) => ({
      label: i18n.language === "en" ? item.title.en : item.title.ar,
      value: item.id,
    }),
  });

  const productOptions = useSetSelectOptions({
    data: product?.result,
    getOption: (item) => ({ label: item.name, value: item.id }),
  });

  const billTypesOptions = useSetSelectOptions({
    data: bill_types?.result,
    getOption: (item) => ({
      label: i18n.language === "en" ? item.title.en : item.title.ar,
      value: item.id,
    }),
  });

  const SearchData = [
    {
      id: 0,
      label: t("common.delegate_name"),
      type: "select",
      name: "delegate",
      options: DelegateOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 6,
      label: t("clients.client"),
      type: "select",
      name: "type",
      options: clientsOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 2,
      label: t("clients.client_group"),
      type: "select",
      name: "client_group",
      options: clientGroupsOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 3,
      label: t("reports.Bills_type"),
      type: "select",
      name: "bill",
      options: billTypesOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 4,
      label: t("common.cities"),
      type: "select",
      name: "city",
      options: cityOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 5,
      label: t("common.locations"),
      type: "select",
      name: "locations",
      options: locationOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 7,
      label: t("types.product_types.product_type"),
      type: "select",
      name: "product_type",
      options: productTypesOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
    {
      id: 8,
      label: t("common.product"),
      type: "select",
      name: "product_name",
      options: productOptions,
      cols: 2,
      component: CustomFilterSearch,
    },
  ];

  const inputsArray = [
    {
      id: 4,
      name: "bill_date",
      type: "date",
      label: t("bills.bill_date"),
      cols: 2,
    },
    {
      id: 5,
      name: "sales_filter",
      type: "text",
      label: t("reports.sales"),
      cols: 2,
    },
  ];

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("reports.clients_info")}
          breadcrumbItems={breadcrumbItems}
        />
        <Card>
          <CardBody>
            <TableContainer
              customComponent={
                <div className="d-grid gap-2 w-100">
                  <SearchCard
                    SearchData={SearchData}
                    control={control}
                    hadelReset={handleReset}
                    inputsArray={inputsArray}
                    register={register}
                    handelSearch={handleSearch}
                    watch={watch}
                    setValue={setValue}
                  />
                  <div className="d-flex justify-content-end">
                    <Button
                      className="btn-sm"
                      color="primary"
                      onClick={toggleColumnModal}
                    >
                      {t("reports.customize_columns")}
                    </Button>
                  </div>
                </div>
              }
              hidePagination
              hideSHowGFilter={false}
              columns={visibleColumnsArray || []}
              data={rowData || []}
              isPagination={true}
              isAddOptions={false}
              iscustomPageSize={true}
              isLoading={isLoading}
              isBordered={true}
              customPageSize={10}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          </CardBody>
        </Card>
        <ColumnVisibilityModal
          isOpen={columnModalOpen}
          onToggle={toggleColumnModal}
          columns={columns}
          visibleColumns={visibleColumns}
          onColumnToggle={toggleColumnVisibility}
        />
      </Container>
    </div>
  );
};

export default ClientInfo;
