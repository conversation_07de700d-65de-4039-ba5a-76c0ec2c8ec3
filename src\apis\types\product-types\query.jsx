import { useQuery } from "@tanstack/react-query";
import { ProductTypesAPis } from "./api";

const useGetAll = ({ limit, page, status }) => {
  const queryResult = useQuery({
    queryKey: ["all-product-typesss", limit, page, status],
    queryFn: () => ProductTypesAPis.getAll({ limit, page, status }),
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["product-typesss", id],
    queryFn: () =>
      id > 0 ? ProductTypesAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const ProductTypesQueries = {
  useGetAll,
  useGet,
};
