import React, { Fragment, useEffect } from "react";
import PropTypes from "prop-types";
import {
  useTable,
  useGlobalFilter,
  useSortBy,
  useFilters,
  useExpanded,
  usePagination,
} from "react-table";
import { Table, Row, Col } from "reactstrap";
import { DefaultColumnFilter } from "./filters";
import { useTranslation } from "react-i18next";
import GlobalFilter from "../table/global-filter";
import ActionButtons from "../table/action-buttons";
import TableHeader from "../table/table-header";
import TableBody from "../table/table-body";
import PaginationControls from "../table/pagination-container";
import ClipLoader from "react-spinners/ClipLoader";

const TableContainer = ({
  columns,
  data,
  isGlobalFilter,
  isJobListGlobalFilter,
  isAddOptions,
  isAddUserList,
  handleUserClick,
  handleCustomerClick,
  isAddCustList,
  customPageSize,
  className,
  customPageSizeOptions,
  customComponent,
  handelCustomClickOnRow,
  hidePagination = false,
  isSmall = false,
  manualPagination = true,
  pageCount,
  onPageChange,
  setPage,
  currentPage,
  isLoading,
  customHeight,
  customMinHeight,
  total,
  hasTotal,
  handleOrderClicks,
  disabledAddTitle,
  canPermission,
  addTitle,
}) => {
  const enhancedColumns = React.useMemo(() => [...columns], [columns]);
  const { t } = useTranslation();

  const {
    getTableProps,
    headerGroups,
    page,
    prepareRow,
    pageOptions,
    state,
    preGlobalFilteredRows,
    setGlobalFilter,
    state: { pageIndex, pageSize },
  } = useTable(
    {
      columns: enhancedColumns,
      data,
      defaultColumn: { Filter: DefaultColumnFilter },

      initialState: {
        pageIndex: 0,
        pageSize: customPageSize,
        sortBy: [{ id: "id", desc: false }],
      },
      manualPagination,
      // pageCount,
    },
    useGlobalFilter,
    useFilters,
    useSortBy,
    useExpanded,
    usePagination
  );
  useEffect(() => {
    const handler = setTimeout(() => {
      if (manualPagination && onPageChange) {
        onPageChange({ pageIndex, pageSize });
      }
    }, 100);

    return () => clearTimeout(handler);
  }, [pageIndex, pageSize, manualPagination, onPageChange]);
  const generateSortingIndicator = (column) =>
    column.isSorted ? (column.isSortedDesc ? " 🔽" : " 🔼") : "";

  return (
    <Fragment>
      <Row className="mb-2 ">
        {isGlobalFilter && (
          <GlobalFilter
            preGlobalFilteredRows={preGlobalFilteredRows}
            globalFilter={state.globalFilter}
            setGlobalFilter={setGlobalFilter}
            isJobListGlobalFilter={isJobListGlobalFilter}
          />
        )}
        {(isAddOptions || customComponent) && (
          <ActionButtons
            isAddUserList={isAddUserList}
            isAddCustList={isAddCustList}
            handleUserClick={handleUserClick}
            handleCustomerClick={handleCustomerClick}
            customComponent={customComponent}
            addTitle={addTitle}
            canPermission={canPermission}
            disabledAddTitle={disabledAddTitle}
            handleOrderClicks={handleOrderClicks}
            isAddOptions={isAddOptions}
          />
        )}
      </Row>
      <>
        <div style={{ maxHeight: "80%", overflowY: "auto" }}>
          <Table bordered hover {...getTableProps()} className={className}>
            <TableHeader
              headerGroups={headerGroups}
              generateSortingIndicator={generateSortingIndicator}
            />
            {isLoading ? (
              <tbody>
                <tr>
                  <td
                    colSpan={headerGroups[0]?.headers?.length || 1}
                    className="text-center p-5"
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: "200px",
                        userSelect: "none",
                      }}
                    >
                      <ClipLoader color="#ddd" size={50} />
                    </div>
                  </td>
                </tr>
              </tbody>
            ) : (
              <TableBody
                page={page}
                prepareRow={prepareRow}
                handelCustomClickOnRow={handelCustomClickOnRow}
                headerGroups={headerGroups}
                t={t}
              />
            )}
          </Table>
        </div>
      </>
      {/* {data?.length > 0 && ( */}
      <div className="d-flex justify-content-between align-items-center">
        <Col md={customPageSizeOptions ? 2 : 1}></Col>
        {!hidePagination && (
          <PaginationControls
            pageIndex={currentPage}
            pageOptions={pageOptions}
            pageCount={pageCount}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < pageCount}
            setPage={setPage}
            t={t}
            total={total}
            hasTotal={hasTotal}
          />
        )}
      </div>
      {/* )} */}
    </Fragment>
  );
};

TableContainer.propTypes = {
  columns: PropTypes.array.isRequired,
  data: PropTypes.array.isRequired,
  isGlobalFilter: PropTypes.bool,
  isJobListGlobalFilter: PropTypes.bool,
  isAddOptions: PropTypes.bool,
  isAddUserList: PropTypes.bool,
  handleOrderClicks: PropTypes.func,
  handleUserClick: PropTypes.func,
  handleCustomerClick: PropTypes.func,
  isAddCustList: PropTypes.bool,
  customPageSize: PropTypes.number,
  className: PropTypes.string,
  customPageSizeOptions: PropTypes.array,
  hideSHowGFilter: PropTypes.bool,
  addTitle: PropTypes.string,
  customComponent: PropTypes.node,
  canPermission: PropTypes.string,
  handelCustomClickOnRow: PropTypes.func,
  hidePagination: PropTypes.bool,
  isSmall: PropTypes.bool,
  setPageNumber: PropTypes.func,
  disabledAddTitle: PropTypes.bool,
};

export default TableContainer;
