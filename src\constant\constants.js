export const contructsEnum = {
  SELLCONTRACT: 1,
  RENTCONTRACT: 2,
};

export const CarsLogsEnum = {
  START_USE: 1,
  END_USE: 2,
  FILL_FUEL: 3,
  FAST_REPAIR: 4,
  GIVE_TO_REPAIR: 5,
  GIVE_FROM_REPAIR: 6,
  UPDATE_KM: 7,
};

export const ReasonsEnum = {
  New: 1,
  DELAYING: 2,
  DELAYED: 3,
  PROCESSING: 4,
  DONE: 5,
  REJECTED: 6,
  CANCELLED: 7,
  TRANSFER: 8,
};

export const ReasonsTranslation = [
  "",
  "new",
  "delaying",
  "delayed",
  "processing",
  "done",
  "rejected",
  "cancelled",
  "transfer",
];

export const AdmminAprovaple = {
  "": "",
  1: "Accepted",
  2: "Rejected",
  3: "Waiting",
};

export const AdmminAprovapleTranslation = (t) => ({
  1: t("types.reason.accepted"),
  2: t("types.reason.rejected"),
  3: t("types.reason.waiting"),
});

export const CreatingType = {
  "Super Admin": 1,
  Admin: 2,
  Delegate: 3,
  Client: 4,
  Server: 5,
};

export const DelegateCarsStatus = (t) => ({
  1: t("delegate.un_active"),
  2: t("delegate.width_car"),
  3: t("delegate.without_car"),
});

export const DelegateCarsEnum = {
  1: "Un Active",
  2: "With Car",
  3: "Without car",
};

export const CreatingStatus = (t) => ({
  1: t("common.in_progress"),
  2: t("common.decline"),
  3: t("types.reason.done"),
  4: t("types.reason.new"),
});

export const DelegateReportStatus = (t) => ({
  1: t("common.accepted"),
  3: t("common.pending"),
  2: t("common.rejected"),
  4: t("common.in_progress"),
});
export const ReasonsBill = {
  ACCEPTED: 1,
  REJECTED: 2,
};
export const getBillReasonTranslation = (value, t) => {
  switch (value) {
    case ReasonsBill.ACCEPTED:
      return t("bill_reasons.accepted");
    case ReasonsBill.REJECTED:
      return t("bill_reasons.rejected");
    default:
      return value;
  }
};

export const MaintainsBillStatus = {
  RECEIVED_FROM_CLIENT: 1,
  RECEIVED_BY_TECHNICIAN: 2,
  FINISHED_BY_TECHNICIAN: 3,
  DELIVERED_TO_DELEGATE: 4,
  DELIVERED_TO_CLIENT: 5,
};
