import ClipLoader from "react-spinners/ClipLoader";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Input,
  Label,
  Row,
} from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import toastr from "toastr";
import { useEffect, useState } from "react";
import { RolesQueries } from "../../apis/roles/query";
import { useForm } from "react-hook-form";
import { RolesAPis } from "../../apis/roles/api";
import { useLocation, useNavigate } from "react-router-dom";
import "./roles.scss";
import { handleBackendErrors } from "../../helpers/api_helper";
import { useTranslation } from "react-i18next";

const ActionRoles = () => {
  const { pathname } = useLocation();
  // Get the search params object
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const { t } = useTranslation();

  const { data: permissions } = RolesQueries.useGetAllPermission({
    enabled: true,
  });

  const breadcrumbItems = [
    { title: t("common.roles"), link: "/roles" },
    {
      title: t("common.roles"),
      link: pathname,
    },
  ];

  const { data: Role, isLoading: isLoadingRole } = RolesQueries.useGetRole({
    id: selectId,
  });
  const {
    handleSubmit,
    setValue,
    getValues,
    reset,
    setError,
    formState: { isSubmitting, errors },
    register,
  } = useForm({
    defaultValues: { permissions: [], title: "" },
  });
  const navigate = useNavigate();

  const handelCloseSideBar = () => {
    reset({ permissions: [], title: "" });
    setSelectedPermissions([]);
    navigate("/roles");
  };

  const handleCheckboxChange = (permissionId) => {
    if (selectedPermissions.includes(permissionId)) {
      // Uncheck: Remove from selectedPermissions
      setSelectedPermissions((prev) =>
        prev.filter((id) => id !== permissionId)
      );
    } else {
      // Check: Add to selectedPermissions
      setSelectedPermissions((prev) => [...prev, permissionId]);
    }
  };

  useEffect(() => {
    if (selectId > 0 && !isLoadingRole && Role?.result) {
      // Create a map of permission keys to IDs
      const permissionKeyToIdMap = permissions?.result.reduce(
        (acc, perm, index) => {
          acc[perm.key] = index + 1; // Adjust index if necessary
          return acc;
        },
        {}
      );

      // Map backend permission names to IDs
      const selectedPermissionIds =
        Role?.result &&
        Role.result.permissions
          ?.map((permission) => permissionKeyToIdMap[permission?.name])
          .filter((id) => id);

      // Set the form values
      setValue("permissions", selectedPermissionIds);
      setSelectedPermissions(selectedPermissionIds); // if using local state

      // Reset form with role data
      reset({
        title: Role.result.title,
        permissions: selectedPermissionIds,
      });
    }
  }, [isLoadingRole, selectId, Role, reset, setValue]);

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoadingRole && Role?.result) {
      // Populate form with role data when loaded
      reset({
        title: Role.result.title,
        permissions: Role.result.permissions || [],
      });
    }
  }, [selectId, isLoadingRole, Role, reset]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    //  debug: debug,
    //  progressBar: progressBar,
    //  preventDuplicates: preventDuplicates,
    //  newestOnTop: newestOnTop,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    //  showDuration: showDuration,
    hideDuration: 1000,
  };
  // Submit form
  const onSubmit = async (data) => {
    try {
      if (selectedPermissions.length <= 0) {
        toastr.error(t("roles.must_select_validation"));
        return;
      } else {
        const response = await RolesAPis.addRole({
          permession_ids: selectedPermissions,
          title: data.title,
        });
        toastr.success(response.message);
        handelCloseSideBar();
      }
    } catch (error) {
      // toastr.error("There are error ");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const UpdateFun = async (data) => {
    const updatedIds = selectedPermissions?.map((item) =>
      item.name ? item.id : item
    );
    try {
      if (updatedIds.length <= 0) {
        toastr.error("Must Select one Permission at least");
        return;
      } else {
        const response = await RolesAPis.updateRole({
          permession_ids: updatedIds,
          title: data.title,
          id: selectId,
        });
        toastr.success(response.message);
        handelCloseSideBar();
      }
    } catch (error) {
      // toastr.error("There are error");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  // const groupPermissions = (permissions) => {
  //   // Group by the 'section' key instead of splitting 'name'
  //   return (
  //     permissions?.result?.reduce((acc, permission) => {
  //       const group = permission.section; // Use section to group the permissions

  //       if (!acc[group]) {
  //         acc[group] = [];
  //       }
  //       acc[group].push(permission);
  //       return acc;
  //     }, {}) || {}
  //   ); // Return an empty object if permissions are undefined
  // };

  const groupPermissions = (permissions) => {
    return (
      permissions?.result?.reduce((acc, permission) => {
        const group = permission.name.split(".")[0]; // Get the prefix before the dot
        if (!acc[group]) {
          acc[group] = [];
        }
        acc[group].push(permission);
        return acc;
      }, {}) || {}
    ); // Return an empty object if permissions are undefined
  };

  // In your render method
  const groupedPermissions = groupPermissions(permissions);

  const handleGroupCheckboxChange = (groupKey, isChecked) => {
    const groupPermissions = groupedPermissions[groupKey];
    const permissionIds = groupPermissions?.map((permission) => permission.id);

    if (isChecked) {
      // Select all permissions in the group (add them to the selectedPermissions state)
      setSelectedPermissions((prev) => [
        ...new Set([...prev, ...permissionIds]),
      ]);
    } else {
      // Deselect all permissions in the group (remove them from the selectedPermissions state)
      setSelectedPermissions((prev) =>
        prev.filter((permId) => !permissionIds.includes(permId))
      );
    }
  };

  // Handle "Select All" checkbox
  const handleSelectAllChange = (isChecked) => {
    const allPermissionIndexes = permissions?.result?.map(
      (_, index) => index + 1
    );

    if (isChecked) {
      // If checked, select all permissions
      setSelectedPermissions(allPermissionIndexes);
    } else {
      // If unchecked, clear all permissions
      setSelectedPermissions([]);
    }
  };

  // Check if all permissions are selected (for the "Select All" checkbox)
  const isAllSelected =
    selectedPermissions?.length === permissions?.result?.length || 0;

  useEffect(() => {
    // Manually set the indeterminate state on the group checkboxes
    Object.keys(groupedPermissions).forEach((groupKey, groupIndex) => {
      const groupCheckbox = document.getElementById(`group-${groupIndex}`);
      const isIndeterminate =
        groupedPermissions[groupKey].some((permission) =>
          selectedPermissions?.includes(permission.id)
        ) &&
        !groupedPermissions[groupKey].every((permission) =>
          selectedPermissions?.includes(permission.id)
        );

      if (groupCheckbox) {
        groupCheckbox.indeterminate = isIndeterminate;
      }
    });
  }, [selectedPermissions, groupedPermissions]);

  // useEffect(() => {
  //   // Assuming 'existingPermissions' is the data coming from the backend for the user
  //   const preSelectedPermissions = Role?.result?.permissions?.map(
  //     (permission) => permission.id
  //   );
  //   setSelectedPermissions(preSelectedPermissions);
  // }, [Role]);

  useEffect(() => {
    if (Role?.result && Array.isArray(Role.result.permissions)) {
      const preSelectedPermissions = Role.result.permissions?.map(
        (permission) => permission.id
      );
      setSelectedPermissions(preSelectedPermissions);
    } else {
      setSelectedPermissions([]); // No permissions yet, set to empty array
    }
  }, [Role]);

  if (!permissions || !permissions.result) {
    return <div>Loading permissions...</div>; // or some other loading state
  }

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("common.roles")}
          breadcrumbItems={breadcrumbItems}
        />
        <Card>
          <CardBody>
            <Row>
              <form
                onSubmit={
                  selectId ? handleSubmit(UpdateFun) : handleSubmit(onSubmit)
                }
              >
                {isLoadingRole ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <div
                      style={{
                        overflowY: "auto",
                        overflowX: "hidden",
                        height: "55vh",
                        position: "relative",
                        paddingInline: 10,
                      }}
                    >
                      <div className="mb-4">
                        <Label
                          className="form-label"
                          htmlFor="title-in-english"
                        >
                          {t("roles.title_of_roles")}
                        </Label>
                        <input
                          type="text"
                          // className="form-control"
                          className={`form-control ${
                            errors.plate ? "is-invalid" : ""
                          }`}
                          placeholder={t("roles.title_of_roles")}
                          disabled={isShow}
                          {...register("title", { required: true })}
                        />
                        {errors.title && (
                          <div className="invalid-feedback">
                            {errors.title.message}
                          </div>
                        )}
                      </div>
                      <p className="permission-title">
                        {t("roles.select_your-permissions")}
                      </p>
                      <Container>
                        <Row>
                          <div className="form-check mb-3">
                            <Input
                              type="checkbox"
                              onChange={(e) =>
                                handleSelectAllChange(e.target.checked)
                              }
                              disabled={isShow}
                              className="form-check-input"
                              checked={isAllSelected}
                            />
                            <Label
                              htmlFor="select-all"
                              className="form-check-label"
                            >
                              {t("common.select_all")}
                            </Label>
                          </div>
                          {Object.keys(groupedPermissions)?.map(
                            (groupKey, groupIndex) => (
                              <div
                                key={groupKey}
                                style={{ borderBottom: "1px solid #eee" }}
                                className="mb-4"
                              >
                                <div className="form-check mb-3">
                                  {/* Group checkbox */}
                                  <Input
                                    type="checkbox"
                                    onChange={(e) =>
                                      handleGroupCheckboxChange(
                                        groupKey,
                                        e.target.checked
                                      )
                                    }
                                    disabled={isShow}
                                    id={`group-${groupIndex}`}
                                    className="form-check-input"
                                    // The group checkbox is checked if all permissions in the group are selected
                                    checked={groupedPermissions[groupKey].every(
                                      (permission) =>
                                        selectedPermissions?.includes(
                                          permission.id
                                        )
                                    )}
                                    // Indeterminate state (partially selected)
                                    indeterminate={
                                      groupedPermissions[groupKey].some(
                                        (permission) =>
                                          selectedPermissions?.includes(
                                            permission.id
                                          )
                                      ) &&
                                      !groupedPermissions[groupKey].every(
                                        (permission) =>
                                          selectedPermissions?.includes(
                                            permission.id
                                          )
                                      )
                                    }
                                  />
                                  <Label
                                    htmlFor={`group-${groupIndex}`}
                                    className="form-check-label"
                                  >
                                    {t(`permissions.groups.${groupKey}`, {
                                      defaultValue:
                                        groupKey.charAt(0).toUpperCase() +
                                        groupKey.slice(1).replace("_", " "),
                                    })}
                                  </Label>
                                </div>
                                <Row>
                                  {groupedPermissions[groupKey]?.map(
                                    (permission) => (
                                      <Col key={permission.id} xs="6">
                                        <div className="form-check mb-3">
                                          <Input
                                            type="checkbox"
                                            onChange={() =>
                                              handleCheckboxChange(
                                                permission.id
                                              )
                                            }
                                            checked={(
                                              selectedPermissions || []
                                            ).includes(permission.id)} // Safe check
                                            id={`check-${permission.id}`}
                                            className="form-check-input"
                                            disabled={isShow}
                                          />
                                          <Label
                                            htmlFor={`check-${permission.id}`}
                                            className="form-check-label"
                                          >
                                            {permission.title}
                                          </Label>
                                        </div>
                                      </Col>
                                    )
                                  )}
                                </Row>
                              </div>
                            )
                          )}
                        </Row>
                      </Container>
                    </div>
                    <div className="sticky-buttons d-flex gap-3">
                      <Button
                        onClick={handelCloseSideBar}
                        color="light"
                        className="waves-effect me-1"
                      >
                        {t("common.close")}
                      </Button>
                      {!isShow && (
                        <Button
                          color="primary"
                          className="waves-effect waves-light primary-button"
                          type="submit"
                        >
                          {isSubmitting ? (
                            <ClipLoader color="white" size={15} />
                          ) : selectId > 0 ? (
                            t("common.update")
                          ) : (
                            t("common.add")
                          )}
                        </Button>
                      )}
                    </div>
                  </>
                )}
              </form>
            </Row>
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};

export default ActionRoles;
