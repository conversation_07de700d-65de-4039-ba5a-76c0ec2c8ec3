import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Card, CardBody, Col, Container, Label, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { clientsQueries } from "../../apis/clients/query";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { clientsGroupsQueries } from "../../apis/client-group/query";
import { clientsGroupsAPis } from "../../apis/client-group/api";
import Select from "react-select";
import { handleBackendErrors, truncateText } from "../../helpers/api_helper";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import { MdDeleteSweep } from "react-icons/md";

const ClientActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const isShow = queryParams.get("id")?.split("?")[1];
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [clientsList, setClientsList] = useState([]);
  const { t, i18n } = useTranslation();
  const { pathname } = useLocation();

  const { data, isLoading } = clientsGroupsQueries.useGet({
    id: Number(selectId),
  });
  const { data: Clients } = clientsQueries.useGetAll({
    status: 1,
    searchParams: { "filter[no_group]": 2 },
  });

  const { data: delegates } = clientsGroupsQueries.useGetAllDelegate({});

  const navigate = useNavigate();

  const handelCancel = () => {
    navigate("/clients-groups");
    reset();
    setClientsList([]);
  };

  const schema = yup
    .object({
      groupTitleArabic: yup.string().required(t("common.field_required")),
    })
    .required();

  const statusOptions = useMemo(
    () => [
      { label: t("common.active"), value: 1 },
      { label: t("common.in_active"), value: 2 },
    ],
    [i18n.language] // Add t to dependencies to ensure re-render when language changes
  );

  const getDefaultStatus = statusOptions[0];

  const initialSTate = useMemo(
    () => ({
      groupTitleEnglish: "",
      groupTitleArabic: "",
      groupDescriptionEnglish: "",
      groupDescriptionArabic: "",
      status: getDefaultStatus,
      clients: [],
      delegate_id: delegates?.result[0]?.full_name,
    }),
    [i18n.language, getDefaultStatus, delegates]
  );

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    register,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const fieldsNames = [
    {
      id: 0,
      name: "groupTitleArabic",
      label: t("clients.group_title"),
      isRequired: true,
      error: errors.groupTitleArabic,
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "groupDescriptionArabic",
      label: t("clients.group_description_in_arabic"),
      isRequired: false,
    },
  ];

  // UseEffect for loading data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Map the clients array to match the format required by react-select
      if (data.result.clients?.length > 0) {
        setClientsList((prev) => {
          const newClients = data.result.clients
            .map((item) => ({
              label: `${item.company_name || "---"}/${item.full_name || "---"}`,
              value: item.id,
            }))
            .filter(
              (newClient) =>
                !prev.some(
                  (existingClient) => existingClient.value === newClient.value
                )
            );

          return [...prev, ...newClients];
        });
      } else {
        setClientsList([]);
      }

      // Reset the form with the formatted data
      reset({
        groupTitleArabic: data?.result.group_title,
        groupDescriptionArabic: data?.result.description,
        groupTitleEnglish: data?.result.group_title,
        status: {
          value: data?.result.status,
          label: statusOptions.find(
            (item) => item.value === data?.result?.status
          )?.label,
        },
      });
    }
  }, [selectId, isLoading, data?.result, i18n.language]);

  useEffect(() => {
    if (!selectId) {
      reset((prevValues) => ({
        ...prevValues,
        status: getDefaultStatus,
      }));
    }
  }, [i18n.language, getDefaultStatus]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      const dataTOsend = {
        group_title: data.groupTitleArabic,
        group_description: data.groupDescriptionArabic,
        status: Number(data.status.value),
        clients: clientsList.map((item) => Number(item.value)),
        delegate_id: data.delegate_id,
      };
      const response = await clientsGroupsAPis.update({
        dataToSend: dataTOsend,
        id: selectId,
      });
      toastr.success(response.message);
      navigate(-1);
      reset();
    } catch (error) {
      handleBackendErrors(error);
    }
  };

  const addFun = async (data) => {
    try {
      const dataTOsend = {
        group_title: data.groupTitleArabic,
        group_description: data.groupDescriptionArabic,
        status: Number(data.status.value),
        clients: clientsList.map((item) => Number(item.value)),
        delegate_id: data.delegate_id,
      };
      const response = await clientsGroupsAPis.add(dataTOsend);
      toastr.success(response.message);
      navigate(-1);
      reset(); // Reset form after successful submission
    } catch (error) {
      handleBackendErrors(error);
    }
  };

  useEffect(() => {
    if (Clients?.result?.length > 0) {
      setOptionGroup(
        Clients.result
          .filter(
            (client) => !clientsList.some((item) => item.value === client.id)
          )
          .map((item) => ({
            label: `${item.company_name || "---"}/${item.full_name || "---"}`,
            value: item.id,
          }))
      );
    }
  }, [Clients?.result, clientsList]);

  const handelRemoveFromList = (id) => {
    setClientsList((prev) => prev.filter((item) => item.value !== id));
  };

  const handelAddProduct = (clientInfo) => {
    setClientsList((prev) => {
      return [...prev, clientInfo];
    });
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("clients.client_group")}
          titleOfSection={t("clients.client_group")}
          titleOfPage={
            isShow
              ? t("common.show")
              : selectId
              ? t("common.update")
              : t("common.create")
          }
          titleLink="/clients-groups"
          currentPageLink={pathname}
        />
        <Card>
          <CardBody>
            <Row>
              <form
                onSubmit={
                  selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
                }
              >
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row>
                      {fieldsNames.map((field) => (
                        <Col xs={6} key={field.id}>
                          <div className="mb-2">
                            <CustomInput
                              name={field.name}
                              control={control}
                              label={field.label}
                              isRequired={field.isRequired}
                              isDisabled={isShow}
                              error={field.error}
                            />
                          </div>
                        </Col>
                      ))}
                      <Col xs={6}>
                        <div className="mb-4">
                          <CustomSelect
                            name="status"
                            menuHeight={80}
                            control={control}
                            options={statusOptions}
                            label={t("common.status")}
                            isDisabled={isShow}
                          />
                        </div>
                      </Col>
                      {textAreaField.map((field) => (
                        <Col key={field.id} xs={12}>
                          <div className="mb-2">
                            <Label className="form-label" htmlFor={field.name}>
                              {field.label}
                            </Label>
                            <textarea
                              id={field.name}
                              type="text"
                              rows={4}
                              className={`form-control`}
                              disabled={isShow}
                              placeholder="...."
                              {...register(`${field.name}`)}
                            />
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </>
                )}
                <Col
                  xs={12}
                  style={{
                    border: "1px solid #ccc",
                    width: "100%",
                    margin: "auto",
                    padding: 10,
                    marginBottom: 10,
                    borderRadius: 5,
                  }}
                >
                  <Col xs={12}>
                    <div className="mb-4">
                      <Label className="form-label" htmlFor="total">
                        {t("common.select")} {t("clients.clients")}
                      </Label>
                      <Controller
                        name="clients"
                        control={control}
                        defaultValue={[]}
                        render={({ field }) => (
                          <Select
                            {...field}
                            options={optionGroup}
                            isDisabled={isShow}
                            isMulti={false}
                            placeholder="---"
                            styles={{
                              menuList: (props) => ({
                                ...props,
                                paddingBottom: 10,
                                height: "100px",
                              }),
                              menu: (props) => ({
                                ...props,
                                height: "100px",
                              }),
                            }}
                            onChange={(selectedOptions) =>
                              handelAddProduct(selectedOptions)
                            }
                          />
                        )}
                      />
                    </div>
                  </Col>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr 1fr 1fr",
                      gap: 6,
                      maxHeight: "80px",
                      overflowY: "auto",
                    }}
                  >
                    {clientsList.map((item) => (
                      <Col
                        style={{
                          border: "1px solid #ccc",
                          position: "relative",
                          height: 40,
                        }}
                        className="p-1 rounded-2 d-flex w-100 align-items-center h-full"
                        xs={3}
                        key={item}
                      >
                        <p style={{ margin: 0 }}>
                          {truncateText({
                            text: item.label,
                            maxLengthPercent: 0.3,
                          })}
                        </p>
                        {!isShow && (
                          <div
                            onClick={() => handelRemoveFromList(item.value)}
                            className="text-danger"
                            style={{
                              cursor: "pointer",
                              position: "absolute",
                              insetInlineEnd: 10,
                              top: 10,
                            }}
                          >
                            {/* <i className="mdi mdi-trash-can font-size-14 "></i> */}
                            <MdDeleteSweep size={18} />
                          </div>
                        )}
                      </Col>
                    ))}
                  </div>
                </Col>
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <Button
                    type="button"
                    color="light"
                    onClick={handelCancel}
                    className="btn-sm "
                    style={{ height: "32px", width: "54px" }}
                  >
                    {t("common.close")}
                  </Button>
                  {!isShow && (
                    <Button
                      color="primary"
                      className="btn-sm waves-effect waves-light primary-button"
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <ClipLoader color="white" size={15} />
                      ) : selectId ? (
                        t("common.update")
                      ) : (
                        t("common.add")
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </Row>
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default ClientActions;
