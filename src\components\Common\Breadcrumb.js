import React from "react";
import { Link } from "react-router-dom";
import { Row, Col, Breadcrumb, Button } from "reactstrap";
import { IoIosArrowBack, IoMdAdd } from "react-icons/io";
import { RiResetLeftFill } from "react-icons/ri";

//i18n
import { useTranslation, withTranslation } from "react-i18next";
import { Can } from "../permissions-way/can";
import { MdKeyboardArrowLeft, MdKeyboardArrowRight } from "react-icons/md";

const Breadcrumbs = ({
  title,
  isAddOptions,
  canPermission,
  handleOrderClicks,
  disabledAddTitle,
  addTitle,
  titleOfPage,
  titleOfSection,
  titleLink,
  currentPageLink,
  customComponent,
}) => {
  const { i18n } = useTranslation();
  return (
    <React.Fragment>
      <Row>
        <Col xs={12}>
          <div className="page-title-box d-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-0 " style={{ fontSize: "0.8rem" }}>
                {title}
              </h4>

              <div className="page-title-right d-flex align-items-center">
                <Breadcrumb listClassName="mt-1">
                  {titleOfSection &&
                    (titleLink ? (
                      <Link to={titleLink} className="text-secondary">
                        {titleOfSection}
                      </Link>
                    ) : (
                      <span className="text-secondary">{titleOfSection}</span>
                    ))}
                  {titleOfPage &&
                    titleOfSection &&
                    (i18n.language === "eng" ? (
                      <MdKeyboardArrowRight size={16} />
                    ) : (
                      <IoIosArrowBack size={16} />
                    ))}
                  {titleOfPage &&
                    (currentPageLink ? (
                      <Link to={currentPageLink} className="text-secondary">
                        {titleOfPage}
                      </Link>
                    ) : (
                      <span className="text-secondary">{titleOfPage}</span>
                    ))}
                </Breadcrumb>
              </div>
            </div>
            <Can permission={canPermission}>
              {customComponent
                ? customComponent
                : isAddOptions && (
                    <div className="text-sm-end">
                      <Button
                        type="button"
                        color="primary"
                        className="rounded-1 flex gap-2 mb-2 me-2 btn-sm p-2"
                        onClick={handleOrderClicks}
                        disabled={disabledAddTitle}
                      >
                        {/* <i className="mdi mdi-plus me-1" /> */}
                        {/* <IoIosSearch /> */}
                        <IoMdAdd size={14} />

                        {addTitle}
                      </Button>
                    </div>
                  )}
            </Can>
          </div>
        </Col>
      </Row>
    </React.Fragment>
  );
};

export default withTranslation()(Breadcrumbs);
