import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Card, CardBody, Col, Container, Label, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { Controller, useForm } from "react-hook-form";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, useNavigate } from "react-router-dom";
import { delegateQueries } from "../../apis/delegate/query";
import { useStoreOperationQueries } from "../../apis/store-operation/query";
import { useStoreRequest } from "../../apis/store-request/query";
import { storeRequestApis } from "../../apis/store-request/api";
import { productQueries } from "../../apis/products/query";
import { useEffect, useState } from "react";
import Select from "react-select";
import { useTranslation } from "react-i18next";

const ClientActions = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id"));
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const { data, isLoading } = useStoreRequest.useGetAll({
    id: Number(selectId),
  });
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: storeOperation } = useStoreOperationQueries.useGetAllOperation(
    {}
  );
  const { data: products } = productQueries.useGetAll({status:1});

  const breadcrumbItems = [
    { title: t("store_transactions.store_transactions"), link: "/clients" },
    {
      title: selectId ? "Update" : "Create",
      link: pathname,
    },
  ];
  const navigate = useNavigate();

  const handelCancel = () => {
    navigate("/clients");
    reset();
  };

  const initialSTate = {
    status: 1,
    transaction: 1,
    delegate_id: null,
    store_operation_id: null,
    products: [], // Add image default value
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting },
    register,
    setError,
    control,
    errors,
  } = useForm({
    defaultValues: { ...initialSTate },
    // resolver: yupResolver(schema),
  });

  const TransactionOptions = [
    { value: 1, label: "Enter Info Main Store" },
    { value: 2, label: "Out From Main Store" },
    { value: 3, label: "Enter into car" },
    { value: 4, label: "out from car" },
  ];
  const staus = [
    { value: 1, label: "New" },
    { value: 2, label: "Rejected" },
    { value: 3, label: "Done" },
  ];

  // UseEffect for loading Role data
  //   useEffect(() => {
  //     if (selectId > 0 && !isLoading && data?.result) {
  // const clientsToReset = data.result.Products.map((item) => ({
  //   label: item.full_name,
  //   value: item.id,
  // }));

  //       // Populate form with role data when loaded
  //       reset({
  //         full_name: data?.result.full_name,
  //         email: data?.result.email,
  //         password: data?.result.password,
  //         phone_number: data?.result.phone_number,
  //         website_url: data?.result.website_url,
  //         company_description: data?.result.company_description,
  //         company_name: data?.result.company_name,
  //         address: data?.result.address,
  //         status: data?.result.status,
  //         client_group_id: data?.result.client_group_id,
  //         delegate_id: data?.result.delegate_id,
  //         bound_total: data?.result.bound_total,
  //         image: null, // Add image default value
  //       });
  //     }
  //   }, [selectId, isLoading, data]);
  useEffect(() => {
    if (products?.result?.length > 0) {
      setOptionGroup((prev) => [
        ...prev,
        ...products?.result?.map((item) => ({
          label: item.name,
          value: item.id,
        })),
      ]);
    }
  }, [products?.result?.length]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    try {
      await storeRequestApis.update({ payload: data, id: selectId });
      toastr.success("Update done");
      navigate(-1);
      reset();
    } catch (error) {
      toastr.error("There are errors");
      Object.keys(error.response.data.errors).forEach((field) => {
        setError(field, {
          type: "manual",
          message: error.response.data.errors[field][0],
        });
      });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    try {
      // Create structured product details using `quant` and `maintaince_quant` from the products list
      const productsDetails = data.products.map((productId) => {
        // Find the product in the original list by its ID
        const product = products?.result.find((p) => p.id === productId.value);

        // Return the object structure with values from the product
        return {
          product_id: productId.value,
          quant: product.quant, // Default to 1 if product is not found
          maintaince_quant: product.maintaince_quant, // Default to 1 if product is not found
        };
      });

      const filterDataToSend = {
        transaction: data.transaction, // StoreRequestTrasnactionEnum
        status: data.status,
        store_operation_id: data.store_operation_id,
        delegate_id: data.delegate_id, // nullable,
        products: productsDetails,
      };
      await storeRequestApis.add(filterDataToSend);
      toastr.success("added successfully!");
      navigate(-1);
      reset(); // Reset form after successful submission
    } catch (error) {
      Object.keys(error.response.data.errors).forEach((field) => {
        setError(field, {
          type: "manual",
          message: error.response.data.errors[field][0],
        });
      });
      toastr.error("There was an error adding");
      console.error("Error:", error);
    }
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs title={"clients"} breadcrumbItems={breadcrumbItems} />
        <Card>
          <CardBody>
            <Row>
              <form
                onSubmit={
                  selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)
                }
              >
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row>
                      <Col xs={4}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            {t("store-request.transaction")}
                          </Label>
                          <select
                            placeholder="status"
                            className={`${
                              errors.transaction?.message
                                ? "form-select is-invalid"
                                : "form-select"
                            }`} // className="form-control"
                            // disabled={isShow}
                            {...register("transaction")} // register the select field
                          >
                            {TransactionOptions?.map((item) => (
                              <option value={item.value} key={item.value}>
                                {item.label}
                              </option>
                            )).slice(2)}
                          </select>
                          {errors.transaction && (
                            <div className="invalid-feedback">
                              {errors.transaction.message}
                            </div>
                          )}
                        </div>
                      </Col>
                      <Col xs={4}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            Status
                          </Label>
                          <select
                            placeholder="status"
                            className="form-control"
                            {...register("status", { required: true })} // register the select field
                          >
                            {staus?.map((item) => (
                              <option value={item.value} key={item.value}>
                                {item.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </Col>
                      <Col xs={4}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            {t("store-request.transaction")}
                          </Label>
                          <select
                            placeholder="status"
                            className="form-control"
                            {...register("transaction", { required: true })} // register the select field
                          >
                            {TransactionOptions?.map((item) => (
                              <option value={item.value} key={item.value}>
                                {item.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </Col>
                      <Col xs={6}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            Delegate
                          </Label>
                          <select
                            placeholder="Delegate"
                            className="form-select"
                            {...register("delegate_id", { required: true })} // register the select field
                          >
                            {delegates?.result.map((item) => (
                              <option value={item.id} key={item.id}>
                                {item.full_name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </Col>
                      <Col xs={6}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            Store Operation
                          </Label>
                          <select
                            placeholder="Delegate"
                            className="form-select"
                            {...register("store_operation_id", {
                              required: true,
                            })} // register the select field
                          >
                            {storeOperation?.result.map((item) => (
                              <option value={item.id} key={item.id}>
                                {item.title[0].en}
                              </option>
                            ))}
                          </select>
                        </div>
                      </Col>
                      <Col xs={12}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            select Products
                          </Label>
                          <Controller
                            name="products"
                            control={control}
                            defaultValue={[]}
                            render={({ field }) => (
                              <Select
                                {...field}
                                options={optionGroup}
                                isMulti
                                styles={{
                                  menuList: (props) => ({
                                    ...props,
                                    paddingBottom: 10,
                                    height: "100px",
                                  }),
                                  menu: (props) => ({
                                    ...props,
                                    height: "100px",
                                  }),
                                }}
                                classNamePrefix="select2-selection"
                                onChange={(selectedOptions) =>
                                  field.onChange(selectedOptions)
                                }
                              />
                            )}
                          />
                        </div>
                      </Col>
                    </Row>
                  </>
                )}
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <Button type="button" color="light" onClick={handelCancel}>
                    Close
                  </Button>
                  {/* <Can permission={selectId ? "client.update" : "client.store"}> */}
                  <Button
                    color="primary"
                    className="waves-effect waves-light primary-button"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <ClipLoader color="white" size={15} />
                    ) : selectId ? (
                      "Update"
                    ) : (
                      "Add"
                    )}
                  </Button>
                  {/* </Can> */}
                </div>
              </form>
            </Row>
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default ClientActions;
