import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import * as serviceWorker from "./serviceWorker";
import { Provider } from "react-redux";
import RTLDirection from "./components/RTLDirection";
// import i18n from "./i18n";

import store from "./store";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter } from "react-router-dom";

const queryClient = new QueryClient();

export const Wrapper = ({ children, data }) => {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <RTLDirection /> {/* Wrapping with InterceptorWrapper */}
        {children}
      </QueryClientProvider>
    </BrowserRouter>
  );
};

const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
  <Provider store={store}>
    <Wrapper>
      <App />
    </Wrapper>
  </Provider>
);

serviceWorker.unregister();
