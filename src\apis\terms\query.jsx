import { useQuery } from "@tanstack/react-query";
import { TermsAPis } from "./api";

const useGetAll = ({ limit, page, sectionId }) => {
  const queryResult = useQuery({
    queryKey: ["sections", limit, page, sectionId],
    queryFn: () =>
      TermsAPis.getAll({
        limit,
        page,
        sectionId,
      }),
  });
  return queryResult;
};

const useGet = ({ id }) => {
  const queryResult = useQuery({
    queryKey: ["section", id],
    queryFn: () =>
      id > 0 ? TermsAPis.getOne({ id }) : Promise.reject("Invalid ID"),
    enabled: id > 0, // Enables query only if id is greater than 0
  });

  return queryResult;
};

export const sectionsQueries = {
  useGetAll,
  useGet,
};
