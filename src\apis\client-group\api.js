import ApiInstance from "../../constant/api-instance";
import { CLIENTS_GROUPS_ROUTES } from "./route";

const getAll = async ({ limit, page, status }) => {
  const { data } = await ApiInstance.get(CLIENTS_GROUPS_ROUTES.CLIENTS, {
    params: {
      limit,
      page,
      "filter[status]": status,
    },
  });
  return data;
};

const getAllDELEGATE = async ({ limit, page }) => {
  const { data } = await ApiInstance.get(CLIENTS_GROUPS_ROUTES.DELEGATE, {});
  return data;
};
const getTask = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CLIENTS_GROUPS_ROUTES.CLIENTS}/${id}`
  );
  return data;
};
const active = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CLIENTS_GROUPS_ROUTES.ACTIVE}/${id}`
  );
  return data;
};
const inActive = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${CLIENTS_GROUPS_ROUTES.IN_ACTIVE}/${id}`
  );
  return data;
};

const add = async (dataToSend) => {
  const { data } = await ApiInstance.post(
    `${CLIENTS_GROUPS_ROUTES.CLIENTS}`,
    dataToSend
  );
  return data;
};

const update = async ({ dataToSend, id }) => {
  const { data } = await ApiInstance.put(
    `${CLIENTS_GROUPS_ROUTES.CLIENTS}/${id}`,
    {
      ...dataToSend,
    }
  );
  return data;
};

const deleteFu = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${CLIENTS_GROUPS_ROUTES.CLIENTS}/${id}`
  );
  return data;
};
export const clientsGroupsAPis = {
  deleteFu,
  update,
  add,
  getTask,
  active,
  inActive,
  getAll,
  getAllDELEGATE,
};
