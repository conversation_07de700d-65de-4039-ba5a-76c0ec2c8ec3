import { useTranslation, withTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  Label,
  Modal,
  ModalBody,
  ModalFooter,
  <PERSON><PERSON>Header,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useStoreTransaction } from "../../apis/store-transations/query";
import { Controller, useForm } from "react-hook-form";
import Select from "react-select";
import { productQueries } from "../../apis/products/query";
import { delegateQueries } from "../../apis/delegate/query";
import { storeTransactionApis } from "../../apis/store-transations/api";
import { handleBackendErrors } from "../../helpers/api_helper";
import ActionStoreRequest from "../../components/storeRequest/actionStoreRequest";
import { Can } from "../../components/permissions-way/can";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const StoreRequest = ({ t }) => {
  const [page, setPage] = useState(1);
  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = useStoreTransaction.useGetAll({ limit: 50, page: page });
  const { data: NotRecived, isLoading: isLoadingNotRecived } =
    useStoreTransaction.useGetAllNotReviced({ limit: 50, page: page });
  const { pathname } = useLocation();
  const [productList, setProductList] = useState([]);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState(0);
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [delegateoptionGroup, setDelegateOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [isLoading, setIsLoading] = useState(false);
  const [openStoreRequestModal, setOPenStoreRequestModal] = useState(false);
  const [selectId, setSelectedId] = useState(0);

  const { data: products } = productQueries.useGetAll({
    enabled: openAddModal,
    status: 1,
  });
  const { data: delegates } = delegateQueries.useGetAll({
    enabled: openAddModal,
    status: 1,
  });

  const initialSTate = {
    delegate_id: null,
    quant: "",
    maintaince_quant: "",
    product_id: null,
    transaction: null,
  };

  const {
    formState: { errors },
    register,
    watch,
    setValue,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
  });

  const resetInputs = () => {
    setSelectedProductId(0);
    setValue("product_id", null);
    setValue("maintaince_quant", "");
    setValue("quant", "");
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const breadcrumbItems = [
    {
      title: t("store_transactions.store_transactions"),
      link: pathname,
    },
  ];

  const SToreRequestTransaction = {
    1: t("store-request.enter_store"),
    2: t("store-request.out_store"),
    3: t("store-request.enter_car"),
    4: t("store-request.out_car"),
  };

  const SToreDelegateStatus = {
    1: t("common.received"),
    2: t("common.not_received"),
    3: t("common.rejected"),
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.delegate_status"),
      accessor: "delegate_status",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.delegate_response"),
      accessor: "delegate_res",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.store_operation.store_operation"),
      accessor: "store_operation",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store-request.transaction"),
      accessor: "transaction",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.order_details"),
      accessor: (cellProps) => {
        return (
          <Can permission={"store.store_transactions.show"}>
            <div className="pointer">
              <Link
                // to={`/action-store-request/?id=${cellProps.store_request_id}?Show=true`}
                onClick={() => {
                  setOPenStoreRequestModal(true);
                  setSelectedId(cellProps.id);
                }}
                className="me-3 text-primary"
                style={{ fontWeight: "bold" }}
                title={t("common.view_details")}
              >
                {t("common.show")}
              </Link>
            </div>
          </Can>
        );
      },
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: t("common.note"),
    //   accessor: "notes",
    //   disableFilters: true,
    //   filterable: false,
    // },
    // {
    //   Header: "Action",
    //   accessor: (cellProps) => {
    //     return (
    //       <>
    //         <Link
    //           title={
    //             cellProps.is_default !== 1
    //               ? "Disabled"
    //               : `/action-store-request/?id=${cellProps.id}`
    //           }
    //           to={cellProps.is_default !== 1 && `#`}
    //           className="me-3 text-primary"
    //         >
    //           <i className="mdi mdi-pencil font-size-18"></i>
    //         </Link>
    //       </>
    //     );
    //   },
    //   disableFilters: true,
    //   filterable: false,
    // },
  ];
  const [isApproveList, setIsApproveList] = useState(0);

  const { i18n } = useTranslation();

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1,
              store_operation:
                (i18n.language === "eng"
                  ? item?.store_operation?.en
                  : item?.store_operation?.ar) || "----",
              delegate: item.delegate?.full_name || "---",
              delegate_status:
                SToreDelegateStatus[item.delegate_status] || "---",
              transaction: SToreRequestTransaction[item.transaction],
              notes: item.delegate_notes || "---",
              store_request_id: item.store_request_id || "---",
              delegate_res:
                new Date(item.delegate_res)?.toISOString()?.split("T")[0] ||
                "---",
            }))
            .reverse()
        : [],
    [ContractTypes?.result, t]
  );

  const NotReceivedRowData = useMemo(
    () =>
      NotRecived?.result?.length > 0
        ? NotRecived.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              store_operation:
                (i18n.language === "eng"
                  ? item?.store_operation?.en
                  : item?.store_operation?.ar) || "----",
              delegate: item.delegate?.full_name || "---",
              delegate_status:
                SToreDelegateStatus[item.delegate_status] || "---",
              transaction: SToreRequestTransaction[item.transaction],
              notes: item.delegate_notes || "---",
              store_request_id: item.store_request_id || "---",
              delegate_res:
                new Date(item.delegate_res)?.toISOString()?.split("T")[0] ||
                "---",
            }))
            .reverse()
        : [],
    [NotRecived?.result, t, isApproveList]
  );

  const NotRevivedColumns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.delegate"),
      accessor: "delegate",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.delegate_status"),
      accessor: "delegate_status",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.delegate_response"),
      accessor: "delegate_res",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("types.store_operation.store_operation"),
      accessor: "store_operation",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: "Transaction",
      accessor: "transaction",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("store_transactions.order_details"),
      accessor: (cellProps) => {
        return (
          <div className="pointer">
            <Can permission={"store.store_transactions.show"}>
              <Link
                onClick={() => {
                  setOPenStoreRequestModal(true);
                  setSelectedId(cellProps.id);
                }}
                className="me-3 text-primary"
                style={{ fontWeight: "bold" }}
                title={t("common.view_details")}
              >
                {t("common.show")}
              </Link>
            </Can>
          </div>
        );
      },
      disableFilters: true,
      filterable: false,
    },
    // {
    //   Header: t("common.note"),
    //   accessor: "notes",
    //   disableFilters: true,
    //   filterable: false,
    // },
    // {
    //   Header: "Action",
    // accessor: (cellProps) => {
    //   return (
    //     <>
    //       <Link
    //         title={
    //           cellProps.is_default !== 1
    //             ? "Disabled"
    //             : `/action-store-request/?id=${cellProps.id}`
    //         }
    //         to={cellProps.is_default !== 1 && `#`}
    //         className="me-3 text-primary"
    //       >
    //         <i className="mdi mdi-pencil font-size-18"></i>
    //       </Link>
    //     </>
    //   );
    // },
    //   disableFilters: true,
    //   filterable: false,
    // },
  ];

  const handelSaveAllChanges = async () => {
    try {
      setIsLoading(true);
      const response = await storeTransactionApis.addTransaction({
        payload: {
          products: productList,
          delegate_id: watch("delegate_id").value,
          transaction: Number(watch("transaction")),
        },
      });
      setIsLoading(false);
      setValue("delegate_id", null);
      setValue("transaction", null);
      toastr.success(response.message);
      setOpenAddModal(false);
      setProductList([]);
      resetInputs();
      refetch();
    } catch (error) {
      setIsLoading(false);
      handleBackendErrors({ error });
    }
  };

  const TransactionOptions = [
    { value: 3, label: t("store-request.store-request.enter_car") },
    { value: 4, label: t("store-request.store-request.out_car") },
  ];

  const productsFields = [
    {
      id: 4,
      field: (
        <Col xs={6}>
          <div className="mb-4">
            <Label className="form-label" htmlFor="total">
              {t("store-request.transaction")}
            </Label>
            <select
              placeholder="---"
              className={`${
                errors.transaction?.message
                  ? "form-select is-invalid"
                  : "form-select"
              }`} // className="form-control"
              disabled={isShow}
              {...register("transaction")} // register the select field
            >
              {TransactionOptions?.map((item) => (
                <option value={item.value} key={item.value}>
                  {item.label}
                </option>
              ))}
            </select>
            {errors.transaction && (
              <div className="invalid-feedback">
                {errors.transaction.message}
              </div>
            )}
          </div>
        </Col>
      ),
    },
    {
      id: 0,
      field: (
        <Col xs={6} className="mb-2">
          <div>
            <Label className="form-label" htmlFor="total">
              {t("common.select")} {t("common.delegate")}
            </Label>
          </div>
          <Controller
            name="delegate_id"
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <Select
                {...field}
                isClearable
                options={delegateoptionGroup}
                isMulti={false}
                isDisabled={isShow}
                styles={{
                  menuList: (props) => ({
                    ...props,
                    paddingBottom: 10,
                    height: "100px",
                  }),
                  menu: (props) => ({
                    ...props,
                    height: "100px",
                  }),
                }}
              />
            )}
          />
        </Col>
      ),
    },
    {
      id: 0,
      field: (
        <Col xs={4} className="mb-4">
          <div>
            <Label className="form-label" htmlFor="total">
              {t("store_transactions.select_product")}
            </Label>
          </div>
          <Controller
            name="product_id"
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <Select
                {...field}
                isClearable
                options={optionGroup.filter(
                  (option) =>
                    !productList.some(
                      (product) => product.product_id === option.value
                    )
                )}
                isMulti={false}
                isDisabled={isShow}
                styles={{
                  menuList: (props) => ({
                    ...props,
                    paddingBottom: 10,
                    height: "100px",
                  }),
                  menu: (props) => ({
                    ...props,
                    height: "100px",
                  }),
                }}
                classNamePrefix="select2-selection"
              />
            )}
          />
        </Col>
      ),
    },
    {
      id: 1,
      field: (
        <Col>
          <div className="mb-2">
            <Label className="form-label" htmlFor="quant">
              {t("common.quant")}
            </Label>
            <input
              name=""
              {...register(`quant`, {
                required: true,
              })}
              disabled={!watch("product_id") || isShow}
              placeholder="...."
              type="number"
              className={`form-control ${errors?.quant ? `is-invalid` : ``}`}
            />
            {errors?.quant && (
              <div className="invalid-feedback">{errors?.quant?.message}</div>
            )}
          </div>
        </Col>
      ),
    },
    {
      id: 2,
      field: (
        <Col>
          <div className="mb-4">
            <Label className="form-label" htmlFor="quant">
              {t("common.maintenance_quantity")}
            </Label>
            <input
              name=""
              {...register(`maintaince_quant`, {
                required: true,
              })}
              disabled={!watch("product_id") || isShow}
              placeholder="...."
              type="number"
              className={`form-control ${errors?.quant ? `is-invalid` : ``}`}
            />
            {errors?.maintaince_quant && (
              <div className="invalid-feedback">
                {errors?.maintaince_quant?.message}
              </div>
            )}
          </div>
        </Col>
      ),
    },
  ];

  const handelFilterFromProductList = (id) => {
    // Filter out the item with the given id from both productList and rowData
    const updatedProductList = productList.filter(
      (item) => item.product_id !== id
    );

    // Update the productList state (this will automatically trigger a re-render)
    setProductList(updatedProductList);
  };

  const handelSetUpdate = (id) => {
    setOpenAddModal(true);
    setSelectedProductId(id);
    handelFilterFromProductList(id);
    const currentProduct = productList.find((item) => item.product_id === id);
    setValue("product_id", {
      value: currentProduct.product_id,
      label: currentProduct.product_name,
    });
    setValue("quant", currentProduct.quant);
    setValue("maintaince_quant", currentProduct.maintaince_quant);
  };

  const handelAddProductToList = () => {
    try {
      // Capture all form values upfront to avoid stale closures
      const productId = watch("product_id");
      const quant = Number(watch("quant"));
      if (productId && quant > 0) {
        // Extract all form values
        const maintainceQuant = Number(watch("maintaince_quant"));

        // Create new product object
        const newProduct = {
          product_id: productId.value,
          product_name: productId.label,
          quant: quant,
          maintaince_quant: maintainceQuant,
        };

        // Update product list state
        setProductList((prev) => {
          // Check if product already exists
          const existingIndex = prev.findIndex(
            (item) => item.product_id === selectedProductId
          );

          if (existingIndex !== -1) {
            // Update existing product
            const updatedList = [...prev];
            updatedList[existingIndex] = newProduct;
            return updatedList;
          } else {
            // Add new product
            return [...prev, newProduct];
          }
        });

        // Reset form and close modal
        resetInputs();
      }
    } catch (error) {
      console.log("Error handling product:", error);
    }
  };

  const reasonsLis = [
    { id: 0, title: t("store-request.transaction") },
    { id: 1, title: t("store_transactions.not_received") },
  ];

  const navigate = useNavigate();

  const handelSelect = (id) => {
    setIsApproveList(id);
  };

  useEffect(() => {
    if (products?.result?.length > 0) {
      setOptionGroup(
        products.result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    }
  }, [products?.result]);

  useEffect(() => {
    if (delegates?.result?.length > 0) {
      setDelegateOptionGroup(
        delegates.result.map((item) => ({
          label: item.full_name,
          value: item.id,
        }))
      );
    }
  }, [delegates?.result]);

  const columnsProduct = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.maintenance_quantity"),
      accessor: "maintaince_quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          !isShow && (
            <div className="d-flex align-items-center gap-2 justify-content-start">
              <div
                className="text-primary"
                onClick={() => {
                  handelSetUpdate(cellProps.product_id);
                }}
              >
                {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                <FaPenToSquare size={14} />
              </div>
              <div
                onClick={() => {
                  handelFilterFromProductList(cellProps.product_id);
                }}
                className="text-danger"
              >
                {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                <MdDeleteSweep size={18} />
              </div>
            </div>
          )
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowDataProduct = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              maintaince_quant: item.maintaince_quant,
            }))
            .reverse()
        : [],
    [productList]
  );

  const handelCloseAddModal = () => {
    setOpenAddModal(false);
    resetInputs();
    setProductList([]);
  };

  const handelOpenAddModal = () => {
    setOpenAddModal(true);
  };

  const handelCloseModal = () => {
    setOPenStoreRequestModal(false);
    setSelectedId(0);
  };

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("store_transactions.store_transactions")}
          breadcrumbItems={breadcrumbItems}
        />
        <Card style={{ height: "78vh" }}>
          <CardBody>
            {/* {isLoadingUsers || isLoadingNotRecived ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
            <>
              {isApproveList !== 1 ? (
                <TableContainer
                  hideSHowGFilter={false}
                  columns={columns || []}
                  data={rowData}
                  isPagination={true}
                  iscustomPageSize={true}
                  isBordered={true}
                  pageSize={10}
                  pageIndex={page}
                  customHeight={"100%"}
                  manualPagination={true}
                  pageCount={ContractTypes?.meta?.last_page || 1}
                  currentPage={page}
                  setPage={setPage}
                  isLoading={isLoadingNotRecived || isLoadingUsers}
                  isAddOptions={true}
                  addTitle={
                    t("common.add") + " " + t("store-request.transaction")
                  }
                  handleOrderClicks={handelOpenAddModal}
                  // handleOrderClicks={() => navigate("/action-store-request")}
                  customPageSize={10}
                  customComponent={
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: 10,
                      }}
                    >
                      {reasonsLis.map((item) => (
                        <p
                          style={{
                            background:
                              item.id === isApproveList
                                ? "rgb(28 187 140)"
                                : "",
                            color: item.id === isApproveList ? "#fff" : "",
                            paddingInline: 20,
                            paddingBlock: 10,
                            borderRadius: 40,
                            border: "1px solid #ccc",
                            cursor: "pointer",
                          }}
                          onClick={() => handelSelect(item.id)}
                          key={item.id}
                        >
                          {item.title}
                        </p>
                      ))}
                    </div>
                  }
                  className="custom-header-css table align-middle table-nowrap"
                  tableClassName="table-centered align-middle table-nowrap mb-0"
                  theadClassName="text-muted table-light"
                />
              ) : (
                <TableContainer
                  hideSHowGFilter={false}
                  columns={NotRevivedColumns || []}
                  data={NotReceivedRowData}
                  isPagination={true}
                  customHeight={"100%"}
                  iscustomPageSize={true}
                  isBordered={true}
                  pageSize={10}
                  isLoading={isLoadingNotRecived || isLoadingUsers}
                  pageIndex={page}
                  manualPagination={true}
                  pageCount={NotRecived?.meta?.last_page || 1}
                  currentPage={page}
                  setPage={setPage}
                  handleOrderClicks={() => navigate("/action-store-request")}
                  customPageSize={10}
                  customComponent={
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: 10,
                      }}
                    >
                      {reasonsLis.map((item) => (
                        <p
                          style={{
                            background:
                              item.id === isApproveList
                                ? "rgb(28 187 140)"
                                : "",
                            color: item.id === isApproveList ? "#fff" : "",
                            paddingInline: 20,
                            paddingBlock: 10,
                            borderRadius: 40,
                            border: "1px solid #ccc",
                            cursor: "pointer",
                          }}
                          onClick={() => handelSelect(item.id)}
                          key={item.id}
                        >
                          {item.title}
                        </p>
                      ))}
                    </div>
                  }
                  className="custom-header-css table align-middle table-nowrap"
                  tableClassName="table-centered align-middle table-nowrap mb-0"
                  theadClassName="text-muted table-light"
                />
              )}
            </>
            {/* )} */}
          </CardBody>
        </Card>
      </Container>
      <Modal
        isOpen={openAddModal}
        backdrop="static"
        style={{
          maxWidth: "60vw",
          maxHeight: "80vh",
          overflowY: "auto",
          height: "90vh",
          borderRadius: 10,
        }}
      >
        <ModalHeader toggle={handelCloseAddModal}>
          {t("common.add")} {t("common.product")}
        </ModalHeader>
        <ModalBody>
          <Row>{productsFields.map((item) => item.field)}</Row>
          <Button
            type="button"
            onClick={handelAddProductToList}
            color="primary"
            disabled={!watch("quant")}
          >
            {selectedProductId > 0
              ? t("store_transactions.update_this_product")
              : t("common.add_to_table")}
          </Button>
          <TableContainer
            hideSHowGFilter={true}
            columns={columnsProduct || []}
            data={rowDataProduct}
            hidePagination
            isPagination={true}
            isAddOptions={false}
            iscustomPageSize={true}
            isBordered={true}
            isSmall
            customPageSize={10}
            handleOrderClicks={handelOpenAddModal}
            className="custom-header-css table align-middle table-nowrap"
            tableClassName="table-centered align-middle table-nowrap mb-0"
            theadClassName="text-muted table-light"
          />
          <ModalFooter style={{ position: "sticky", bottom: 0 }}>
            <Button type="button" color="light" onClick={handelCloseAddModal}>
              {t("common.close")}
            </Button>
            <Button
              type="button"
              onClick={handelSaveAllChanges}
              color="primary"
              disabled={productList.length === 0 || !watch("delegate_id")}
            >
              {isLoading ? (
                <ClipLoader size={20} color="#fff" />
              ) : (
                t("common.save")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
      <ActionStoreRequest
        handelCloseAddModal={handelCloseModal}
        handelCloseModal={() => {
          handelCloseModal();
        }}
        isShow={true}
        openAddModal={openStoreRequestModal}
        // refetch={refetch}
        selectId={selectId}
        setOpenAddModal={setOPenStoreRequestModal}
      />
    </div>
  );
};
export default withTranslation()(StoreRequest);
