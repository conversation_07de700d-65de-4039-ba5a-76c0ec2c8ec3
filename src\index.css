@font-face {
  font-family: "Material Design Icons";
  src: url("../public/fonts/materialdesignicons-webfont.eot"); /* IE9 */
  src: url("../public/fonts/materialdesignicons-webfont.eot?#iefix")
      format("embedded-opentype"),
    url("../public/fonts/materialdesignicons-webfont.woff2") format("woff2"),
    url("../public/fonts/materialdesignicons-webfont.woff") format("woff"),
    url("../public/fonts/materialdesignicons-webfont.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

.mdi {
  font-family: "Material Design Icons";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  direction: ltr;
}

.icon-button {
  background: transparent;
  border: none;
  cursor: pointer;
}

.dropdown-content {
  background: white;
  border-radius: 6px;
  padding: 4px;
  min-width: 180px;
  box-shadow: 0px 10px 38px rgba(0, 0, 0, 0.12),
    0px 10px 20px rgba(0, 0, 0, 0.08);
  z-index: 1000;
}

.dropdown-item {
  all: unset;
  font-size: 14px;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
}

.dropdown-item:hover {
  background-color: #f1f1f1;
}
