import ApiInstance from "../../constant/api-instance";
import { OFFER_PRICE_ROUTES } from "./route";

const getAll = async ({ limit, page, bond_type }) => {
  const { data } = await ApiInstance.get(OFFER_PRICE_ROUTES.OFFER_PRICE, {
    params: {
      limit,
      page,
      bond_type,
    },
  });
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(
    `${OFFER_PRICE_ROUTES.OFFER_PRICE}/${id}`
  );
  return data;
};

const add = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${OFFER_PRICE_ROUTES.OFFER_PRICE}`,
    payload
  );
  return data;
};

const update = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${OFFER_PRICE_ROUTES.OFFER_PRICE}/${id}`,
    payload
  );
  return data;
};

const Approve = async ({ status, id }) => {
  const { data } = await ApiInstance.post(
    `${OFFER_PRICE_ROUTES.OFFER_PRICE_APPROVE}/${id}`,
    { ...status }
  );
  return data;
};

const deleteBond = async ({ id }) => {
  const { data } = await ApiInstance.delete(
    `${OFFER_PRICE_ROUTES.OFFER_PRICE}/${id}`
  );
  return data;
};

export const offerAPis = {
  getAll,
  getOne,
  deleteBond,
  update,
  add,
  Approve,
};
