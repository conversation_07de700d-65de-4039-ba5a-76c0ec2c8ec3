import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>,
} from "reactstrap";

// Product Modal Components
const ProductModal = ({ isOpen, onClose, onAdd, children, isEditMode }) => {
  const { t } = useTranslation();

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal isOpen={isOpen} backdrop="static">
      <ModalHeader toggle={handleClose}>
        {isEditMode ? t("common.update") : t("common.add")}{" "}
        {t("common.product")}
      </ModalHeader>
      <ModalBody>
        <Row className="g-1">{children}</Row>
        <ModalFooter>
          <Button
            type="button"
            color="light"
            className="btn-sm"
            onClick={handleClose}
          >
            {t("common.close")}
          </Button>
          <Button
            type="button"
            className="btn-sm"
            onClick={onAdd}
            color="primary"
          >
            {isEditMode ? t("common.update") : t("common.add")}
          </Button>
        </ModalFooter>
      </ModalBody>
    </Modal>
  );
};

export default ProductModal;
