import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Col, Container, Row } from "reactstrap";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { delegateAPis } from "../../apis/delegate/api";
import toastr from "toastr";
import { useTranslation } from "react-i18next";
import { delegateQueries } from "../../apis/delegate/query";
import { handleBackendErrors } from "../../helpers/api_helper";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import TextAreaField from "../../components/Common/textArea";
import ClipLoader from "react-spinners/ClipLoader";
import ImageUpload from "../../components/Common/ImageUpload.jsx";
import { FaEyeSlash, FaRegEye } from "react-icons/fa";

const DelegateActions = ({ isShow, selectId, handelClose }) => {
  const { t } = useTranslation();
  const [images, setImages] = useState([]);

  const { data, isLoading } = delegateQueries.useGet({ id: Number(selectId) });

  const handelCancel = () => {
    // navigate("/delegate");
    reset();
    handelClose();
  };

  const schema = yup
    .object({
      full_name: yup.string().required(t("common.field_required")),
      password: selectId
        ? ""
        : yup.string().required(t("common.field_required")),
      confirm_password: selectId
        ? yup
            .string()
            .oneOf(
              [yup.ref("password"), null],
              t("common.passwords_must_match")
            )
        : yup
            .string()
            .oneOf(
              [yup.ref("password"), null],
              t("common.passwords_must_match")
            )
            .required(t("common.field_required")),
      phone_number: yup.string().required(t("common.field_required")),
      email: yup
        .string()
        .nullable()
        .notRequired()
        .email(t("clients.validations.email")),
    })
    .required();

  const initialSTate = {
    full_name: "",
    email: "",
    password: "",
    confirm_password: "",
    phone_number: "",
    description: "",
    status: { value: "1", label: t("common.active") },
    image: null, // Add image default value
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const fieldsNames = [
    {
      id: 0,
      name: "full_name",
      label: t("common.full_name"),
      isRequired: true,
      error: errors.full_name,
      isShow: true,
    },
    {
      id: 2,
      name: "email",
      label: t("common.email"),
      isRequired: true,
      error: errors.email,
      isShow: true,
    },
    // {
    //   id: 4,
    //   name: "phone_number",
    //   label: t("common.phone_number"),
    //   isShow: true,
    //   type: "number",
    //   isRequired: false,
    //   error: errors.phone_number,
    // },
  ];

  const statusOptions = [
    { value: "1", label: t("common.active") },
    { value: "2", label: t("common.in_active") },
  ];

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      // Populate form with role data when loaded
      reset({
        full_name: data?.result.full_name,
        email: data?.result.email,
        password: data?.result.password,
        confirm_password: data?.result.password,
        phone_number: data?.result.phone_number,
        description: data?.result.description,
        status:
          data?.result.status === 1
            ? { value: "1", label: t("common.active") }
            : { value: "2", label: t("common.in_active") },
        image: null, // Add image default value
      });

      // Set the image if it exists in the response
      if (data?.result?.image) {
        setImages([
          {
            data_url: data.result?.image?.url,
            id: data.result?.image?.id,
          },
        ]);
      }
    }
  }, [selectId, isLoading, data?.result, t]);

  const [isShowPassword, setIsShowPassword] = useState(false);

  const togglePassword = () => {
    setIsShowPassword((prev) => !prev);
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Helper function to conditionally append values to formData
  const appendIfNotNull = (key, value, formData) => {
    if (value !== null && value !== undefined && value !== "") {
      formData.append(key, value);
    }
  };

  const handleImageChange = (imageList) => {
    setImages(imageList);
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    const formData = new FormData();
    const jsonData = {};

    // Helper function to conditionally add fields
    const appendIfNotNull = (key, value) => {
      if (value !== null && value !== undefined) {
        jsonData[key] = value;
      }
    };

    // Append fields conditionally
    appendIfNotNull("full_name", data.full_name);
    appendIfNotNull("password", data.password);
    appendIfNotNull("phone_number", data.phone_number);
    appendIfNotNull("description", data.description);
    appendIfNotNull("status", data.status.value);
    appendIfNotNull("email", data.email);

    // Add image if exists
    if (images?.length > 0 && images[0]?.file) {
      formData.append("image", images[0].file);
    }

    // Add all other fields to formData
    Object.keys(jsonData).forEach((key) => {
      formData.append(key, jsonData[key]);
    });

    try {
      const response = await delegateAPis.update({
        formData: formData,
        id: selectId,
      });
      // console.log("response", response);
      // navigate(-1);
      handelClose();
      toastr.success(response?.message);
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    const formData = new FormData();
    const jsonData = {};

    // Helper function to conditionally add fields
    const appendIfNotNull = (key, value) => {
      if (value !== null && value !== undefined) {
        jsonData[key] = value;
      }
    };

    // Append fields conditionally
    appendIfNotNull("full_name", data.full_name);
    appendIfNotNull("password", data.password);
    appendIfNotNull("phone_number", data.phone_number);
    appendIfNotNull("description", data.description);
    appendIfNotNull("status", data.status.value);
    appendIfNotNull("email", data.email);

    // Add image if exists
    if (images?.length > 0 && images[0]?.file) {
      formData.append("image", images[0].file);
    }

    // Add all other fields to formData
    Object.keys(jsonData).forEach((key) => {
      formData.append(key, jsonData[key]);
    });

    try {
      const response = await delegateAPis.add(formData);
      toastr.success(response?.message);
      // navigate(-1);
      handelClose();
      reset(); // Reset form after successful submission
    } catch (error) {
      handleBackendErrors({ error, setError });
      console.error("Error:", error);
    }
  };

  return (
    <div className="">
      <Container fluid>
        <form
          onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
        >
          {isLoading ? (
            <div className="container-loading">
              <ClipLoader color="#ddd" size={50} />
            </div>
          ) : (
            <Row>
              <Row
                className="g-3 table-responsive "
                style={{ maxHeight: "60vh", overflowY: "auto" }}
              >
                {fieldsNames.map(
                  (field) =>
                    field.isShow && (
                      <Col xs={6} key={field.id}>
                        <CustomInput
                          name={field.name}
                          control={control}
                          error={errors[field.name]}
                          disabled={isShow}
                          placeholder={field.label}
                          type={field.type || "text"}
                        />
                      </Col>
                    )
                )}

                <Col xs={6}>
                  <CustomInput
                    name="password"
                    control={control}
                    error={errors.password}
                    isDisabled={isShow}
                    placeholder={t("common.password")}
                    type={isShowPassword ? "text" : "password"}
                    endIcon={
                      <div
                        style={{
                          height: "100%",
                          width: 20,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={togglePassword}
                      >
                        {/* <i className=" ri-eye-off-line"></i> */}
                        {/* <FaRegEye size={18} /> */}
                        {!isShowPassword ? (
                          <FaEyeSlash size={13} />
                        ) : (
                          <FaRegEye size={13} />
                        )}
                      </div>
                    }
                  />
                </Col>

                <Col xs={6}>
                  <CustomInput
                    name="confirm_password"
                    control={control}
                    error={errors.confirm_password}
                    isDisabled={isShow}
                    placeholder={t("common.confirm_password")}
                    type={isShowPassword ? "text" : "password"}
                    endIcon={
                      <div
                        style={{
                          height: "100%",
                          width: 20,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={togglePassword}
                      >
                        {/* <i className=" ri-eye-off-line"></i> */}
                        {/* <FaRegEye size={18} /> */}
                        {!isShowPassword ? (
                          <FaEyeSlash size={13} />
                        ) : (
                          <FaRegEye size={13} />
                        )}
                      </div>
                    }
                  />
                </Col>

                <Col xs={6}>
                  <CustomInput
                    name="phone_number"
                    control={control}
                    error={errors.phone_number}
                    disabled={isShow}
                    placeholder={t("common.phone_number")}
                    type="number"
                  />
                </Col>

                <Col xs={6}>
                  <CustomSelect
                    name="status"
                    control={control}
                    error={errors.status}
                    options={statusOptions}
                    label={t("common.status")}
                    isDisabled={isShow}
                  />
                </Col>

                <Col xs={12} className="mb-2">
                  <TextAreaField
                    name="description"
                    control={control}
                    error={errors.description}
                    disabled={isShow}
                    placeholder={t("common.description")}
                    rows={4}
                  />
                </Col>

                <Col xs={12} className="mb-2">
                  <ImageUpload
                    images={images}
                    onChange={handleImageChange}
                    maxNumber={1}
                    isMultiple={false}
                    isDisabled={isShow}
                    onError={(error) =>
                      handleBackendErrors({ error, setError })
                    }
                    label={t("common.image")}
                  />
                </Col>
              </Row>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                  justifyContent: "end",
                }}
              >
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={handelCancel}
                >
                  {t("common.close")}
                </Button>
                {!isShow && (
                  <Button
                    color="primary"
                    className="waves-effect waves-light primary-button btn-sm"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <ClipLoader color="white" size={15} />
                    ) : selectId ? (
                      t("common.update")
                    ) : (
                      t("common.add")
                    )}
                  </Button>
                )}
              </div>
            </Row>
          )}
        </form>
      </Container>
    </div>
  );
};
export default DelegateActions;
