import React, { useEffect, useState } from "react";
import {
  Dropdown,
  DropdownToggle,
  DropdownMenu,
  Dropdown<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
} from "reactstrap";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
// i18n
import { withTranslation } from "react-i18next";
import toastr from "toastr";
import TypesModel from "../../Common/types-model";
// users
import avatar2 from "../../../assets/images/users/avatar-2.jpg";
import { AuthAPis } from "../../../apis/auth/api";
import { Link, useNavigate } from "react-router-dom";
import ClipLoader from "react-spinners/ClipLoader";
import { useForm } from "react-hook-form";
import { handleBackendErrors } from "../../../helpers/api_helper";
import ImageUpload from "../../Common/ImageUpload";
import CustomInput from "../../Common/Input";
import { authQueries } from "../../../apis/auth/query";
import { Can } from "../../permissions-way/can";
import { IoIosArrowDown, IoMdSettings } from "react-icons/io";
import { MdPassword } from "react-icons/md";
import { CgProfile } from "react-icons/cg";
import { FaEyeSlash, FaRegEye } from "react-icons/fa";

const ProfileMenu = ({ t }) => {
  const [menu, setMenu] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [open, setOpen] = useState(false);
  const [productImages, setProductImages] = useState([]);
  const [changePassword, setOpeChangePassword] = useState(false);
  const [isShowPassword, setIsShowPassword] = useState(false);

  const schema = yup
    .object({
      full_name: open && yup.string().required(t("common.field_required")),
      email:
        open &&
        yup
          .string()
          .nullable()
          .notRequired()
          .email(t("clients.validations.email")),
      password: changePassword
        ? yup.string().required(t("common.field_required"))
        : null,
      confirm_password: changePassword
        ? yup
            .string()
            .oneOf(
              [yup.ref("password"), null],
              t("common.passwords_must_match")
            )
            .required(t("common.field_required"))
        : null,
    })
    .required();

  const {
    control,
    reset,
    formState: { errors, isSubmitting },
    handleSubmit,
    setError,
    setValue,
  } = useForm({
    defaultValues: {
      full_name: "",
      email: "",
      old_password: "",
      password: "",
      confirm_password: "",
    },
    resolver: yupResolver(schema),
  });

  const { data, refetch } = authQueries.useGetProfile({ enable: true });

  useEffect(() => {
    if (data?.data && open) {
      setValue("email", data.data?.email);
      setValue("full_name", data.data?.full_name);
      // Set profile image if it exists
      if (data.data?.image) {
        setProductImages([
          {
            data_url: data.data.image.url,
            id: data.data.image.id,
          },
        ]);
      }
    }
  }, [data, open]);

  const navigate = useNavigate();
  const toggle = () => {
    setMenu(!menu);
  };

  const handelCloseProfiel = () => {
    setOpen(false);
    setProductImages([]);
    reset();
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  const handelUpdateProfile = async (data) => {
    try {
      setIsDeleting(true);
      const jsonData = {
        full_name: data.full_name,
        email: data.email,
      };
      const formData = new FormData();

      productImages.forEach((image, index) => {
        if (image.file) {
          formData.append(`image`, image.file);
        }
      });

      // Add all other fields to formData
      Object.keys(jsonData).forEach((key) => {
        formData.append(key, jsonData[key]);
      });
      const response = await AuthAPis.updateProfile(formData);
      reset();
      toastr.success(response?.message);
      setIsDeleting(false);
      refetch();
      setOpen(false);
      setProductImages([]);
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  const upatePassword = async (data) => {
    try {
      const response = await AuthAPis.updatePassword({
        payload: {
          old_password: data.old_password,
          password: data.password,
          password_confirmation: data.password,
        },
      });
      toastr.success(response?.message);
      setOpeChangePassword(false);
      reset(); // Reset form after successful update
    } catch (error) {
      handleBackendErrors({ error });
    }
  };

  const handleProductImagesChange = (imageList) => {
    setProductImages(imageList);
  };

  const togglePassword = () => {
    setIsShowPassword((prev) => !prev);
  };

  let username = "";
  if (localStorage.getItem("authUser")) {
    const obj = JSON.parse(localStorage.getItem("authUser"));
    const uNm = obj.user.email.split("@")[0];
    username = uNm.charAt(0).toUpperCase() + uNm.slice(1);
  }

  const handelLogout = async () => {
    localStorage.removeItem("bills_selected_tab");
    await AuthAPis.logOut();
    localStorage.removeItem("authUser");
    navigate("/login");
  };

  // Get profile image URL
  const profileImageUrl = data?.data?.image?.url || avatar2;

  return (
    <React.Fragment>
      <Dropdown
        isOpen={menu}
        toggle={toggle}
        className="d-inline-block user-dropdown"
      >
        <DropdownToggle
          tag="button"
          className="btn header-item waves-effect"
          id="page-header-user-dropdown"
        >
          <img
            className="rounded-circle header-profile-user me-1"
            src={profileImageUrl}
            alt="Header Avatar"
          />
          <span className="d-none d-xl-inline-block ms-1 text-transform">
            {username}
          </span>
          {/* <i className="mdi mdi-chevron-down d-none ms-1 d-xl-inline-block"></i> */}
          <IoIosArrowDown size={12} className="mx-1" />
        </DropdownToggle>
        <DropdownMenu className="dropdown-menu-end">
          <DropdownItem onClick={() => setOpen(true)}>
            {/* <i className="ri-user-line align-middle me-1"></i>{" "} */}
            <CgProfile size={14} className="mx-1" />

            {t("common.profile")}
          </DropdownItem>
          <DropdownItem
            onClick={() => setOpeChangePassword(true)}
            className="d-block"
          >
            {/* <span className="badge badge-success float-end mt-1">11</span> */}
            {/* <i className="ri-settings-2-line align-middle me-1"></i>{" "} */}

            <MdPassword size={14} className="mx-1" />

            {t("common.update_password")}
          </DropdownItem>
          <Can permission={"setting.vat" || "setting.index"}>
            <Link to="/settings">
              <DropdownItem className="d-block">
                {/* <span className="badge badge-success float-end mt-1">11</span> */}
                {/* <i className="ri-settings-2-line align-middle me-1"></i>{" "} */}
                <IoMdSettings size={14} className="mx-1" />

                {t("common.settings")}
              </DropdownItem>
            </Link>
          </Can>
          {/* <DropdownItem href="#">
            <i className="ri-lock-unlock-line align-middle me-1"></i> Lock
            screen
          </DropdownItem> */}
          <DropdownItem divider />
          <DropdownItem className="text-danger" onClick={handelLogout}>
            {/* <i className="ri-shut-down-line align-middle me-1 text-danger"></i>{" "} */}
            <IoMdSettings size={14} className="mx-1" />

            {t("common.logout")}
          </DropdownItem>
        </DropdownMenu>

        <Modal isOpen={open} backdrop="static">
          <ModalHeader toggle={handelCloseProfiel}>
            {t("common.update_profile")}
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit(handelUpdateProfile)}>
              <Row>
                <Col xs={6}>
                  <div className="mb-3">
                    <CustomInput
                      control={control}
                      name="full_name"
                      placeholder={t("common.full_name")}
                      error={errors.full_name}
                    />
                  </div>
                </Col>
                <Col xs={6}>
                  <div className="mb-3">
                    <CustomInput
                      control={control}
                      name="email"
                      placeholder={t("common.email")}
                      error={errors.email}
                    />
                  </div>
                </Col>

                <Col xs={12} className="mb-2">
                  <ImageUpload
                    images={productImages}
                    onChange={handleProductImagesChange}
                    maxNumber={1}
                    isMultiple={false}
                    onError={(error) =>
                      handleBackendErrors({ error, setError })
                    }
                    label={t("common.prodile_iamge")}
                  />
                </Col>
              </Row>
              <ModalFooter>
                <Button
                  type="button"
                  color="light"
                  disabled={isDeleting}
                  className="btn-sm"
                  onClick={() => {
                    handelCloseProfiel();
                  }}
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  disabled={isDeleting}
                  color="primary"
                  type="submit"
                  className="btn-sm"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.save")
                  )}
                </Button>
              </ModalFooter>
            </form>
          </ModalBody>
        </Modal>
        <TypesModel
          open={changePassword}
          handelClose={() => {
            setOpeChangePassword(false);
            reset(); // Reset form when closing modal
          }}
          hideAll={true}
          content={
            <form onSubmit={handleSubmit(upatePassword)}>
              <h1 className="fs-5 mb-4">{t("common.update_password")}</h1>
              <Row className="mb-2 gap-2">
                <Col xs={12}>
                  {/* <CustomInput
                  control={control}
                  name="old_password"
                  label={t("common.old_password")}
                  error={errors?.old_password}
                  type="password"
                /> */}
                  <CustomInput
                    name="old_password"
                    control={control}
                    error={errors.old_password}
                    placeholder={t("common.old_password")}
                    type={isShowPassword ? "text" : "password"}
                    endIcon={
                      <div
                        style={{
                          height: "100%",
                          width: 400,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={togglePassword}
                      >
                        {/* <i className=" ri-eye-off-line"></i> */}
                        {!isShowPassword ? (
                          <FaEyeSlash size={13} />
                        ) : (
                          <FaRegEye size={13} />
                        )}
                      </div>
                    }
                  />
                </Col>

                <Col xs={12}>
                  <CustomInput
                    name="password"
                    control={control}
                    error={errors.password}
                    placeholder={t("common.password")}
                    type={isShowPassword ? "text" : "password"}
                    endIcon={
                      <div
                        style={{
                          height: "100%",
                          width: 20,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={togglePassword}
                      >
                        {/* <i className=" ri-eye-off-line"></i> */}
                        {!isShowPassword ? (
                          <FaEyeSlash size={13} />
                        ) : (
                          <FaRegEye size={13} />
                        )}
                      </div>
                    }
                  />
                </Col>
                <Col xs={12}>
                  <CustomInput
                    name="confirm_password"
                    control={control}
                    error={errors.confirm_password}
                    placeholder={t("common.confirm_password")}
                    type={isShowPassword ? "text" : "password"}
                    endIcon={
                      <div
                        style={{
                          height: "100%",
                          width: 20,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                        onClick={togglePassword}
                      >
                        {/* <i className=" ri-eye-off-line"></i> */}
                        {!isShowPassword ? (
                          <FaEyeSlash size={13} />
                        ) : (
                          <FaRegEye size={13} />
                        )}
                      </div>
                    }
                  />
                </Col>
              </Row>
              <div className="d-flex gap-2">
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={() => {
                    setOpeChangePassword(false);
                    reset();
                  }}
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  disabled={isSubmitting}
                  type="submit"
                  color="primary"
                  className="btn-sm"
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.save")
                  )}
                </Button>
              </div>
            </form>
          }
        />
      </Dropdown>
    </React.Fragment>
  );
};

export default withTranslation()(ProfileMenu);
