# إصلاح مشكلة selectedTotalPrice في تعديل المنتج

## المشكلة
عند تعديل قيمة السعر (price) في تعديل منتج، لا يتم تحديث `selectedTotalPrice` فوراً، مما يؤدي إلى عدم تطابق القيم المعروضة.

## السبب
كان `selectedTotalPrice` يتم حسابه فقط في `useEffect` الذي يعتمد على `productList` و `watch("total_discount_value")`. عند تعديل السعر في المودال، كان `product_total` يتم تحديثه لكن `productList` لم يكن يتم تحديثه حتى يتم الضغط على زر الحفظ.

## الحل المطبق

### 1. تحديث useEffect للسعر والكمية
```javascript
useEffect(() => {
  if (watch("unit_price") && watch("quant")) {
    const newProductTotal = Number(watch("unit_price")) * Number(watch("quant"));
    setValue("product_total", newProductTotal);

    // If we're editing a product, update the productList and recalculate selectedTotalPrice
    if (selectedProductId) {
      setProductList((prev) => {
        const updatedList = prev.map((item) => {
          if (item.product_id === selectedProductId) {
            const discountValue = Number(watch("product_discount_value")) || 0;
            const discountPercentage = Number(watch("product_discount_percentage")) || 0;
            const netPrice = discountValue > 0 ? newProductTotal - discountValue : newProductTotal;

            return {
              ...item,
              quant: Number(watch("quant")),
              total: newProductTotal,
              unit_price: Number(watch("unit_price")),
              net_price: netPrice,
              discount_value: discountValue,
              discount_percentage: discountPercentage,
              discount: discountValue,
            };
          }
          return item;
        });
        return updatedList;
      });
    }
  }
}, [watch("unit_price"), watch("quant"), selectedProductId]);
```

### 2. تحديث useEffect للخصم بالنسبة المئوية
```javascript
useEffect(() => {
  if (lastProductDiscountUpdate.current === "value") return;
  const percentage = Number(productDiscountPercentage);
  const totalValue = Number(productTotal);
  if (!percentage && percentage !== 0) return;
  if (percentage >= 0 && percentage <= 100 && totalValue > 0) {
    const newValue = Math.round((percentage * totalValue) / 100);
    lastProductDiscountUpdate.current = "percentage";
    setValue("product_discount_value", newValue > 0 ? newValue : null, {
      shouldValidate: true,
    });
    
    // If we're editing a product, update the productList
    if (selectedProductId) {
      setProductList((prev) => {
        const updatedList = prev.map((item) => {
          if (item.product_id === selectedProductId) {
            const discountValue = newValue > 0 ? newValue : 0;
            const netPrice = discountValue > 0 ? totalValue - discountValue : totalValue;
            
            return {
              ...item,
              discount_value: discountValue,
              discount_percentage: percentage,
              discount: discountValue,
              net_price: netPrice,
            };
          }
          return item;
        });
        return updatedList;
      });
    }
  }
}, [productDiscountPercentage, selectedProductId, productTotal]);
```

### 3. تحديث useEffect للخصم بالقيمة
```javascript
useEffect(() => {
  if (lastProductDiscountUpdate.current === "percentage") return;
  const value = Number(productDiscountValue);
  const totalValue = Number(productTotal);
  if (!value && value !== 0) return;
  if (value >= 0 && totalValue > 0) {
    const newPercentage = Math.round((value / totalValue) * 100);
    lastProductDiscountUpdate.current = "value";
    setValue(
      "product_discount_percentage",
      newPercentage > 0 ? newPercentage : null,
      { shouldValidate: true }
    );
    
    // If we're editing a product, update the productList
    if (selectedProductId) {
      setProductList((prev) => {
        const updatedList = prev.map((item) => {
          if (item.product_id === selectedProductId) {
            const discountValue = value;
            const netPrice = discountValue > 0 ? totalValue - discountValue : totalValue;
            
            return {
              ...item,
              discount_value: discountValue,
              discount_percentage: newPercentage > 0 ? newPercentage : 0,
              discount: discountValue,
              net_price: netPrice,
            };
          }
          return item;
        });
        return updatedList;
      });
    }
  }
}, [productDiscountValue, selectedProductId, productTotal]);
```

## النتيجة
الآن عند تعديل أي من القيم التالية في مودال تعديل المنتج:
- السعر الوحدة (unit_price)
- الكمية (quant)
- قيمة الخصم (product_discount_value)
- نسبة الخصم (product_discount_percentage)

سيتم تحديث `productList` فوراً، مما يؤدي إلى إعادة حساب `selectedTotalPrice` تلقائياً عبر الـ `useEffect` الموجود في السطر 1015-1033.

## اختبار الإصلاح
1. افتح صفحة تعديل عرض سعر
2. اضغط على تعديل منتج موجود
3. غير السعر أو الكمية أو الخصم
4. لاحظ أن المجموع الكلي (selectedTotalPrice) يتم تحديثه فوراً
