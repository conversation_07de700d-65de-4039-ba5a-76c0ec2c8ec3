import { <PERSON><PERSON>, <PERSON>, CardBody, Col, Container, Label, Row } from "reactstrap";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import { useLocation, useNavigate } from "react-router-dom";
import { clientsQueries } from "../../apis/clients/query";
import { useForm } from "react-hook-form";
import { BillQueries } from "../../apis/bills/query";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";

const BillsActions = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);
  const [productList, setProductList] = useState([]);

  const { data, isLoading } = BillQueries.useGetOneFollowUp({
    id: Number(selectId),
  });

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const breadcrumbItems = [{ title: "Bills", link: "/bills" }];
  const navigate = useNavigate();

  const handelCancel = () => {
    navigate("/bills");
    reset();
  };

  const initialSTate = {
    client_id: null,
    // task_id: null,
    bill_date: "",
    return_date: "",
    notes: "",
    res_name: "",
    total: 0,
    discount_percentage: 0,
    discount_value: 0,
    change_oil: 1,
    quant: 0,
    product_id: null,
    product_note: "",
    product_gift: 0,
    product_total: 0,
    product_price: 1,
    discount_product: 0,
    discount_product_type: "",
  };

  const {
    reset,
    formState: { errors },
    register,
    watch,
  } = useForm({
    defaultValues: { ...initialSTate },
  });

  const fieldsNames = [
    {
      name: "bill_date",
      isRequired: false,
      errorMessage: errors?.bill_date?.message,
      label: t("bills.bill_date"),
      type: "date",
      showIn: true,
    },
    {
      name: "follow_up_number",
      isRequired: false,
      errorMessage: errors?.bill_date?.message,
      label: t("bills.follow_up"),
      type: "text",
      showIn: true,
    },
    {
      name: "res_name",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("bills.response_name"),
      type: "text",
      showIn: true,
    },
    {
      name: "delegate",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("common.delegate"),
      type: "text",
      showIn: true,
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "note",
      label: t("common.note"),
      isRequired: false,
      showIn: true,
    },
  ];

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      const returnedProduct = [];
      data?.result.items.map((item) => {
        returnedProduct.push({
          product_id: item.product_id,
          product_name: item.product_name,
          quant: item.actual_quant,
          note: item.notes,
          checked_quant: item.checked_quant,
        });
      });
      setProductList(returnedProduct);
      // Convert the date from backend to 'YYYY-MM-DD'
      const formattedDate = data?.result.bill_date.split("T")[0]; // '2024-11-26'
      // Populate form with role data when loaded
      reset({
        client_id: data?.result.client.id,
        bill_date: formattedDate,
        res_name: data?.result.res_name,
        note: data?.result.notes,
        follow_up_number: data?.result.follow_up_number,
        delegate: data?.result.delegate,
      });
    }
  }, [selectId, isLoading, data]);

  const columns = [
    {
      Header: t("common.id"),
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.product"),
      accessor: "product_name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bills.checked_quant"),
      accessor: "checked_quant",
      disableFilters: true,
      filterable: false,
    },
  ];
  const rowData = useMemo(
    () =>
      productList.length > 0
        ? productList
            .map((item, index) => ({
              product_id: item.product_id,
              id_toShow: index + 1,
              product_name: item.product_name,
              quant: item.quant,
              checked_quant: item.checked_quant,
            }))
            .reverse()
        : [],
    [productList]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs title={"Bills"} breadcrumbItems={breadcrumbItems} />
        <Card>
          <CardBody>
            <Row>
              <form>
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <>
                    <Row>
                      <Col xs={6}>
                        <div className="mb-4">
                          <Label className="form-label" htmlFor="total">
                            {t("clients.clients")}
                          </Label>
                          <select
                            placeholder=""
                            className="form-control"
                            {...register("client_id", { required: true })} // register the select field
                          >
                            {clients?.result.map((item) => (
                              <option value={item.id} key={item.id}>
                                {item?.full_name + "/" + item.company_name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </Col>
                      {fieldsNames.map(
                        (field) =>
                          field.showIn && (
                            <Col xs={6} key={field.id}>
                              <div className="mb-2">
                                <Label
                                  className="form-label"
                                  htmlFor={field.name}
                                >
                                  {field.label}
                                </Label>
                                <input
                                  name={field.name}
                                  {...register(`${field.name}`, {
                                    required: true,
                                  })}
                                  step="any" // Allows decimal input
                                  placeholder="...."
                                  type={field.type}
                                  className={`form-control ${
                                    field.errorMessage ? "is-invalid" : ""
                                  }`}
                                  id="title-in-english"
                                />
                                {field.errorMessage && (
                                  <div className="invalid-feedback">
                                    {field.errorMessage}
                                  </div>
                                )}
                              </div>
                            </Col>
                          )
                      )}
                      {textAreaField.map((field) => (
                        <Col key={field.id} xs={12}>
                          <div className="mb-2">
                            <Label className="form-label" htmlFor={field.name}>
                              {field.label}
                            </Label>
                            <textarea
                              id={field.name}
                              type="text"
                              rows={4}
                              className={`form-control`}
                              placeholder="...."
                              {...register(`${field.name}`)}
                            />
                          </div>
                        </Col>
                      ))}
                    </Row>
                  </>
                )}
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <Button type="button" color="light" onClick={handelCancel}>
                    {t("common.close")}
                  </Button>
                </div>
              </form>
            </Row>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <TableContainer
              hideSHowGFilter={true}
              columns={columns || []}
              data={rowData}
              hidePagination
              isPagination={true}
              isAddOptions={false}
              addTitle=""
              iscustomPageSize={true}
              isBordered={true}
              customPageSize={10}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
            />
          </CardBody>
        </Card>
      </Container>
    </div>
  );
};
export default BillsActions;
