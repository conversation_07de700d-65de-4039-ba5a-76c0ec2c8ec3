import React from "react";
import { Container, <PERSON>, <PERSON> } from "reactstrap";
import { Link } from "react-router-dom";

//Import images
import errorImg from "../../assets/images/error-img.png";
import { useTranslation } from "react-i18next";

const ErrorPage = () => {
  const { t, i18n } = useTranslation();
  return (
    <React.Fragment>
      <div className="my-5 pt-5">
        <Container>
          <Row>
            <Col lg={12}>
              <div className="text-center my-5">
                {i18n.language === "ar" ? (
                  <h1
                    className="fw-bold text-error"
                    // style={{ transform: "rotateY(180deg)" }}
                  >
                    0{" "}
                    <span
                      style={{ fontSize: 120 }}
                      className="error-text fw-bold"
                    >
                      0
                    </span>{" "}
                    5
                  </h1>
                ) : (
                  <h1
                    className="fw-bold text-error"
                    // style={{ transform: "rotateY(180deg)" }}
                  >
                    5{" "}
                    <span
                      style={{ fontSize: 120 }}
                      className="error-text fw-bold"
                    >
                      0
                    </span>{" "}
                    0
                  </h1>
                )}
                <h3 className="text-uppercase">{t("common.interval_error")}</h3>
                <div className="mt-5 text-center">
                  <Link
                    to="/dashboard"
                    className="btn btn-primary waves-effect waves-light"
                  >
                    {t("common.back_to_dashboard")}
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </React.Fragment>
  );
};

export default ErrorPage;
