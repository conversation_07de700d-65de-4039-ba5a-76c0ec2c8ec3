import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "reactstrap";
import { useForm } from "react-hook-form";
import toastr from "toastr";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import ClipLoader from "react-spinners/ClipLoader";
import { delegateQueries } from "../../apis/delegate/query";
import { useStoreOperationQueries } from "../../apis/store-operation/query";
import { useStoreRequest } from "../../apis/store-request/query";
import { storeRequestApis } from "../../apis/store-request/api";
import { productQueries } from "../../apis/products/query";
import { useEffect, useMemo, useRef, useState } from "react";
import { ProductTypesQueries } from "../../apis/types/product-types/query";

import "./store-request.css";
import { handleBackendErrors } from "../../helpers/api_helper";
import TableContainer from "../../components/Common/TableContainer";
import { useTranslation } from "react-i18next";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import { MdDeleteSweep } from "react-icons/md";
import { FaPenToSquare } from "react-icons/fa6";
const ClientActions = ({
  isShow,
  selectId,
  handelCloseAddModal,
  setOpenAddModal,
  refetch,
}) => {
  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [searchQuery, setSearchQuery] = useState(""); // Add this line for search state
  const [productList, setProductList] = useState([]);

  const { data, isLoading } = useStoreRequest.useOne({
    id: Number(selectId),
  });
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });
  const { data: storeOperation } = useStoreOperationQueries.useGetAllOperation(
    {}
  );
  const initialSTate = {
    status: 1,
    transaction: null,
    delegate_id: null,
    store_operation_id: null,
    products: [], // Add image default value
    notes: "",
    maintenance_quantity: 0,
    ProductTypeId: { label: "", value: 0 },
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, dirtyFields },
    register,
    setError,
    control,
    watch,
    setValue,
    clearErrors,
    getValues,
    trigger,
    resetField,
  } = useForm({
    defaultValues: { ...initialSTate },
    // resolver: yupResolver(schema),
  });

  const { t, i18n } = useTranslation();

  const handelCancel = () => {
    reset();
    handelCloseAddModal();
    reset();
    setValue("store_operation_id", null);
  };

  const schema = yup
    .object({
      // delegate_id: yup.number().required("this field required"),
      // store_operation_id: yup.number().required("this field required"),
    })
    .required();

  const TransactionOptions = storeOperation?.result
    ?.filter((val) => val.is_default === 1)
    ?.map((item) => ({
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
      value: item?.trasnaction_type,
    }));

  const NotDefaultTransactionOptions = storeOperation?.result
    ?.filter((val) => val.is_default === 0)
    ?.filter((val) => val.trasnaction_type === watch("transaction")?.value)
    ?.map((item) => ({
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
      value: item?.id,
    }));

  const { data: products } = productQueries.useGetAll({
    status: 1,
    searchParams: searchQuery ? { "filter[name]": searchQuery } : undefined,
  });

  // UseEffect for loading Role data
  useEffect(() => {
    if (selectId > 0 && !isLoading && data?.result) {
      setProductList(
        data.result.details.map((item) => ({
          product_id: item.id,
          id: item.id,
          quant: Number(item.quant),
          productName: item.title,
          maintaince_quant: Number(item.maintaince_quant),
          productType: {
            label: item.product_type?.title,
            value: item.product_type?.id,
          },
        }))
      );
      // Populate form with role data when loaded
      reset({
        status: data?.result.status,
        delegate_id: {
          label: data?.result.delegate?.full_name,
          value: data?.result.delegate?.id,
        },
        store_operation_id: {
          label:
            i18n.language === "eng"
              ? data?.result.store_operation?.title?.en
              : data?.result.store_operation?.title?.ar,
          value: data?.result.store_operation?.id,
        },
        transaction: TransactionOptions?.find(
          (item) => Number(item.value) === data?.result?.transaction
        ),
      });
    }
  }, [selectId, isLoading, data]);

  // Reset store_operation_id when transaction changes
  useEffect(() => {
    if (dirtyFields?.transaction) {
      resetField("store_operation_id", { defaultValue: null });
    }
  }, [dirtyFields]);

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  // Convert data to FormData and send it
  const UpdateFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("store-request.add_products_validation"));
      return;
    }
    if (
      (Number(data?.transaction) === 3 || Number(data?.transaction) === 4) &&
      !data?.delegate_id
    ) {
      toastr.error(t("store-request.select_delegate_validation"));
      return;
    }
    if (
      (Number(data?.transaction) === 1 || Number(data?.transaction) === 2) &&
      !data?.store_operation_id
    ) {
      toastr.error(t("store-request.store_operation_validation"));
      return;
    }
    if (
      watch("quant") ||
      watch("maintaince_quant") ||
      watch("products")?.label
    ) {
      toastr.error(t("store-request.val_error"));
      return;
    }

    try {
      const filterDataToSend = {
        transaction: Number(data.transaction?.value), // StoreRequestTrasnactionEnum
        status: Number(data.status),
        store_operation_id: data.store_operation_id?.value,
        delegate_id: data.delegate_id?.value, // nullable,
        products: productList,
      };
      const response = await storeRequestApis.update({
        id: selectId,
        payload: filterDataToSend,
      });
      toastr.success(response.message);
      handelCloseAddModal();
      setOpenAddModal(false);
      refetch();
      reset();
      setValue("store_operation_id", null);
    } catch (error) {
      // toastr.error("There are errors");
      handleBackendErrors({ error, setError });
      console.log("error", error);
    }
  };

  const addFun = async (data) => {
    if (productList.length === 0) {
      toastr.error(t("store-request.add_products_validation"));
      return;
    }
    if (
      (Number(data?.transaction) === 3 || Number(data?.transaction) === 4) &&
      !data?.delegate_id
    ) {
      toastr.error(t("store-request.select_delegate_validation"));
      return;
    }
    if (
      (Number(data?.transaction) === 1 || Number(data?.transaction) === 2) &&
      !data?.store_operation_id
    ) {
      toastr.error(t("store-request.store_operation_validation"));
      return;
    }

    if (
      watch("quant") ||
      watch("maintaince_quant") ||
      watch("products")?.label
    ) {
      toastr.error(t("store-request.val_error"));
      return;
    }
    try {
      const prepeareToSend = productList.map((item) => ({
        ...item,
        price: 0,
      }));
      const filterDataToSend = {
        transaction: data.transaction?.value, // StoreRequestTrasnactionEnum
        status: 1,
        store_operation_id: data.store_operation_id?.value,
        delegate_id: data.delegate_id?.value, // nullable,
        products: prepeareToSend,
      };
      const response = await storeRequestApis.add(filterDataToSend);
      toastr.success(response.message);
      // navigate(-1);
      handelCloseAddModal();
      setOpenAddModal(false);
      refetch();
      setValue("store_operation_id", null);
      reset(); // Reset form after successful submission
    } catch (error) {
      // console.error("Error:", error);
      handleBackendErrors({ error, setError });
    }
  };

  const [setSelected, setSelectedId] = useState(0);
  const [selectMinQuant, setSelectedMinQuant] = useState(0);
  const [selectMaxQuant, setSelectedMaxQuant] = useState(0);
  const [quant, setQuant] = useState(0);
  const [quantAmount, setQuantAmount] = useState(0);
  const [productName, setProductName] = useState("");
  const [hasError, setHasError] = useState({ value: false, text: "" });
  const [addNewProductFromSelect, setAddNewProductFromSelect] = useState({});
  const [selectedQuant, setSelectedQuant] = useState(null);

  const isUpdate = useRef(null);

  const handeSelectedIProducts = ({ id, value, currentProductList }) => {
    if (isUpdate.current && id) {
      const selectedFromProductList = currentProductList.find(
        (item) => item.id === id
      );
      setProductName(selectedFromProductList?.productName);
      setValue("ProductTypeId", {
        label: selectedFromProductList.productType.label,
        value: selectedFromProductList.productType.value,
      });
      setValue("products", {
        label: selectedFromProductList?.productName,
        value: selectedFromProductList.product_id,
      });
      setValue(
        "maintenance_quantity",
        selectedFromProductList.maintaince_quant
      );
    }

    if (id && !isUpdate.current) {
      const selectedProduct = products?.result.find((item) => item.id === id);
      if (selectedProduct) {
        setQuant(selectedProduct.quant);
        setSelectedQuant(selectedProduct.quant);
        setProductName(selectedProduct.name);
        setSelectedMinQuant(selectedProduct.min_quant);
        setSelectedMaxQuant(selectedProduct.max_quant);
        setQuantAmount(selectedProduct.div_quant);
        setValue("maintenance_quantity", 0);
        setValue("products", { label: selectedProduct.name, value: id });
      }
    } else if (isUpdate.current && value) {
      const selectedProduct = productList?.find(
        (item) => item.productName === value
      );
      setQuant(selectedProduct.quant);
      setValue("quant", selectedProduct.quant);
      setValue("maintenance_quantity", selectedProduct.maintaince_quant);
      setValue("products", {
        label: selectedProduct.productName,
        value: selectedProduct.productName,
      });
    }
    setHasError({ text: "", value: false });
  };

  useEffect(() => {
    if (setSelected && !isUpdate.current) {
      handeSelectedIProducts({ id: setSelected });
      // Always set maintenance_quantity to 0 for new products
      setValue("maintenance_quantity", 0);
    }
  }, [setSelected, isUpdate]);

  useEffect(() => {
    if (products?.result?.length > 0) {
      setOptionGroup(
        products.result
          .filter(
            (product) =>
              !productList.some((item) => item.product_id === product.id)
          )
          .map((item) => ({
            label: item.name,
            value: item.id,
          }))
      );
    }
  }, [products?.result, productList]);

  // Custom text for new option creation
  const formatCreateLabel = (inputValue) =>
    `${t("store-request.add_new_option")}: "${inputValue}"`;

  const handelAddProduct = () => {
    if (
      (Number(watch("maintenance_quantity")) <= 0 &&
        Number(watch("quant")) <= 0) ||
      hasError?.value
    ) {
      toastr.error(t("store-request.add_qunat_validation"));
      return;
    }

    // Create new product to add to the list
    const newProduct = isUpdate.current
      ? {
          product_id: watch("products")?.value || setSelected,
          id: watch("products")?.value || setSelected,
          quant: Number(watch("quant")),
          productName: watch("products")?.label || productName,
          productType: {
            label: watch("ProductTypeId")?.label,
            value: watch("ProductTypeId")?.value,
          },
          maintaince_quant: Number(watch("maintenance_quantity")),
        }
      : addNewProductFromSelect?.label
      ? {
          product_id: null,
          id: null,
          quant: Number(watch("quant")),
          name: addNewProductFromSelect?.label,
          maintaince_quant: Number(watch("maintenance_quantity")),
          productType: {
            label: watch("productType")?.label,
            value: watch("productType")?.value,
          },
        }
      : {
          product_id: setSelected,
          id: setSelected,
          quant: Number(watch("quant")),
          productName: productName,
          productType: {
            label: watch("ProductTypeId")?.label,
            value: watch("ProductTypeId")?.value,
          },
          maintaince_quant: Number(watch("maintenance_quantity")),
        };

    setProductList((prev) => [...prev, newProduct]);

    // Reset all form values
    setProductName("");
    setQuantAmount(0);
    setSelectedQuant(null);
    setQuant(0);
    setSelectedMaxQuant(0);
    setSelectedMinQuant(0);
    setSelectedId(0);
    setValue("quant", "");
    setValue("products", null);
    setValue("ProductTypeId", { label: "", value: 0 });
    setValue("maintenance_quantity", 0);
    isUpdate.current = null;
    setAddNewProductFromSelect(undefined);
    setHasError({ text: "", value: false });
  };

  const handelRemoveFromList = (id) => {
    setProductList((prev) => prev.filter((item) => item.id !== id));
  };

  const handelUpdate = ({ id, isNew, value }) => {
    isUpdate.current = true;

    if (isNew) {
      handeSelectedIProducts({ value: value });
      setProductList((prev) =>
        prev.filter((item) => item.productName !== value)
      );
    } else {
      if (isUpdate.current && value) {
        toastr.error(t("store-request.validation.there_are_product_to_update"));
        return;
      } else {
        const selectedProduct = productList.find((item) => item.id === id);
        if (selectedProduct) {
          handeSelectedIProducts({
            id: id,
            currentProductList: productList,
          });
          setProductList((prev) => prev.filter((item) => item.id !== id));
          setSelectedId(id);
          setQuant(selectedProduct.quant);
          setValue("quant", selectedProduct.quant);
          setValue("maintenance_quantity", selectedProduct.maintaince_quant);
        }
      }
    }
  };

  const [isAccepted, setIsAccepted] = useState(false);
  const [isRejected, setIsRejected] = useState(false);

  const rejectStatus = async () => {
    if (!watch("notes")) {
      // Set an error if notes are empty
      setError("notes", {
        type: "manual",
        message: t("store-request.validation.rejections_validation"),
      });
      return; // Do not proceed to API call
    }
    try {
      setIsRejected(true);
      clearErrors();
      const response = await storeRequestApis.reject({
        id: selectId,
        note: watch("notes"),
      });
      toastr.success(response.message);
      // navigate(-1);
      setIsRejected(false);
      handelCLoseModal();
      setOpenAddModal(false);
      setSelectedId(0);
      refetch();
      handelCloseAddModal();
      reset();
      setValue("store_operation_id", null);
    } catch (error) {
      setIsRejected(false);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const handelAcceptRequest = async () => {
    const prepeareToSend = productList.map((item) => ({
      ...item,
      price: 0,
    }));
    const filterDataToSend = {
      transaction: Number(watch("transaction")?.value), // StoreRequestTrasnactionEnum
      store_operation_id: Number(data.store_operation_id?.value),
      delegate_id: Number(watch("delegate_id")?.value), // nullable,
      products: prepeareToSend,
      note: watch("notes"),
    };
    try {
      setIsAccepted(true);
      const response = await storeRequestApis.accept({
        id: selectId,
        payload: {
          ...filterDataToSend,
        },
      });
      // toastr.success("Accept Request Done");
      toastr.success(response.message);
      // navigate(-1);
      setIsAccepted(false);
      refetch();
      handelCLoseModal();
      setSelectedId(0);
      setOpenAddModal(false);
      setSelectedId(0);
      handelCloseAddModal();
      reset();
      setValue("store_operation_id", null);
    } catch (error) {
      handleBackendErrors({ error, setError });
      setIsAccepted(false);
    }
  };

  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [opeAcceptModal, setOpeAcceptModal] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setOpeAcceptModal(false);
  };

  const columns = [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.name"),
      accessor: "name",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.quant"),
      accessor: "quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.maintenance_quantity"),
      accessor: "maintaince_quant",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.actions"),
      accessor: (cellProps) => {
        return (
          <>
            {!isShow && (
              <div className="d-flex align-items-center">
                <div
                  className="me-3 text-primary"
                  onClick={() => {
                    if (cellProps.id > 0) {
                      handelUpdate({
                        id: cellProps.id,
                        isNew: false,
                      });
                    } else {
                      handelUpdate({
                        isNew: true,
                        id: 0,
                        value: cellProps.name,
                      });
                      setAddNewProductFromSelect({
                        label: cellProps.name,
                        value: cellProps.name,
                      });
                    }
                  }}
                  style={{ cursor: "pointer" }}
                >
                  {/* <i className="mdi mdi-pencil pointer-event font-size-14"></i> */}
                  <FaPenToSquare size={14} />
                </div>
                <div
                  onClick={() => handelRemoveFromList(cellProps.id)}
                  className="text-danger"
                  style={{ cursor: "pointer" }}
                >
                  {/* <i className="mdi mdi-trash-can font-size-14 "></i> */}
                  <MdDeleteSweep size={18} />
                </div>
              </div>
            )}
          </>
        );
      },
      disableFilters: true,
      filterable: false,
    },
  ];

  const rowData = useMemo(
    () =>
      productList?.length > 0
        ? productList
            .map((item, index) => ({
              id: item.id,
              id_toShow: index + 1,
              maintaince_quant: item.maintaince_quant,
              name: item.productName,
              quant: item.quant,
            }))
            .reverse()
        : [],
    [productList]
  );

  return (
    <div>
      <Row>
        <form
          onSubmit={selectId ? handleSubmit(UpdateFun) : handleSubmit(addFun)}
        >
          {isLoading ? (
            <div className="container-loading">
              <ClipLoader color="#ddd" size={50} />
            </div>
          ) : (
            <div
            // style={{ maxHeight: 450, overflowY: "auto", overflowX: "hidden" }}
            >
              <Row>
                <Col xs={6}>
                  <div className="mb-4">
                    <CustomSelect
                      control={control}
                      label={t("store-request.transaction")}
                      options={TransactionOptions}
                      name="transaction"
                      isDisabled={isShow}
                    />
                  </div>
                </Col>
                {(Number(watch("transaction")?.value) === 3 ||
                  Number(watch("transaction")?.value) === 4) && (
                  <Col xs={6}>
                    <div className="mb-4">
                      <CustomSelect
                        control={control}
                        label={t("common.delegate")}
                        options={delegates?.result.map((item) => ({
                          label: item.full_name,
                          value: item.id,
                        }))}
                        name="delegate_id"
                        isDisabled={isShow}
                      />
                    </div>
                  </Col>
                )}
                {(Number(watch("transaction")?.value) === 1 ||
                  Number(watch("transaction")?.value) === 2) && (
                  <Col xs={6}>
                    <div className="mb-4">
                      <CustomSelect
                        control={control}
                        label={t("types.store_operation.store_operation")}
                        options={NotDefaultTransactionOptions}
                        name="store_operation_id"
                        isDisabled={isShow}
                      />
                    </div>
                  </Col>
                )}
                <Col
                  xs={12}
                  style={{
                    border: "1px solid #ccc",
                    width: "98%",
                    margin: "auto",
                    padding: 10,
                    marginBottom: 10,
                    borderRadius: 5,
                  }}
                >
                  {!isShow && (
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns: "1fr 1fr 1fr",
                        gap: 12,
                      }}
                    >
                      {/* <div className="mb-4">
                        <CustomSelect
                          control={control}
                          label={t("types.product_types.product_type")}
                          options={productTypesList?.result?.map((item) => ({
                            label:
                              i18n.language === "eng"
                                ? item.title.en
                                : item.title.ar,
                            value: item.id,
                          }))}
                          name="ProductTypeId"
                          isDisabled={isShow}
                        />
                      </div> */}
                      <div>
                        {/* <p>{t("common.product")}</p> */}
                        {!isShow && (
                          <Col xs={12}>
                            <div className="mb-4">
                              <CustomSelect
                                name="products"
                                control={control}
                                label={t("common.product")}
                                // placeholder="Select Product"
                                defaultValue={[]}
                                isMulti={false}
                                isClearable
                                isDisabled={isShow}
                                creatable={
                                  Number(watch("transaction")?.value) === 1
                                }
                                options={
                                  Number(watch("transaction")?.value) !== 1
                                    ? optionGroup.filter(
                                        (option) =>
                                          !productList.some(
                                            (product) =>
                                              product.product_id ===
                                              option.value
                                          )
                                      )
                                    : optionGroup
                                }
                                menuHeight={100}
                                className="select2-selection"
                                formatCreateLabel={formatCreateLabel}
                                onInputChange={(newValue) => {
                                  setSearchQuery(newValue);
                                }}
                                customOnChange={(selectedOption, onChange) => {
                                  if (
                                    Number(watch("transaction")?.value) === 1 &&
                                    selectedOption?.__isNew__
                                  ) {
                                    setAddNewProductFromSelect({
                                      label: selectedOption.label,
                                      value: 0,
                                    });
                                  } else {
                                    setSelectedId(selectedOption?.value);
                                  }
                                  onChange(selectedOption);
                                }}
                              />
                            </div>
                          </Col>
                        )}
                      </div>
                      <div>
                        {/* <p>{t("common.quant")}</p> */}
                        {!isShow && (
                          // <div className="w-100 d-flex gap-2 align-items-center">
                          <CustomInput
                            control={control}
                            name="quant"
                            // value={isUpdate.current ? quant : null}
                            isDisabled={isShow}
                            type="number"
                            className="w-100"
                            // label={selectedQuant || "---"}
                            label={t("common.quant") || "---"}
                            error={hasError.value}
                          />
                          // </div>
                        )}
                      </div>
                      <div>
                        {/* <p>{t("common.maintenance_quantity")}</p> */}
                        {!isShow && (
                          // <div className="d-flex gap-2 align-items-center">
                          <CustomInput
                            control={control}
                            name="maintenance_quantity"
                            isDisabled={!watch("products")?.value || isShow}
                            placeholder={t("common.maintenance_quantity")}
                            type="number"
                            className="w-100"
                            error={errors?.maintenance_quantity}
                          />
                          // </div>
                        )}
                      </div>
                      <button
                        type="button"
                        className="btn btn-primary"
                        style={{ width: "fit-content" }}
                        title="add product"
                        disabled={
                          !productName &&
                          !addNewProductFromSelect?.label &&
                          !hasError.value
                        }
                        onClick={handelAddProduct}
                      >
                        {t("common.add_to_table")}
                      </button>
                    </div>
                  )}
                  <TableContainer
                    hideSHowGFilter
                    columns={columns || []}
                    data={rowData || []}
                    isPagination={false}
                    iscustomPageSize={false}
                    isBordered={true}
                    isSmall
                    hidePagination
                    customPageSize={10}
                    isAddOptions={false}
                    className="custom-header-css table align-middle table-nowrap"
                    tableClassName="table-centered align-middle table-nowrap mb-0"
                    theadClassName="text-muted table-light"
                  />
                </Col>
              </Row>
            </div>
          )}
          <Modal
            isOpen={openDeleteMdal}
            toggle={handelCLoseModal}
            backdrop="static"
          >
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.reject")} {t("common.request")}
            </ModalHeader>
            <ModalBody>
              <Row>
                <p>{t("common.delete_text")}</p>
                <Col xs={12}>
                  <div className="mb-2">
                    <textarea
                      type="text"
                      rows={4}
                      className={`form-control ${
                        errors.notes?.message ? "is-invalid" : ""
                      }`}
                      placeholder={t("store-request.reason_reject")}
                      {...register(`notes`)}
                    />
                    {/* {errors.notes} */}
                    {errors.notes && (
                      <div className="invalid-feedback">
                        {errors.notes?.message}
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
              <ModalFooter>
                <Button
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                  className="btn-sm"
                >
                  {t("common.close")}
                </Button>
                <Button
                  onClick={rejectStatus}
                  type="button"
                  color="danger"
                  className="btn-sm"
                >
                  {isRejected ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.reject")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
          <Modal
            isOpen={opeAcceptModal}
            toggle={handelCLoseModal}
            backdrop="static"
          >
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.accept")} {t("common.request")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <Col xs={12}>
                <div className="mb-2">
                  <textarea
                    type="text"
                    rows={4}
                    className={`form-control ${
                      errors.notes?.message ? "is-invalid" : ""
                    }`}
                    placeholder={t("store-request.reason_accept")}
                    {...register(`notes`)}
                  />
                  {/* {errors.notes} */}
                  {errors.notes && (
                    <div className="invalid-feedback">
                      {errors.notes?.message}
                    </div>
                  )}
                </div>
              </Col>
              <ModalFooter>
                <Button
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                  className="btn-sm"
                >
                  {t("common.close")}
                </Button>
                <Button
                  onClick={handelAcceptRequest}
                  type="button"
                  color="success"
                  className="btn-sm"
                >
                  {isAccepted ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.accept")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              justifyContent: "space-between",
              position: "sticky",
              bottom: 0,
            }}
          >
            <div className="d-flex align-items-center gap-2">
              <Button
                type="button"
                color="light"
                className="btn-sm "
                style={{ height: "32px", width: "54px" }}
                onClick={() => {
                  handelCancel();
                  setOpenAddModal(false);
                }}
              >
                {t("common.close")}
              </Button>
              {/* <Can permission={selectId ? "client.update" : "client.store"}> */}
              {!isShow && (
                <Button
                  color="primary"
                  className="btn-sm waves-effect waves-light primary-button"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <ClipLoader color="white" size={15} />
                  ) : selectId ? (
                    t("common.update")
                  ) : (
                    t("common.save")
                  )}
                </Button>
              )}
            </div>
            <div className="d-flex align-items-center gap-2 text-center">
              {!isShow && selectId > 0 && data?.result?.status === 1 && (
                <Button
                  color="danger"
                  // className="waves-effect waves-light btn-danger"
                  // color
                  type="button"
                  className="btn-sm"
                  style={{ height: 30 }}
                  // disabled={isRejected}
                  onClick={() => setOpenDeleteModal(true)}
                >
                  <p>{t("common.reject")}</p>
                </Button>
              )}
              {!isShow && selectId > 0 && data?.result?.status === 1 && (
                <Button
                  color="success"
                  className="btn-sm"
                  style={{ height: 30 }}
                  // className="waves-effect waves-light btn-success"

                  type="button"
                  onClick={() => setOpeAcceptModal(true)}
                  // disabled={isAccepted}
                >
                  <p>{t("common.accept")}</p>
                </Button>
              )}
            </div>
            {/* </Can> */}
          </div>
        </form>
      </Row>
    </div>
  );
};
export default ClientActions;
