import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Col,
  Container,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useEffect, useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import { BondsAPis } from "../../apis/bound/api";
import { BondsQueries } from "../../apis/bound/query";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { clientsQueries } from "../../apis/clients/query";
import {
  formatDate,
  getTodayDate,
  handleBackendErrors,
  today,
} from "../../helpers/api_helper";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { carLogsQueries } from "../../apis/car-logs/query";
import { Can } from "../../components/permissions-way/can";
import CustomInput from "../../components/Common/Input";
import CustomSelect from "../../components/Common/Select";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

const BondType = () => {
  const { pathname } = useLocation();
  const tabNumber = localStorage.getItem("tab_number");

  const [optionGroup, setOptionGroup] = useState([]); // Assuming you have a way to populate this
  const [optionBondTypesGroup, setOptionBondTypesGroup] = useState([]); // Assuming you have a way to populate this
  const [selectId, setSelectId] = useState(null);
  const [selectedBondType, setSelectedBondType] = useState();
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openAddModel, setOpenAddModel] = useState(false);
  const [show, setIsShow] = useState(false);
  const { t, i18n } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };
  const [isDeleting, setIsDeleting] = useState(false);

  const handelSelectId = (id) => {
    setSelectId(id);
  };
  const navigate = useNavigate();

  const { data: clients } = clientsQueries.useGetAll({ status: 1 });

  const { data: carsLogsList } = carLogsQueries.useGetAll({
    un_paid: Number(selectId) ? 2 : 1,
    bond_id: selectId ? Number(selectId) : null,
  });

  const handelAddBonds = () => {
    if (selectedBondType === 2) {
      if (carsLogsList?.result?.length === 0) {
        toastr.info(t("bonds.select_car_validation"));
      } else {
        navigate("/action-bonds");
      }
    } else {
      setOpenAddModel(true);
    }
  };

  const {
    data: ContractTypes,
    isLoading: isLoadingUsers,
    refetch,
  } = BondsQueries.useGetAll({
    limit: pageSize,
    page: currentPage,
    bond_type: Number(selectedBondType),
  });

  const paymentTypeOption = [
    { id: 1, title: t("bonds.cash") },
    { id: 2, title: t("bonds.cheque") },
    { id: 3, title: t("bonds.visa") },
  ];

  const initialSTate = {
    client_id: null,
    bond_date: formatDate(today),
    total: null,
    bond_type: paymentTypeOption[0],
    payment_type: paymentTypeOption[0],
    operation_number: "",
  };

  useEffect(() => {
    if (tabNumber) {
      setSelectedBondType(Number(tabNumber));
    } else {
      setSelectedBondType(1);
    }
  }, [tabNumber]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedBondType]);

  const schema = yup.object({
    total: yup
      .number()
      .min(1, t("bonds.min_totla"))
      .max(999999, t("bonds.max_total"))
      .integer(t("common.integer"))
      .required(t("common.field_required"))
      .typeError(t("common.valid_number")),
    operation_number: yup.string().when("payment_type.value", {
      is: (val) => val === "2" || val === "3" || val === 2 || val === 3, // handle both string and number
      then: () => yup.string().required(t("common.field_required")),
      otherwise: () => yup.string().nullable(),
    }),
    client_id:
      tabNumber !== "3" &&
      yup
        .object()
        .shape({
          label: yup.string().required(),
          value: yup.string().required(),
        })
        .transform((value, originalValue) => {
          // if not an object (e.g., 0 or ""), return undefined to trigger `.required()`
          return typeof originalValue === "object" && originalValue !== null
            ? originalValue
            : undefined;
        })
        .required(t("common.field_required")),
    bond_type: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required(t("common.field_required")),
    payment_type: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required(t("common.field_required")),
  });

  const {
    reset,
    formState: { errors },
    setError,
    control,
    watch,

    handleSubmit,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setSelectId(null);
    setOpenAddModel(false);
    setIsShow(false);
    reset({
      bond_date: formatDate(today),
      client_id: 0,
      total: "",
      bond_type: null,
      operation_number: "",
      payment_type: paymentTypeOption[0],
    });
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };

  useEffect(() => {
    if (clients?.result?.length > 0) {
      setOptionGroup(
        clients.result.map((item) => ({
          label: `${item.company_name || "---"}/${item.full_name || "---"}`,
          value: item.id,
        }))
      );
    }
  }, [clients?.result]);

  const { data: bondTypes } = BondsQueries.useGetAllBoundsType({
    bond_type: Number(selectedBondType),
    enabled: openAddModel && selectedBondType !== 2,
  });

  useEffect(() => {
    if (bondTypes?.result?.length > 0) {
      setOptionBondTypesGroup(
        bondTypes.result?.map((item) => ({
          label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
          value: item?.id,
        }))
      );
    }
  }, [bondTypes?.result]);

  const breadcrumbItems = [
    {
      title: t("bonds.voucher"),
      link: pathname,
    },
  ];

  const { data, isLoading: isLoadingBond } = BondsQueries.useGetOne({
    id: Number(selectId),
  });

  useEffect(() => {
    if (data) {
      reset({
        total: data?.result?.total,
        bond_date: data?.result?.bond_date.split(" ")[0],
        operation_number: data?.result?.operation_number,
        payment_type: {
          value: data?.result?.payment_type,
          label:
            data?.result?.payment_type === 1
              ? t("bonds.cash")
              : data?.result?.payment_type === 2
              ? t("bonds.cheque")
              : t("bonds.visa"),
        },
        client_id: {
          value: data?.result?.client?.id,
          label:
            (data?.result?.client?.full_name
              ? data?.result?.client?.full_name
              : "----") +
            "/" +
            data?.result?.client?.company_name
              ? data?.result?.client?.company_name
              : "---",
        },
        bond_type: {
          label:
            i18n.language === "eng"
              ? data?.result.bond_type.title.en
              : data?.result.bond_type.title.ar,
          value: data?.result.bond_type.id,
        },
      });
    }
  }, [data?.result]);

  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.voucher_number"),
        accessor: "bond_number",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.voucher_date"),
        accessor: "bond_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.voucher_type"),
        accessor: "bond_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bonds.operation_number"),
        accessor: "operation_number",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <div className="d-flex align-items-center gap-2">
              <Can permission={"bond.update"}>
                <Link
                  to={
                    selectedBondType === 2 && `/action-bonds?id=${cellProps.id}`
                  }
                  className="text-primary"
                  onClick={() => {
                    if (selectedBondType !== 2) {
                      setSelectId(cellProps.id);
                      setOpenAddModel(true);
                    }
                  }}
                >
                  {/* <i className="mdi mdi-pencil font-size-18"></i> */}
                  <FaPenToSquare size={14} />
                </Link>
              </Can>
              <Can permission={"bond.destroy"}>
                {cellProps.is_default !== 1 && (
                  <Link
                    onClick={() => {
                      if (cellProps.isDefault !== 1) {
                        handelOpenModal();
                        handelSelectId(cellProps.id);
                      }
                    }}
                    to="#"
                    className="text-danger"
                  >
                    {/* <i className="mdi mdi-trash-can font-size-18"></i> */}
                    <MdDeleteSweep size={18} />
                  </Link>
                )}
              </Can>
              <Can permission={"bond.show"}>
                <Link
                  to={
                    selectedBondType === 2 &&
                    `/action-bonds?id=${cellProps.id}?Show=true`
                  }
                  className="text-success"
                  onClick={() => {
                    if (selectedBondType !== 2) {
                      setSelectId(cellProps.id);
                      setOpenAddModel(true);
                      setIsShow(true);
                    }
                  }}
                >
                  {/* <i className=" ri-information-fill font-size-16"></i> */}
                  <FaInfoCircle size={14} />
                </Link>
              </Can>
            </div>
          );
        },
        disableFilters: true,
        filterable: false,
      },
    ];

    // Conditionally add the "Client" or "Car" column
    if (selectedBondType !== 3) {
      baseColumns.splice(1, 0, {
        Header: selectedBondType === 2 ? t("common.cars") : t("common.client"),
        accessor: selectedBondType === 2 ? "cars" : "client_id",
        disableFilters: true,
        filterable: false,
      });
    }

    return baseColumns;
  }, [
    selectedBondType,
    setSelectId,
    setOpenAddModel,
    setIsShow,
    i18n.language,
  ]);

  const rowData = useMemo(
    () =>
      ContractTypes?.result?.length > 0
        ? ContractTypes.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (currentPage - 1) * 10 + index + 1, // 10 is your page size
              client_id: item?.client?.company_name
                ? item?.client?.full_name
                  ? item?.client?.full_name
                  : "---" +
                    "/" +
                    (item?.client?.company_name
                      ? item?.client?.company_name
                      : "---")
                : "---",
              bond_number: item.bond_number,
              bond_date: item.bond_date,
              total: item.total,
              operation_number:
                item.operation_number === "0"
                  ? "---"
                  : item.operation_number || "---",
              bond_type:
                i18n.language === "eng"
                  ? item?.bond_type?.title?.en
                  : item?.bond_type?.title?.ar,
              cars:
                item?.cars.length > 0 ? (
                  item?.cars.length <= 3 ? (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: 6,
                      }}
                    >
                      {item?.cars.map((item) => (
                        <p
                          key={item.id}
                          style={{
                            background: "#eee",
                            borderRadius: 10,
                            padding: 10,
                          }}
                        >
                          {item.name}
                        </p>
                      ))}
                    </div>
                  ) : (
                    t("bonds.many_cars")
                  )
                ) : (
                  "---"
                ),
            }))
            .reverse()
        : [],
    [ContractTypes?.result, i18n.language, currentPage, pageSize]
  );

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await BondsAPis.deleteBond({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
    // Call API with selected permissions (data.permissions)
  };

  const Add = async (data) => {
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });
      setIsDeleting(true);
      const response = await BondsAPis.add({
        payload: {
          client_id: data.client_id?.value,
          bond_type_id: Number(data.bond_type?.value),
          bond_date: data.bond_date + " " + formattedTime,
          total: data.total,
          payment_type: Number(data.payment_type.value),
          operation_number: data.operation_number,
        },
      });
      refetch();
      toastr.success(response.message);
      setIsDeleting(false);
      reset({
        bond_date: formatDate(today),
        client_id: 0,
        total: "",
        bond_type: null,
        operation_number: "",
        payment_type: paymentTypeOption[0],
      });

      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  const update = async (data) => {
    try {
      const now = new Date(Date.now());
      const formattedTime = now.toLocaleTimeString("en-GB", {
        hour12: false,
      });
      setIsDeleting(true);
      const response = await BondsAPis.update({
        payload: {
          client_id: data.client_id?.value,
          bond_type_id: Number(data.bond_type?.value),
          bond_date: data.bond_date + " " + formattedTime,
          total: data.total,
          payment_type: Number(data.payment_type.value),
          operation_number: data.operation_number,
        },
        id: selectId,
      });
      refetch();
      reset({
        bond_date: formatDate(today),
        client_id: 0,
        total: "",
        bond_type: null,
        operation_number: "",
        payment_type: paymentTypeOption[0],
      });

      toastr.success(response.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error, setError });
    }
  };

  const reasonsLis = [
    { id: 1, title: t("clients.clients") },
    { id: 2, title: t("common.cars") },
    { id: 3, title: t("common.other") },
  ];

  const handelSelect = (id) => {
    setSelectedBondType(id);
    localStorage.setItem("tab_number", id);
  };

  const currenttoday = getTodayDate();

  return (
    <div className="page-content">
      <Container fluid style={{ height: "100%" }}>
        <Breadcrumbs
          title={t("bonds.voucher")}
          breadcrumbItems={breadcrumbItems}
          addTitle={t("common.add") + " " + t("bonds.voucher")}
          canPermission={"bond.store"}
          isAddOptions={true}
          handleOrderClicks={handelAddBonds}
        />
        <Card style={{ maxHeight: "90%", height: "90%", overflowY: "auto" }}>
          <CardBody>
            <TableContainer
              hideSHowGFilter={false}
              columns={columns || []}
              data={rowData || []}
              customPageSize={pageSize}
              addTitle={t("common.add") + " " + t("bonds.voucher")}
              // isAddOptions
              disabledAddTitle={
                selectedBondType === 2 && carsLogsList?.result?.length === 0
              }
              customHeight={"60vh"}
              canPermission={"bond.store"}
              handleOrderClicks={handelAddBonds}
              className="custom-header-css table align-middle table-nowrap"
              tableClassName="table-centered align-middle table-nowrap mb-0"
              theadClassName="text-muted table-light"
              customComponent={
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: 10,
                  }}
                >
                  {reasonsLis.map((item) => (
                    <p
                      style={{
                        background:
                          item.id === selectedBondType ? "rgb(28 187 140)" : "",
                        color: item.id === selectedBondType ? "#fff" : "",
                        paddingInline: 20,
                        paddingBlock: 10,
                        borderRadius: 40,
                        border: "1px solid #ccc",
                        cursor: "pointer",
                      }}
                      onClick={() => handelSelect(item.id)}
                      key={item.id}
                    >
                      {item.title}
                    </p>
                  ))}
                </div>
              }
              manualPagination={true}
              pageCount={ContractTypes?.meta?.last_page || 1}
              currentPage={currentPage}
              setPage={setCurrentPage}
              isLoading={isLoadingUsers}
            />
          </CardBody>
        </Card>
      </Container>
      <Modal isOpen={openAddModel} toggle={handelCLoseModal} backdrop="static">
        <form onSubmit={selectId ? handleSubmit(update) : handleSubmit(Add)}>
          <ModalHeader toggle={handelCLoseModal}>
            {show
              ? t("common.show") + " " + t("bonds.voucher")
              : selectId
              ? t("common.update") + " " + t("bonds.voucher")
              : t("common.add") + " " + t("bonds.voucher")}
          </ModalHeader>
          <ModalBody>
            {isLoadingBond ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : (
              <Row>
                <Col xs={6} className="mb-4">
                  <CustomSelect
                    name="payment_type"
                    control={control}
                    isDisabled={show || selectId}
                    placeholder={t("bonds.payment_type")}
                    options={paymentTypeOption.map((item) => ({
                      label: item.title,
                      value: item.id.toString(),
                    }))}
                    error={errors?.payment_type}
                  />
                </Col>
                {selectedBondType === 1 && (
                  <Col xs={6} className="mb-4">
                    <CustomSelect
                      name="client_id"
                      control={control}
                      isDisabled={selectId || show}
                      options={optionGroup}
                      placeholder={t("common.client")}
                      error={errors?.client_id}
                    />
                  </Col>
                )}
                <Col xs={6} className="mb-4">
                  <CustomSelect
                    name="bond_type"
                    control={control}
                    isDisabled={selectId || show}
                    options={optionBondTypesGroup}
                    placeholder={t("bonds.select_bond_type")}
                    error={errors?.bond_type}
                  />
                </Col>
                <Col>
                  <div className="mb-4">
                    <CustomInput
                      name="bond_date"
                      control={control}
                      type="date"
                      placeholder={t("bonds.voucher_date")}
                      error={errors?.bond_date}
                      isDisabled={show}
                      max={currenttoday}
                    />
                  </div>
                </Col>
                <Col xs={6}>
                  <div className="mb-4">
                    <CustomInput
                      name="total"
                      control={control}
                      type="number"
                      placeholder={t("common.value")}
                      error={errors?.total}
                      isDisabled={show}
                    />
                  </div>
                </Col>
                {(Number(watch("payment_type")?.value) === 2 ||
                  Number(watch("payment_type")?.value) === 3) && (
                  <Col xs={6}>
                    <div className="mb-4">
                      <CustomInput
                        name="operation_number"
                        control={control}
                        type="text"
                        placeholder={t("bonds.operation_number")}
                        error={errors?.operation_number}
                        isDisabled={show}
                      />
                    </div>
                  </Col>
                )}
              </Row>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCLoseModal}
            >
              {t("common.close")}
            </Button>
            {!show && (
              <Button
                disabled={isDeleting}
                onClick={selectId ? update : Add}
                type="submit"
                color="primary"
                className="btn-sm"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : selectId ? (
                  t("common.update")
                ) : (
                  t("common.add")
                )}
              </Button>
            )}
          </ModalFooter>
        </form>
      </Modal>
      <Modal
        isOpen={openDeleteMdal}
        toggle={handelCLoseModal}
        backdrop="static"
      >
        <ModalHeader toggle={handelCLoseModal}>
          {t("common.delete")} {t("bonds.voucher")}
        </ModalHeader>
        <ModalBody>
          <p>{t("common.delete_text")}</p>
          <ModalFooter>
            <Button
              className="btn-sm"
              type="button"
              color="light"
              onClick={handelCLoseModal}
            >
              {t("common.close")}
            </Button>
            <Button
              disabled={isDeleting}
              onClick={DeleteFun}
              type="button"
              className="btn-sm"
              color="danger"
            >
              {isDeleting ? (
                <ClipLoader color="white" size={15} />
              ) : (
                t("common.delete")
              )}
            </Button>
          </ModalFooter>
        </ModalBody>
      </Modal>
    </div>
  );
};
export default BondType;
