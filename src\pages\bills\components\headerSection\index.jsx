import { useTranslation } from "react-i18next";
import { FormField } from "../../action-bill";
import useSetSelectOptions from "../../../../hooks/use-set-select-options";
import CustomSelect from "../../../../components/Common/Select";
import { Col } from "reactstrap";
import TextAreaField from "../../../../components/Common/textArea";
import { delegateQueries } from "../../../../apis/delegate/query";
import { clientsQueries } from "../../../../apis/clients/query";

const HeaderSection = ({ errors, isShow, register, control, selectId }) => {
  const { t, i18n } = useTranslation();
  const { data: clients } = clientsQueries.useGetAll({ status: 1 });
  const { data: delegates } = delegateQueries.useGetAll({ status: 1 });

  const delegateOptions = useSetSelectOptions({
    data: delegates?.result,
    getOption: (item) => ({
      label: item.full_name, // Dynamic label property
      value: item.id, // Assuming there's an id
    }),
  });

  const clientOptions = useSetSelectOptions({
    data: clients?.result,
    getOption: (item) => ({
      label: `${item.company_name}/${item.full_name || "---"}`,
      value: item.id,
    }),
  });
  const fieldsNames = [
    {
      name: "bill_date",
      isRequired: false,
      errorMessage: errors?.bill_date?.message,
      label: t("bills.bill_date"),
      type: "date",
      placeholder: "",
      showIn: true,
    },
    {
      name: "res_name",
      isRequired: false,
      errorMessage: errors?.res_name?.message,
      label: t("common.responsible"),
      type: "text",
      showIn: true,
      disabled: isShow,
    },
  ];

  const textAreaField = [
    {
      id: 0,
      name: "note",
      label: t("common.note"),
      isRequired: false,
      showIn: true,
    },
  ];
  return (
    <>
      {fieldsNames.map(
        (field) =>
          field.showIn && (
            <FormField
              key={field.id}
              field={field}
              register={register}
              errors={errors}
              isShow={isShow}
              isDisabled={field.disabled || field.disable}
              t={t}
              control={control}
              placeholder={field.label}
            />
          )
      )}

      <Col xs={2}>
        <CustomSelect
          control={control}
          error={errors.client_id}
          label={t("common.client")}
          options={clientOptions}
          name="client_id"
          isDisabled={isShow || selectId}
        />
      </Col>

      <Col xs={2}>
        <CustomSelect
          control={control}
          error={errors.delegate_id}
          label={t("common.delegate")}
          options={delegateOptions}
          name="delegate_id"
          isDisabled={isShow}
        />
      </Col>

      {textAreaField.map((field) => (
        <Col key={field.id} xs={5}>
          <TextAreaField
            name="notes"
            control={control}
            placeholder={t("common.note")}
            defaultValue=""
            className="mb-2"
            rows={1}
            disabled={isShow}
            error={errors?.notes}
          />
        </Col>
      ))}
    </>
  );
};

export default HeaderSection;
