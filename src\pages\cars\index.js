import { useTranslation, withTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Container,
  Input,
  Modal,
  Modal<PERSON>ody,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useMemo, useState } from "react";
import ClipLoader from "react-spinners/ClipLoader";
import toastr from "toastr";
import {
  handleBackendErrors,
  hasPermission,
  truncateText,
} from "../../helpers/api_helper";
import { MdDeleteSweep } from "react-icons/md";
import { FaInfoCircle } from "react-icons/fa";
import { FaPenToSquare } from "react-icons/fa6";

import "./user.scss";
import { UsersQueries } from "../../apis/cars/query";
import { USERSAPis } from "../../apis/cars/api";
import { Link, useNavigate } from "react-router-dom";
import { Can } from "../../components/permissions-way/can";

const Users = () => {
  const [page, setPage] = useState(1);
  const {
    data: Users,
    isLoading: isLoadingUsers,
    refetch,
  } = UsersQueries.useGetAllUsers({ limit: 10, page: page });

  const { t } = useTranslation();

  const navigate = useNavigate();
  const [selectId, setSelectId] = useState(null);
  const [openDeleteMdal, setOpenDeleteModal] = useState(false);
  const [openStatsusModal, setOpenStatsusModal] = useState(false);
  const [statusIsActive, setstausIsActive] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handelCLoseModal = () => {
    setOpenDeleteModal(false);
    setOpenStatsusModal(false);
    setSelectId(null);
  };
  const handelOpenModal = () => {
    setOpenDeleteModal(true);
  };

  const handelAddCars = () => {
    navigate("/action-cars");
  };

  toastr.options = {
    positionClass: "toast-top-right",
    timeOut: 5000,
    extendedTimeOut: 1000,
    closeButton: true,
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
    hideDuration: 1000,
  };
  const handelCloseSideBar = () => {
    setSelectId(null);
    setIsDeleting(false);
  };
  const handelSelectId = (id) => {
    setSelectId(id);
  };

  const DeleteFun = async () => {
    try {
      setIsDeleting(true);
      const response = await USERSAPis.deleteUser({
        id: selectId,
      });
      refetch();
      toastr.success(response.message);
      handelCloseSideBar();
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const ActiveUser = async (id) => {
    try {
      setIsDeleting(true);
      const resposne = await USERSAPis.activeUser({
        id: id,
      });
      refetch();
      toastr.success(resposne.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      // toastr.error("There are error");
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };
  const InActive = async (id) => {
    try {
      setIsDeleting(true);
      const resposne = await USERSAPis.inActiveUser({
        id: id,
      });
      refetch();
      // toastr.success("in Active done");
      toastr.success(resposne.message);
      setIsDeleting(false);
      handelCLoseModal();
    } catch (error) {
      setIsDeleting(false);
      handleBackendErrors({ error });
      console.log("error", error);
    }
    // Call API with selected permissions (data.permissions)
  };

  const breadcrumbItems = [
    { title: t("common.cars"), link: "/cars" },
    // { title: "list", link: "#" },
  ];

  const handelToggleStatus = ({ cellProps }) => {
    if (cellProps.status === 1 && hasPermission("car.disactivate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(false);
    } else if (hasPermission("car.activate")) {
      setSelectId(cellProps.id);
      setOpenStatsusModal(true);
      setstausIsActive(true);
    }
  };

  const columns = useMemo(
    () => [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      // {
      //   Header: t("common.image"),
      //   accessor: "image",
      //   disableFilters: true,
      //   filterable: false,
      // },
      {
        Header: t("bonds.car_name"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("cars.modal"),
        accessor: "model",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("cars.plate"),
        accessor: "plate",
        disableFilters: true,
        filterable: false,
      },
      // {
      //   Header: "Purchase year",
      //   accessor: "purchase_year",
      //   disableFilters: true,
      //   filterable: false,
      // },
      {
        Header: t("common.delegate"),
        accessor: "delegate",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("cars.car_card_expire"),
        accessor: "car_card_expire",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("cars.examination_expiration_date"),
        accessor: "examination_expiration_date",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.last_km_update"),
        accessor: "last_km_update",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.last_km"),
        accessor: "last_km",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.status"),
        disableFilters: true,
        filterable: false,
        accessor: (cellProps) => {
          return (
            <div className="form-check form-switch">
              <Input
                type="checkbox"
                className="form-check-input"
                // defaultChecked={cellProps.status === 1 ? true : false}
                // onClick={() => handelToggleStatus({ cellProps })}
                checked={cellProps.status === 1}
                onClick={() => handelToggleStatus({ cellProps })}
              />
              <></>
            </div>
          );
        },
      },
      {
        Header: t("common.actions"),
        accessor: (cellProps) => {
          return (
            <>
              <div className="d-flex align-items-center gap-2">
                <Can permission={"car.update"}>
                  <Link
                    to={
                      cellProps.is_default !== 1 &&
                      `/action-cars?id=${cellProps.id}`
                    }
                    className="text-primary"
                    onClick={() => {}}
                  >
                    {/* <i className="mdi mdi-pencil font-size-16"></i> */}
                    <FaPenToSquare size={14} />
                  </Link>
                </Can>
                <Can permission={"car.destroy"}>
                  <Link
                    onClick={() => {
                      if (cellProps.isDefault !== 1) {
                        handelOpenModal();
                        handelSelectId(cellProps.id);
                      }
                    }}
                    to="#"
                    className="text-danger"
                  >
                    {/* <i className="mdi mdi-trash-can font-size-16"></i> */}
                    <MdDeleteSweep size={18} />
                  </Link>
                </Can>
                <Can permission={"car.show"}>
                  <Link
                    to={`/action-cars/?id=${cellProps.id}?Show=true`}
                    className="text-success"
                  >
                    {/* <i className=" ri-information-fill font-size-16"></i> */}
                    <FaInfoCircle size={14} />
                  </Link>
                </Can>
              </div>
            </>
          );
        },
        disableFilters: true,
        filterable: false,
      },
    ],
    [Users?.result]
  );

  const rowData = useMemo(
    () =>
      Users?.result?.length > 0
        ? Users.result
            .map((item, index) => ({
              id: item.id,
              id_toShow: (page - 1) * 10 + index + 1, // 10 is your page size
              name: truncateText({
                text: item.name,
                maxLengthPercent: 0.3,
              }),
              model: item.model,
              plate: item.plate,
              last_km_update: item.last_km_update || "---",
              last_km: item.last_km >= 0 ? item.last_km : "---",
              note: item.note || "---",
              image: item.image || "---",
              status: item.status,
              delegate: item?.used?.delegate || "---",
              examination_expiration_date:
                item?.inspection_expiry_date || "---",
              car_card_expire: item?.license_expiry_date || "---",
            }))
            .reverse()
        : [],
    [Users?.result, t]
  );

  return (
    <div className="page-content">
      <Container fluid style={{ height: "90%" }}>
        <Breadcrumbs
          title={t("common.cars")}
          breadcrumbItems={breadcrumbItems}
          isAddOptions={true}
          addTitle={t("common.add") + " " + t("common.cars")}
          handleOrderClicks={handelAddCars}
          canPermission={"car.store"}
        />
        <Card
          style={{
            maxHeight: "100%",
            height: "100%",
            overflowY: "auto",
            padding: 20,
          }}
        >
          {/* <CardBody> */}
          {/* {isLoadingUsers ? (
              <div className="container-loading">
                <ClipLoader color="#ddd" size={50} />
              </div>
            ) : ( */}
          <TableContainer
            hideSHowGFilter={false}
            columns={columns || []}
            data={rowData || []}
            isPagination={true}
            // isGlobalFilter={true}
            iscustomPageSize={true}
            isLoading={isLoadingUsers}
            isBordered={true}
            pageSize={10}
            pageIndex={page}
            manualPagination={true}
            pageCount={Users?.meta?.last_page || 1}
            currentPage={page}
            setPage={setPage}
            // className="custom-header-css table align-middle table-nowrap"
            // tableClassName="table-centered align-middle table-nowrap mb-0"
            // theadClassName="text-muted table-light"
          />
          {/* )} */}
          {/* </CardBody> */}
        </Card>
        <Modal
          isOpen={openDeleteMdal}
          toggle={handelCLoseModal}
          backdrop="static"
        >
          <ModalHeader toggle={handelCLoseModal}>
            {t("common.delete")} {t("common.car")}
          </ModalHeader>
          <ModalBody>
            <p>{t("common.delete_text")}</p>
            <ModalFooter>
              <Button
                className="btn-sm"
                type="button"
                color="light"
                onClick={handelCLoseModal}
              >
                {t("common.close")}
              </Button>
              <Button
                className="btn-sm"
                onClick={DeleteFun}
                type="button"
                color="danger"
              >
                {isDeleting ? (
                  <ClipLoader color="white" size={15} />
                ) : (
                  t("common.delete")
                )}
              </Button>
            </ModalFooter>
          </ModalBody>
        </Modal>
        {openStatsusModal && selectId && (
          <Modal isOpen={openStatsusModal} backdrop="static">
            <ModalHeader toggle={handelCLoseModal}>
              {t("common.Attention")}
            </ModalHeader>
            <ModalBody>
              <p>{t("common.delete_text")}</p>
              <ModalFooter>
                <Button
                  className="btn-sm"
                  type="button"
                  color="light"
                  onClick={handelCLoseModal}
                >
                  {t("common.no")}
                </Button>
                <Button
                  onClick={() =>
                    statusIsActive ? ActiveUser(selectId) : InActive(selectId)
                  }
                  disabled={isDeleting}
                  type="button"
                  color="primary"
                  className="btn-sm"
                >
                  {isDeleting ? (
                    <ClipLoader color="white" size={15} />
                  ) : (
                    t("common.yes")
                  )}
                </Button>
              </ModalFooter>
            </ModalBody>
          </Modal>
        )}
      </Container>
    </div>
  );
};
export default Users;
