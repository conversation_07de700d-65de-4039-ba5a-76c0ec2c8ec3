import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { But<PERSON>, Col, Row } from "reactstrap";
import { useTranslation } from "react-i18next";
import { handleBackendErrors } from "../../helpers/api_helper";
import toastr from "toastr";
import { TermsAPis } from "../../apis/terms/api";
import CustomInput from "../../components/Common/Input";
import { sectionsQueries } from "../../apis/terms/query";
import TextAreaField from "../../components/Common/textArea";

const TermsActions = ({
  isShow,
  selectId,
  handelClose,
  refetch,
  sectionId,
}) => {
  const { t } = useTranslation();
  const taskId = selectId;
  const schema = yup.object({}).required();

  const initialSTate = {
    name: "",
    language: null,
    contract_type_id: null,
    is_active: 0,
    is_editable: 0,
    order: null,
  };

  const {
    handleSubmit,
    reset,
    register,
    formState: { isSubmitting, errors },
    setError,
    control,
    watch,
    setValue,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const { data, isLoading } = sectionsQueries.useGet({
    id: Number(taskId),
  });

  useEffect(() => {
    if (taskId && !isLoading && data?.result) {
      const item = data.result;
      reset({
        title: item?.text,
        is_active: item?.is_active || 0,
        is_editable: item?.is_editable || 0,
        order: item?.order || 0,
      });
    }
  }, [selectId, isLoading, data]);

  const UpdateFun = async (data) => {
    try {
      const dataForSend = {
        text: data.title,
        is_active: data.is_active ? 1 : 0,
        is_editable: data.is_editable ? 1 : 0,
        order: Number(data.order),
      };
      const response = await TermsAPis.update({
        payload: dataForSend,
        id: selectId,
      });
      handelClose();
      toastr.success(response.message);
      refetch();
      reset();
      setValue("order", "");
      setValue("title", "");
      setValue("is_active", 0);
      setValue("is_editable", 0);
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const dataForSend = {
        text: data.title,
        is_active: data.is_active ? 1 : 0,
        is_editable: data.is_editable ? 1 : 0,
        order: Number(data.order),
      };
      const response = await TermsAPis.add({
        payload: dataForSend,
        id: sectionId,
      });
      toastr.success(response.message);
      refetch();
      reset();
      setValue("order", "");
      setValue("title", "");
      setValue("is_active", 0);
      setValue("is_editable", 0);
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div style={{ borderTop: "1px solid #ccc", marginTop: 15 }}>
      <div className="d-flex align-items-center justify-content-between gap-2 mb-3 mt-2">
        <h1 style={{ fontSize: 15 }}>{t("common.terms_of_section")}</h1>
        {!isShow && (
          <Button
            color="primary"
            onClick={handleSubmit(selectId ? UpdateFun : addFun)}
            disabled={isSubmitting || !watch("title") || !watch("order")}
            className="btn-sm"
          >
            {isSubmitting ? (
              <span className="spinner-border spinner-border-sm" />
            ) : (
              t("common.save_term")
            )}
          </Button>
        )}
      </div>
      <Row className="g-3">
        <Col xs={12}>
          <TextAreaField
            name="title"
            control={control}
            placeholder={t("common.text")}
            defaultValue=""
            className="mb-2"
            rows={2}
            disabled={isShow}
            error={errors?.title}
          />
        </Col>
        <Col xs={12} className="d-flex align-items-center gap-2">
          <CustomInput
            control={control}
            error={errors.order?.errorMessage}
            isDisabled={isShow}
            placeholder={t("common.order")}
            name="order"
            type="number"
          />

          <div className="form-check">
            <input
              type="checkbox"
              className="form-check-input"
              id="is_active_terms"
              disabled={isShow}
              {...register("is_active")}
            />
            <label className="form-check-label" htmlFor="is_active_terms">
              {t("common.is_active")}
            </label>
          </div>

          <div className="form-check">
            <input
              type="checkbox"
              className="form-check-input"
              id="is_editable_terms"
              disabled={isShow}
              {...register("is_editable")}
            />
            <label className="form-check-label" htmlFor="is_editable_terms">
              {t("common.is_editable")}
            </label>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default TermsActions;
