import ApiInstance from "../../constant/api-instance";
import { CAR_LOGS_ROUTES } from "./route";

const getAll = async ({ limit, page, un_paid, bond_id, searchParams }) => {
  const { data } = await ApiInstance.get(CAR_LOGS_ROUTES.CAR_LOG, {
    params: {
      limit,
      page,
      "filter[un_paid]": un_paid,
      bond_id,
      ...searchParams,
    },
  });
  return data;
};

const getOne = async ({ id }) => {
  const { data } = await ApiInstance.get(`${CAR_LOGS_ROUTES.CAR_LOG}/${id}`);
  return data;
};

const addUser = async ({ payload }) => {
  const { data } = await ApiInstance.post(
    `${CAR_LOGS_ROUTES.CAR_LOG}`,
    payload,
    {}
  );
  return data;
};

const updateUser = async ({ payload, id }) => {
  const { data } = await ApiInstance.put(
    `${CAR_LOGS_ROUTES.CAR_LOG}/${id}`,
    payload,
    {}
  );
  return data;
};

const deleteUser = async ({ id }) => {
  const { data } = await ApiInstance.delete(`${CAR_LOGS_ROUTES.CAR_LOG}/${id}`);
  return data;
};

export const carLogsAPis = {
  getAll,
  getOne,
  deleteUser,
  updateUser,
  addUser,
};
