import ClipLoader from "react-spinners/ClipLoader";
import { Card, CardBody, Container, Button, Row, Col } from "reactstrap";
import TableContainer from "../../components/Common/TableContainer";
import { useMemo, useState } from "react";
import Breadcrumbs from "../../components/Common/Breadcrumb";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { DelegateReportStatus } from "../../constant/constants";
import { delegateReportsQueries } from "../../apis/delegate-reports/query";
import "./delegate-reports.css";
import SearchCard from "../../components/Reports/search-card";
import { useForm } from "react-hook-form";

const DelegateReports = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const selectId = Number(queryParams.get("id")?.split("?")[0]);

  const { register, control, reset, handleSubmit, watch, setValue } = useForm();

  const { data, isLoading } = delegateReportsQueries.useGetAll({});

  const [filterParams, setFilterParams] = useState({});

  const handleSearch = (data) => {
    setFilterParams(data);
    // Implement search logic here
  };

  const handleReset = () => {
    reset();
    setFilterParams({});
    // Reset filters logic
  };

  // Search options
  const searchData = [
    {
      id: 1,
      name: "client",
      label: t("common.client"),
      options:
        data?.result[0]?.filtered_bills?.map((bill) => ({
          value: bill.res_name,
          label: bill.res_name,
        })) || [],
      defaultValue: "",
    },
    {
      id: 2,
      name: "product",
      label: t("common.product"),
      options:
        data?.result[0]?.filtered_bills?.flatMap((bill) =>
          bill.details.map((detail) => ({
            value: detail.product.name,
            label: detail.product.name,
          }))
        ) || [],
      defaultValue: "",
    },
  ];

  const inputsArray = [
    {
      id: 1,
      name: "bill_number",
      label: t("bills.bill_number"),
      type: "text",
    },
  ];

  const getFilteredBills = (bills, filterParams) => {
    if (!bills) return [];
    let filtered = bills;
    if (
      filterParams.bill_number &&
      String(filterParams.bill_number).trim() !== ""
    ) {
      filtered = filtered.filter((bill) =>
        bill.bill_number?.toString().includes(filterParams.bill_number)
      );
    }
    if (filterParams.client && String(filterParams.client).trim() !== "") {
      filtered = filtered.filter(
        (bill) => bill.res_name === filterParams.client
      );
    }
    if (filterParams.product && String(filterParams.product).trim() !== "") {
      filtered = filtered.filter((bill) =>
        bill.details.some(
          (detail) => detail.product.name === filterParams.product
        )
      );
    }
    return filtered;
  };

  const filteredBills = useMemo(() => {
    return getFilteredBills(data?.result[0]?.filtered_bills, filterParams);
  }, [data?.result, filterParams]);

  const rowData = useMemo(() => {
    if (!filteredBills) return [];
    const transformedData = [];
    filteredBills.forEach((bill, billIndex) => {
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        bill_number: bill.bill_number,
        bill_type:
          i18n.language === "eng"
            ? bill.bill_type?.title?.en
            : bill.bill_type?.title?.ar,
        client: bill.res_name,
        product_name: "",
        quant: "",
        gift: "",
        price: "",
        total: bill.total,
        notes: bill.notes || "----",
        isParent: true,
      };
      parentRow.subRows = bill.details.map((detail, detailIndex) => ({
        id: `${bill.id}-${detail.id}`,
        id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
        bill_number: "",
        bill_type: "",
        client: "",
        product_name: detail.product.name,
        quant: detail.quant,
        gift: detail.gift,
        price: detail.price,
        total: detail.total,
        notes: detail.notes || "---",
        isSubRow: true,
        parentId: bill.id,
      }));
      transformedData.push(parentRow);
    });
    return transformedData.reverse();
  }, [filteredBills, i18n.language]);

  const totalCatchBond = useMemo(() => {
    return filteredBills.reduce(
      (acc, bill) => acc + (bill.total_catch_bonds || 0),
      0
    );
  }, [filteredBills]);

  const totalPaymentBond = useMemo(() => {
    return filteredBills.reduce(
      (acc, bill) => acc + (bill.total_payment_bonds || 0),
      0
    );
  }, [filteredBills]);

  const columns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}>{}</div>;
          }
          return value;
        },
      },
      {
        Header: t("bills.bill_number"),
        accessor: "bill_number",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("bills.bill_type"),
        accessor: "bill_type",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.product"),
        accessor: "product_name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("common.gift"),
        accessor: "gift",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("reports.individual_price"),
        accessor: "price",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("reports.total_price"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return <strong>{value}</strong>;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
    ],
    [t]
  );

  // دالة فلترة الفواتير حسب القيم المدخلة

  const catchBondColumns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bonds.voucher_number"),
      accessor: "bond_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.payments"),
      accessor: "payments",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.price"),
      accessor: "price",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "notes",
      disableFilters: true,
      filterable: false,
    },
  ]);

  const catchBondRowData = useMemo(
    () =>
      data?.result[0].payment_bonds
        .map((item, index) => ({
          id: item.id,
          id_toShow: index + 1,
          payments:
            i18n.language === "eng"
              ? item.bill_type?.title?.en
              : item.bill_type?.title?.ar,
          price: item.total,
          notes: item.notes,
        }))
        .reverse(),
    [data?.result, t]
  );

  const SummaryColumns = useMemo(() => [
    {
      Header: "#",
      width: 50,
      accessor: "id_toShow",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("bonds.voucher_number"),
      accessor: "bond_number",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("clients.client"),
      accessor: "client",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.cash_received"),
      accessor: "cash_received",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: () => (
        <div style={{ textAlign: "center", width: "100%" }}>
          <span style={{ paddingBottom: 8 }}>
            {t("reports.cheque_received")}
          </span>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              alignItems: "center",
              marginTop: 10,
            }}
          >
            <span
              style={{
                textAlign: "center",
                borderBlockEndWidth: 1,
                borderInlineEnd: "1px solid #ccc",
              }}
            >
              {t("bonds.operation_number")}
            </span>
            <span style={{ textAlign: "center" }}>{t("common.price")}</span>
          </div>
        </div>
      ),
      accessor: "cheque_received",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: (
        <div style={{ textAlign: "center", width: "100%" }}>
          <span style={{ paddingBottom: 8 }}>{t("reports.visa_received")}</span>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              alignItems: "center",
              marginTop: 10,
            }}
          >
            <span
              style={{
                textAlign: "center",
                borderBlockEndWidth: 1,
                borderInlineEnd: "1px solid #ccc",
              }}
            >
              {t("bonds.operation_number")}
            </span>
            <span style={{ textAlign: "center" }}>{t("common.price")}</span>
          </div>
        </div>
      ),
      accessor: "visa_received",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("reports.total_received"),
      accessor: "total_received",
      disableFilters: true,
      filterable: false,
    },
    {
      Header: t("common.note"),
      accessor: "notes",
      disableFilters: true,
      filterable: false,
    },
  ]);

  const SummatyRowData = useMemo(
    () =>
      data?.result[0].catch_bonds
        .map((item, index) => ({
          id: item.id,
          id_toShow: index + 1,
          client: item?.client?.full_name,
          cash_received: item.payment_type === 1 ? item.total : "---",
          cheque_received: (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  textAlign: "center",
                  borderBlockEndWidth: 1,
                  borderInlineEnd: "1px solid #ccc",
                }}
              >
                {item.operation_number || "---"}
              </span>
              <span style={{ textAlign: "center" }}>
                {item.payment_type === 2 ? item.total : "---"}
              </span>
            </div>
          ),
          visa_received: (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  textAlign: "center",
                  borderBlockEndWidth: 1,
                  borderInlineEnd: "1px solid #ccc",
                }}
              >
                {item.operation_number || "---"}
              </span>
              <span style={{ textAlign: "center" }}>
                {item.payment_type === 3 ? item.total : "---"}
              </span>
            </div>
          ),
          total_received: totalPaymentBond || "---",
          notes: item.notes || "---",
          bond_number: item.bond_number,
        }))
        .reverse(),
    [data?.result, t]
  );

  // دالة فلترة جداول chang_oil_bills حسب الفلاتر
  const getFilteredChangeOilBills = (bills, filterParams) => {
    if (!bills) return [];
    let filtered = bills;
    if (filterParams.client && String(filterParams.client).trim() !== "") {
      filtered = filtered.filter(
        (bill) => bill.client.full_name === filterParams.client
      );
    }
    if (filterParams.product && String(filterParams.product).trim() !== "") {
      filtered = filtered.filter((bill) =>
        bill.details.some(
          (detail) => detail.product.name === filterParams.product
        )
      );
    }
    return filtered;
  };

  // جداول تغيير الزيت
  const changeOilRowData = useMemo(() => {
    if (!data?.result[0]?.chang_oil_bills) return [];
    const filtered = getFilteredChangeOilBills(
      data.result[0].chang_oil_bills,
      filterParams
    );
    const transformedData = [];
    filtered.forEach((bill, billIndex) => {
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        notes: bill.notes || "---",
        client: bill.client.full_name || "---",
      };
      parentRow.subRows = bill.details.map((detail, detailIndex) => ({
        id: `${bill.id}-${detail.id}`,
        id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
        product_name: detail.product.name || "---",
        quant: detail.change_oil === 0 ? detail.quant : "---",
        return: detail.change_oil === 1 ? detail.quant : "---",
      }));
      transformedData.push(parentRow);
    });
    return transformedData.reverse();
  }, [data?.result, i18n.language, filterParams]);

  const sampleRowData = useMemo(() => {
    if (!data?.result[0]?.chang_oil_bills) return [];
    const filtered = getFilteredChangeOilBills(
      data.result[0].chang_oil_bills,
      filterParams
    );
    const transformedData = [];
    filtered.forEach((bill, billIndex) => {
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        notes: bill.notes || "---",
        client: bill.client.full_name,
        bill_type:
          (i18n.language === "eng"
            ? bill.bill_type.title?.en
            : bill.bill_type.title?.en) || "---",
      };
      parentRow.subRows = bill.details.map((detail, detailIndex) => ({
        id: `${bill.id}-${detail.id}`,
        id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
        name: detail.product.name || "---",
        quant: bill.bill_type.type === 1 ? 2000 : "---",
        return: bill.bill_type.type === 9 ? detail.quant : "---",
      }));
      transformedData.push(parentRow);
    });
    return transformedData.reverse();
  }, [data?.result, i18n.language, filterParams]);

  const maintainced_billsRowData = useMemo(() => {
    if (!data?.result[0]?.chang_oil_bills) return [];
    const filtered = getFilteredChangeOilBills(
      data.result[0].chang_oil_bills,
      filterParams
    );
    const transformedData = [];
    filtered.forEach((bill, billIndex) => {
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        notes: bill.notes || "---",
        client: bill.client.full_name,
        bill_type:
          (i18n.language === "eng"
            ? bill.bill_type.title?.en
            : bill.bill_type.title?.ar) || "---",
        bill_number: bill.bill_number || "---",
      };
      parentRow.subRows = bill.details.map((detail, detailIndex) => ({
        id: `${bill.id}-${detail.id}`,
        id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
        name: detail.product.name || "---",
        quant: detail.quant || "---",
        total: detail.total || "---",
      }));
      transformedData.push(parentRow);
    });
    return transformedData.reverse();
  }, [data?.result, i18n.language, filterParams]);

  const contractssRowData = useMemo(() => {
    if (!data?.result[0]?.chang_oil_bills) return [];
    const filtered = getFilteredChangeOilBills(
      data.result[0].chang_oil_bills,
      filterParams
    );
    const transformedData = [];
    filtered.forEach((bill, billIndex) => {
      const parentRow = {
        id: bill.id,
        id_toShow: billIndex + 1,
        notes: bill.notes || "---",
        client: bill.client.full_name,
        contract_number: bill.contract_number || "---",
      };
      parentRow.subRows = bill.details.map((detail, detailIndex) => ({
        id: `${bill.id}-${detail.id}`,
        id_toShow: `${billIndex + 1}.${detailIndex + 1}`,
        name: detail.product.name || "---",
        quant: detail.quant || "---",
        total: detail.total || "---",
      }));
      transformedData.push(parentRow);
    });
    return transformedData.reverse();
  }, [data?.result, i18n.language, filterParams]);

  const getting_summary = data?.result?.map((item) => ({
    cash: item.summary.cash,
    visa: item.summary.visa,
    cheque: item.summary.cheque,
  }));

  // إعادة تعريف الأعمدة التي تم حذفها بالخطأ
  const changeOilColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}></div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("reports.Perfume_name"),
        accessor: "product_name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.bottle"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.return"),
        accessor: "return",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const sampleColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}></div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("products.product_name"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.bottle"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.return"),
        accessor: "return",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{ color: "#666" }}>{value}</div>;
          }
          return value;
        },
      },
      {
        Header: t("types.bill.bill_types"),
        accessor: "bill_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const materialMovementColumns = useMemo(
    () => [
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("reports.material_name"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("reports.input_quant"),
        accessor: "inQuant",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("reports.out_quant"),
        accessor: "outQuant",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("reports.balance"),
        accessor: "totalQuant",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const materialMovementRowData = useMemo(
    () =>
      data?.result[0].products_totals
        .map((item, index) => ({
          id: item.product_id,
          id_toShow: index + 1,
          name: item.product_name,
          outQuant: item.outQuant,
          totalQuant: item.totalQuant,
          inQuant: item.inQuant,
        }))
        .reverse(),
    [data?.result, t]
  );

  const maintainced_billsColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}></div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("bills.bill_number"),
        accessor: "bill_number",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("types.bill.bill_types"),
        accessor: "bill_type",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("products.products"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  const contractsColumns = useMemo(
    () => [
      {
        id: "expander",
        Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) => (
          <Button
            {...getToggleAllRowsExpandedProps()}
            color="link"
            size="sm"
            className="p-0"
          >
            <span style={{ marginInlineEnd: 4 }}>
              {t("reports.Expand_all")}
            </span>
            {isAllRowsExpanded ? (
              <i className="mdi mdi-chevron-down"></i>
            ) : (
              <i className="mdi mdi-chevron-right"></i>
            )}
          </Button>
        ),
        Cell: ({ row }) => {
          if (!row.original.subRows?.length) return null;
          return (
            <Button
              {...row.getToggleRowExpandedProps()}
              color="link"
              size="md"
              className="p-0"
            >
              {row.isExpanded ? (
                <i className="mdi mdi-chevron-down"></i>
              ) : (
                <i className="mdi mdi-chevron-right"></i>
              )}
            </Button>
          );
        },
        width: 30,
        disableFilters: true,
        filterable: false,
      },
      {
        Header: "#",
        width: 50,
        accessor: "id_toShow",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return <div style={{}}></div>;
          }
          return value;
        },
      },
      {
        Header: t("clients.client"),
        accessor: "client",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("contracts.contract_number"),
        accessor: "contract_number",
        disableFilters: true,
        filterable: false,
      },
      {
        Header: t("products.products"),
        accessor: "name",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.quant"),
        accessor: "quant",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("reports.total"),
        accessor: "total",
        disableFilters: true,
        filterable: false,
        Cell: ({ row, value }) => {
          if (row.original.isSubRow) {
            return "";
          }
          return value;
        },
      },
      {
        Header: t("common.notes"),
        accessor: "notes",
        disableFilters: true,
        filterable: false,
      },
    ],
    [t]
  );

  return (
    <div className="page-content">
      <Container fluid>
        <Breadcrumbs
          title={t("reports.show_delegate_report")}
          breadcrumbItems={[]}
        />
        <div className="p-2" style={{ background: "#fff" }}>
          <Row className="mb-2">
            <Col xs={12}>
              <div>
                <SearchCard
                  SearchData={searchData}
                  control={control}
                  inputsArray={inputsArray}
                  register={register}
                  hadelReset={handleReset}
                  handelSearch={handleSubmit(handleSearch)}
                  watch={watch}
                  setValue={setValue}
                />
              </div>
            </Col>
          </Row>
        </div>
        <Row>
          <Col lg={12}>
            <Card className="">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={columns || []}
                    data={rowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.daily_sales_report")}
                      </h4>
                    }
                    isSmall
                    isPagination={true}
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>

          <Col lg={12}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={SummaryColumns || []}
                    data={SummatyRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.payment_summary")}
                      </h4>
                    }
                    isPagination={true}
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    isSmall
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={catchBondColumns || []}
                    data={catchBondRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.catch_summary")}
                      </h4>
                    }
                    isPagination={true}
                    iscustomPageSize={true}
                    isBordered={true}
                    isSmall
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>

          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={changeOilColumns || []}
                    data={changeOilRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("types.bill.change_oil")}
                      </h4>
                    }
                    isPagination={true}
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    isSmall
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={sampleColumns || []}
                    data={sampleRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("types.bill.sample_bill")}
                      </h4>
                    }
                    isPagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>

          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={materialMovementColumns || []}
                    data={materialMovementRowData || []}
                    isSmall
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.material_movement_summary")}
                      </h4>
                    }
                    isPagination={true}
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={maintainced_billsColumns || []}
                    data={maintainced_billsRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.maintenance_billing_report")}
                      </h4>
                    }
                    isPagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>

          <Col lg={6}>
            <Card className="h-100">
              <CardBody className="small-text">
                {isLoading ? (
                  <div className="container-loading">
                    <ClipLoader color="#ddd" size={50} />
                  </div>
                ) : (
                  <TableContainer
                    hidePagination
                    hideSHowGFilter={false}
                    columns={contractsColumns || []}
                    data={contractssRowData || []}
                    customComponent={
                      <h4 className="small-font">
                        {t("reports.trusteeship_contract_report")}
                      </h4>
                    }
                    isPagination={true}
                    isSmall
                    iscustomPageSize={true}
                    isBordered={true}
                    customPageSize={5}
                    pageOptions={[5, 10, 20]}
                    className="custom-header-css table align-middle table-nowrap small-text"
                    tableClassName="table-centered align-middle table-nowrap mb-0 small-font"
                    theadClassName="text-muted table-light"
                  />
                )}
              </CardBody>
            </Card>
          </Col>
        </Row>
        <Row className="mt-4">
          <Col lg={12}>
            <Card className="w-fit-content">
              <CardBody className="small-text">
                <h6>{t("reports.revenue_summary")}</h6>

                <ul
                  style={{
                    border: "1px solid #ccc",

                    borderRadius: "10px",
                    width: "fit-content",
                  }}
                >
                  {getting_summary &&
                    getting_summary?.map((item, index) => (
                      <li
                        key={index}
                        style={{
                          listStyle: "none",
                          display: "grid",
                          gridTemplateColumns: "1fr 1fr 1fr",
                          alignItems: "center",
                          gap: 10,
                          justifyContent: "center",
                        }}
                      >
                        <span style={{ padding: "10px" }}>
                          Cash: {item.cash}
                        </span>
                        <span
                          style={{
                            borderInlineStart: "1px solid #ccc",
                            padding: "10px",
                          }}
                        >
                          Visa: {item.visa}
                        </span>
                        <span
                          style={{
                            borderInlineStart: "1px solid #ccc",
                            padding: "10px",
                          }}
                        >
                          Cheque: {item.cheque}
                        </span>
                      </li>
                    ))}
                </ul>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};
export default DelegateReports;
