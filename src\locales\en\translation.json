{"common": {"type": "type", "actions": "Actions", "add": "Add", "update": "Update", "title": "Title", "title_in_english": "Title in english", "title_in_arabic": "Title in arabic", "description_in_english": "Description in english", "description_in_arabic": "Description in arabic", "close": "Close", "create": "Create", "id": "ID", "show": "Show", "delete_text": "Are you sure for do this action?", "delete": "Delete", "total": "total", "submit_error": "You must agree before submitting.", "count": "count", "no": "No", "yes": "Yes", "Attention": "Attention", "status": "status", "active": "Active", "in_active": "Not active", "no-data": "No data to show", "true": "true", "false": "False", "cars": "Cars", "image": "image", "name": "Name", "last_km": "Last km", "last_km_update": "Last km update", "full_name": "Full Name", "select_image": "Select image", "note": "Note", "notes": "Notes", "car": "Car", "km": "Km", "delegate": "Delegate", "select": "Select", "roles": "Roles", "select_all": "Select All", "email": "Email", "phone_number": "Phone number", "visit_number": "Visit number", "quant": "Quant", "icon": "Icon", "unit": "Unit", "price": "Price", "description": "Description", "maintenance_quantity": "Maintenance Quantity", "save": "Save", "password": "Password", "tasks": "Tasks", "approve": "Approve", "transfer": "Transfer", "cancel": "Cancel", "another": "Another", "reject": "Reject", "accept": "Accept", "client": "Client", "rows_total": "Rows total", "rows_discount": "Rows discount", "responsible": "Responsible", "restore": "Rest<PERSON>", "product": "product", "gift": "Gift", "discount": "Discount", "net_price": "Net price", "contract": "Contract", "value": "Value", "date_not_before_today": "Must be at least today", "percentage": "Percentage", "decline": "Decline", "in_progress": "In progress", "field_required": "this field is required", "label_required": "Label is required", "value_required": "Value is required", "start_date": "Start date", "end_date": "End date", "request": "Request", "other": "Other", "make": "Make", "add_to_table": "Add to table", "received": "Received", "not_received": "not received", "rejected": "rejected", "view_details": "View details", "group": "Group", "select_reason": "select Reason", "duplicate": "Duplicate", "types": "Types", "users_and_ole": "Users and Role", "roles_permissions": "Roles permissions", "users": "Users", "store": "Store", "dashboard": "Dashboard", "profile": "Profile", "my_wallet": "My Wallet", "settings": "settings", "logout": "Logout", "states": "States", "cities": "Cities", "locations": "Locations", "regions": "Regions", "update_states": "Update States", "add_states": "Add States", "name_of_state": "Name of state", "name_of_city": "Name of City", "city": "City", "name_of_location": "Name of Location", "login": "Log In", "404_error": "Sorry, page not found", "back_to_dashboard": "Back to Dashboard", "interval_error": "Internal Server Error", "no_data": "No data available", "page": "Page", "of": "Of", "user": "user", "payment": "Payment", "catch": "catch", "state": "state", "location": "location", "delegate_reports": "Delegate Reports", "vat": "Tax", "vat_rate": "Tax rate", "vat_status": "Tax status", "operation_date": "Operation date", "operation_type": "Operation type", "delegate_name": "Delegate name", "total_price": "Total price", "total_price_unit": "Unit price", "search": "Search", "reset": "Reset", "filter_by": "Filter <PERSON>", "pending": "Pending", "accepted": "Accepted", "date": "Date", "hours": "Hour", "minuets": "Minute", "details": "Details", "one": "one", "previous": "Previous", "next": "Next", "attention": "attentions", "confirm_password": "Confirm password", "residence_expiry_date": "Residence expiry date to", "company": "Company", "residence": "Residence", "type_of_accommodation": "Type of accommodation", "passwords_must_match": "Passwords must match", "comparison": "Comparison", "greater_than": "Greater than", "less_than": "Less than", "equal_to": "Equal to", "without_group": "Without Group", "client_status": "Client status", "date_error": "Start date cannot be greater than end date", "end_date_error": "end date cannot be less than start date", "invalid_date_format": "Invalid date format", "end_date_before_start": "End date cannot be before the start date", "valid_number": "Please enter a valid number", "integer": "Must to be valid number", "send_warranty": "Send warranty card", "admin": "Admin", "amount": "Contract amount", "remove_all": "Remove all", "upload_image": "Upload image", "prodile_iamge": "Profile Image", "update_profile": "Update profile", "old_password": "Old password", "delete_all": "Delete All", "drop_files_here_or_click_to_upload": "Drop files here or click to upload", "edit_image": "Edit Image", "delete_image": "Delete Image", "image_preview": "Image Preview", "max_images_reached": "Maximum number of images reached", "please_delete_existing_image": "Please delete existing image first", "contract_template": "Contract template", "language": "Language", "arabic": "Arabic", "english": "English", "sections": "Sections", "terms": "Terms", "is_active": "Is Active", "order": "order", "is_editable": "Is editable", "text": "Text", "save_term": "Save Term", "section": "Section", "terms_of_section": "Terms of this section", "for": "for", "back_to_template": "back to templates", "sections_with_terms": "Sections with terms", "map": "Map", "visit_loation_start": "Location start visit", "visit_loation_end": "Location end visit", "trem": "Term", "reason_types": "Tasks status", "color": "Color", "_location": "location"}, "types": {"contract_types": {"contract_type": "Contract type", "contract_count": "Contract count", "sell_contract": "Sell contract", "rent_contract": "Rent Contract"}, "bonds": {"voucher": "Voucher", "payment_type": "Payment type", "sign": "sign"}, "units": {"products_units": "Products units"}, "product_types": {"product_type": "Product type", "product_groups": "Product Groups", "select_bill_types": "select Bill Types", "select_contract_types": "select contract Types"}, "bill": {"bill_types": "Bill types", "sample_bill": "<PERSON><PERSON>", "return_bill": "Return Bill", "pickup_maintenance_bill": "Pickup Maintenance Bill", "deliver_maintenance_bill": "Deliver Maintenance Bill", "change_oil": "Change Oil", "sales_bill": "Sales Bill", "contract_bill": "Contract Bill", "follow_up": "Follow Up", "return_sample_bill": "Return sample bill"}, "car_logs": {"start_use": "Start Use", "end_use": "End Use", "fill_fuel": "Fill Fuel", "fast_repair": "Fast Repair", "give_to_repair": "Give to Repair", "give_from_repair": "Give from Repair", "update_km": "Update KM", "car_logs_type": "Cra logs types", "car_log_type": "Cra log type", "car_logs_date": "Record creation date", "car_logs_time": "Record creation time"}, "store_operation": {"store_operation": "Store operation", "stores_operation": "store operation", "input": "Input", "expulsion": "Expulsion"}, "reason": {"reasons": "Reasons", "reason": "reason", "admin_approve": "Admin approval", "new": "New", "delaying": "Delaying", "delayed": "Delayed", "processing": "Processing", "done": "Done", "rejected": "Rejected", "accepted": "Accepted", "waiting": "Waiting", "cancelled": "Cancelled", "canceled": "Cancelled", "transfer": "Transfer", "reasons_list": "Reasons list", "task_reason": "Tasks reasons", "approval_reasons_list": "Not Approval Reasons List"}}, "cars": {"car_type": "Car type", "modal": "Modal", "plate": "Plate number", "purchase_km": "Purchase km", "purchase-year_input": "Purchase date", "car_Status": "Car status", "car_card_expire": "Car card expiration date", "examination_expiration_date": "Examination expiration date", "car_logs": "car logs", "car_log_type": "Car log type", "payment_amount": "Payment amount", "START_USE": "Iniciar <PERSON>", "END_USE": "<PERSON><PERSON><PERSON>", "FILL_FUEL": "Repostar Combustible", "FAST_REPAIR": "Reparación Rápida", "GIVE_TO_REPAIR": "En<PERSON>r a Taller", "GIVE_FROM_REPAIR": "<PERSON><PERSON><PERSON><PERSON>", "UPDATE_KM": "<PERSON><PERSON><PERSON><PERSON>", "liter_price": "Liter price", "total_price": "Total price", "select_car_logs_type": "Select car logs type", "validations": {"km_max": "Must be less then 1 milion"}, "used": "Used", "car_owner": "Car Owner"}, "roles": {"users_count": "Users count", "must_select_validation": "Must Select one Permission at least", "title_of_roles": "Title of Role", "select_your-permissions": "Select Your permissions"}, "clients": {"clients": "Clients", "client": "client", "company_name": "Company name", "client_group": "Clients groups", "next_visit_day": "Next visit day", "website_url": "Website url", "company_description": "Company description", "address": "Address", "voucher_total": "Voucher total", "select_group": "Select Group", "group_title": "Group title", "number_of_clients": "Number of Clients", "group": "Group", "warrantyed": "warranted", "not_warrantyed": "Not warranted", "sende_warrantyed": "Sended warranted", "warranty_status": "Warranty status", "client_product": "Clients Product", "group_description_in_arabic": "Group description in arabic", "validations": {"url": "The website url must be a valid URL.", "email": "The email must be a valid email address", "visit_number_min": "Must be greater then  or equal to 0", "visit_number_max": "Must be less then or equal to 28", "visit_number": "Must be between 1 and 3", "letter_only": "Enter a valid name (letters only, no diacritics)"}, "no_group": "No Group", "all": "All", "without_group": "Without Group", "with_group": "With Group"}, "products": {"products": "Products", "product_name": "Product name", "min_quantity": "Minimum Quantity Notification", "max_quantity": "Max Quantity", "default_add_quantity": "De<PERSON><PERSON> Add Quantity", "div_quant": "Division Quantity", "product_group": "Product Group", "product_unit": "Product Unit", "product_images": "Product images", "icon": "Icon", "start_quant": "Start quant"}, "delegate": {"clients_count": "Clients count", "delegate_car_status": "Delegate status", "un_active": "Un Active", "width_car": "With Car", "without_car": "Without car", "assign_car": "Assign car to delegate", "available_cars": "Available Cars", "validation": {"status_required": "Status is required", "km_min": "You must enter KM when using a car", "km_required": "KM is required", "cars_min": "You must select at least one car", "cars_required": "Car selection is required", "km_requeued": "please select km", "assign_validation": "please select cars and km"}}, "tasks": {"creating_type": "Creating type", "task_date": "Task date", "task_time": "Task Time", "deadline": "Deadline", "transfer_tasks": "Transfer task", "tasks_date": "Task date", "deadLine_task": "DeadLine Task", "task_type": "Task Type", "tasks_type": "Tasks type", "task_transaction": "Task transaction", "duo_after_task": "Due date must be after task date", "client_change": "Delegate is Change", "done-task": "Done task", "client_note": "client note", "admin_note": "task result", "card_number": "Card number", "product_code": "Product code", "updated_by": "Updated by", "reason_required": "Reason is required", "another_reason": "Another reason", "show_in_customer_dashboard": "Show in customer dashboard", "delegate_requierd": "Delegate is required"}, "bills": {"bills": "Bills", "bill_number": "<PERSON>", "bill_type": "Bill type", "response_name": "Response Name", "bill_date": "Bill date", "sample_bill": "<PERSON><PERSON>", "return_bill": "Return Bill", "pickup_maintenance_bill": "PickupMaintenance Bill", "deliver_maintenance_bill": "DeliverMaintenance Bill", "change_oil_bill": "ChangeOil Bill", "sales_bill": "Sales Bill", "contract_bill": "Contract Bill", "follow_up_bill": "Follow Up Bill", "bills_delete": "Bills for delete", "follow_up": "Follow Up", "pickup_maintenance_price": "Pickup maintenance price", "discount_validation": "discount must be less total", "discount_type": "Discount Type", "pickup_maintenance": "pickup bill", "discount_value": "Discount value", "discount_percentage": "Discount percentage", "total_before_tax": "Total before Tax", "change_maintenance_status": "Change maintenance status", "move_to_delegate": "Has it been delivered to the delegate?", "products_with_delegate": "Products with delegate", "customer_was_delevier": "The customer was delivered", "maintenance_done": "Is the maintenance done?", "transfer_to_maintenance": "Transfer to maintenance", "validation": {"greater_than_0": "The value must be greater than 0", "greater_than_5000": "The value must be less than 5000", "greater_than_10m": "The value must be less than 10000000", "multiple_of": "The entered number must be a multiple of {{value}}", "min_value": "The value must be greater than or equal to {{value}}", "max_value": "The value must be less than or equal to {{value}}", "max_percentage_value": "The biggest value for discount is 100", "select_product_validation": "At least one product must be selected.", "validation_fix": "Please fix all errors before add", "quant_required": "Quant Field is required", "change_oli_val": "Please Add  one restore and store change oil at least", "max_qunat": "quant is bigger then product count"}}, "contracts": {"contracts": "Contracts", "contract_number": "Contract number", "contract_type": "Contract type", "contract_start_date": "Contract start date", "contract_end_date": "Contract end date", "start_date_from": "Start date from", "start_date_to": "Start date to", "end_date_from": "End date from", "end_date_to": "End date to", "subscription_amount": "Subscription amount", "start_date_required": "Contract start date is required", "end_date_required": "Contract end date is required", "invalid_date_format": "Invalid date format", "end_date_before_start": "Contract end date cannot be before the start date", "start_date_after_end": "Start date cannot be after end date", "subscription_amount_positive": "Subscription amount must be a positive number", "visit_number_min": "Visit Number must be at least {{min}}", "visit_number_max": "Visit Number must be at most {{max}}", "visit_number_required": "Visit Number is required", "min_quant": "quant must more then 0", "add_other_products": "Add Other Products", "other_product": "Update Other product", "product_validation": "Please add product or other product", "steal_amount": "Steal amount", "other_product_name": "Other product name", "other_product_quant": "Other product quant"}, "offer": {"quotation": "Quotation", "product_validation": "Please add one product at least", "bidder_type": "Bidder type"}, "bonds": {"voucher": "Voucher", "Vouchers": "Vouchers", "voucher_number": "Voucher number", "voucher_type": "Voucher type", "voucher_date": "Voucher date", "operation_number_type": "Operation type number", "voucher_count": "Voucher count", "many_cars": "Many cars", "select_bond_type": "select bond type", "select_car_to_payment": "Select car logs to payment", "car_name": "Car name", "km_value": "Km value", "max_total": "Value must be less than 999999", "min_totla": "Value must be greater than 0", "payment_type": "Payment type", "cash": "Cash", "visa": "Visa", "cheque": "Cheque", "operation_number": "Operation Number", "select_car_validation": "Sorry, it is not possible to add a document without the presence of operations for the cars"}, "store-request": {"store_request": "Store request", "enter_store": "Enter into main store", "store_request-add": "Add store request", "out_store": "Out from main store", "enter_car": "Enter info car", "out_car": "Out info car", "created_by": "Created By", "transaction": "Transaction", "created_at": "Created at", "reason_reject": "Reason for reject", "reason_accept": "Reason for accept", "make_request": "Make request", "update_request": "Update request", "add_products_validation": "please add product", "store_operation_validation": "please select Store operation", "select_delegate_validation": "plea se select delegate", "add_qunat_validation": "Please add Maintenance quantity or quant", "add_new_option": "Add new option", "val_error": "Please make sure the fields is empty before", "validation": {"multiple_of": "The entered number must be a multiple of the number {{value}}", "min_value": "The value must be greater than or equal to {{value}}", "max_value": "The value must be less than or equal to {{value}}", "rejections_validation": "Reason for rejection is required", "there_are_product_to_update": "There are product to update"}}, "store_transactions": {"store_transactions": "Store transactions", "order_details": "Order details", "delegate_response": "Delegate response date", "delegate_status": "Delegate status", "select_product": "select Product", "update_this_product": "Update this product", "not_received": "Not Received Transactions"}, "login": {"welcome": "Welcome Back !", "helper_text": "Sign in to continue to Secnt World."}, "reports": {"reports": "Reports", "clients_info": "Clients info", "customer_admin_number": "Customer admin number", "material_classification": "Material classification", "symbol": "symbol", "unit": "Unit", "column_visibility": "Column visibility", "customize_columns": "Customize columns", "res_number": "Res number", "sales": "Sales Reports", "sales_date": "Bills Date", "sales_numbers": "Bills Numbers", "Bills_type": "Bills Type", "visit_reason": "Visit Reason", "product_Materil": "Name of the material", "store": "Delegate Store", "main_store": "Main Store", "immediate_visit": "Immediate Visit", "stor": "Store", "bonds": "Voucher Report", "receipt_number": "Receipt Number", "voucher_type": "Voucher Type", "voucher_date": "Voucher Date", "total_report": "Total report", "client_visit": "Client visit", "total_visit": "Total visit", "completed_visit": "Completed visit", "delayed_visit": "Delayed visit", "contract_report": "Contract report", "expired_contracts": "Expired contracts", "renewable": "Renewable", "expired": "Expired", "quant_heiger": "<PERSON><PERSON>", "quant_lower": "Quant lower", "price_heigher": "Price heigher", "price_lower": "Price lower", "total_lower": "Total Lower", "total_higher": "Total higher", "total_expenses": "Total expenses", "car_expenses": "Cars Expenses", "recieved_date": "Recieved date", "delievered_date": "Delievered date", "summary": "summary", "show_delegate_report": "Show delegate report", "individual_price": "Individual price", "total_price": "Total price", "payments": "Payments", "payment_summary": "Payment Summary", "cash_received": "Cash received", "cheque_received": "Cheque received", "visa_received": "Visa received", "total_received": "Total received", "catch_summary": "Catch Summary", "daily_sales_report": "Daily sales report", "return": "Return", "bottle": "The bottle", "Perfume_name": "Perfume name", "material_movement_summary": "Material Movement Summary", "input_quant": "Input_quant", "out_quant": "Out_quant", "balance": "Balance", "material_name": "Material name", "total": "Total", "maintenance_billing_report": "Maintenance Billing Report", "trusteeship_contract_report": "Trusteeship Contract Report", "Expand_all": "Expand all", "revenue_summary": "Revenue Summary", "Cash": "Cash", "visa": "Visa", "check": "Check", "store_name": "Store name"}, "visits": {"visit": "Visits", "visit_duration": "Visit of duration"}, "bill_reasons": {"bill_reason": "<PERSON>", "bills_reason": "<PERSON>", "accepted": "Accepted", "rejected": "Rejected"}, "permissions": {"groups": {"user": "Users", "contract_type": "Contract Types", "reason": "Reasons", "bill_type": "Bill <PERSON>s", "task_type": "Task Types", "product_type": "Product Types", "product_unit": "Product Units", "bond_type": "Bond Types", "store_operation": "Store Operations", "car_log_type": "Car Log Types", "billReason": "<PERSON>", "client": "Clients", "client_group": "Client Groups", "delegate": "Delegates", "car": "Cars", "car_log": "Car Logs", "product": "Products", "task": "Tasks", "bill": "Bills", "contract": "Contracts", "bond": "<PERSON><PERSON>", "store": "Store", "store_request": "Store Requests", "store_transaction": "Store Transactions", "follow_up_log": "Follow Up Logs", "report": "Reports", "reports": "Reports", "visit": "Visits", "role": "Roles", "permission": "Permissions", "dashboard": "Dashboard", "profile": "Profile", "setting": "Settings"}}}