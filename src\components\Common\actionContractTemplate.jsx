import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { But<PERSON>, Col, Row } from "reactstrap";
import { useTranslation } from "react-i18next";
import { handleBackendErrors } from "../../helpers/api_helper";
import useSetSelectOptions from "../../hooks/use-set-select-options";
import CustomSelect from "../../components/Common/Select";
import CustomInput from "../../components/Common/Input";
import toastr from "toastr";
import ClipLoader from "react-spinners/ClipLoader";
import { ContractQueries } from "../../apis/types/contract-type/query";
import { contractTemplateAPis } from "../../apis/contract_templete/api";
import { contractTemplateQueries } from "../../apis/contract_templete/query";
const ContractTemplateActions = ({
  isShow,
  selectId,
  handelClose,
  refetch,
}) => {
  const { t, i18n } = useTranslation();
  const taskId = selectId;
  const schema = yup.object({}).required();

  const initialSTate = {
    name: "",
    language: null,
    contract_type_id: null,
  };

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting, errors },
    setError,
    control,
  } = useForm({
    defaultValues: { ...initialSTate },
    resolver: yupResolver(schema),
  });

  const { data, isLoading } = contractTemplateQueries.useGet({
    id: Number(taskId),
  });

  const { data: contract_type } = ContractQueries.useGetAllContractTypes({});

  const options = [
    { label: t("common.arabic"), value: 1 },
    { label: t("common.english"), value: 2 },
  ];

  const contractTypeOptions = useSetSelectOptions({
    data: contract_type?.result,
    getOption: (item) => ({
      label: i18n.language === "eng" ? item?.title?.en : item?.title?.ar,
      value: item.id,
    }),
  });

  useEffect(() => {
    if (taskId && !isLoading && data?.result) {
      const item = data.result;
      reset({
        name: item?.name,
        contract_type_id: {
          label: item.contract_type?.title,
          value: item.contract_type?.id,
        },
        language: options[item?.language - 1],
      });
    }
  }, [selectId, isLoading, data]);

  const UpdateFun = async (data) => {
    try {
      const dataForSend = {
        name: data.name,
        language: data.language?.value,
        contract_type_id: data.contract_type_id?.value,
      };
      const response = await contractTemplateAPis.update({
        payload: dataForSend,
        id: selectId,
      });
      handelClose();
      toastr.success(response.message);
      refetch();
      reset();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  const addFun = async (data) => {
    try {
      const dataForSend = {
        name: data.name,
        language: data.language?.value,
        contract_type_id: data.contract_type_id?.value,
      };
      const response = await contractTemplateAPis.add({ payload: dataForSend });
      toastr.success(response.message);
      refetch();
      reset();
      handelClose();
    } catch (error) {
      handleBackendErrors({ error, setError });
    }
  };

  return (
    <div>
      <h1 style={{ fontSize: 16 }} className="mb-4">
        {isShow
          ? t("common.show") + " " + t("common.contract_template")
          : selectId
          ? t("common.update") + " " + t("common.contract_template")
          : t("common.add") + " " + t("common.contract_template")}
      </h1>
      {isLoading ? (
        <div className="container-loading">
          <ClipLoader color="#ddd" size={50} />
        </div>
      ) : (
        <Row className="g-3">
          <Col xs={6}>
            <CustomInput
              control={control}
              error={errors.name?.errorMessage}
              isDisabled={isShow}
              placeholder={t("common.name")}
              name="name"
            />
          </Col>
          <Col xs={6}>
            <CustomSelect
              control={control}
              error={errors.language}
              label={t("common.language")}
              options={options}
              name="language"
              isDisabled={isShow}
            />
          </Col>
          <Col xs={6}>
            <CustomSelect
              control={control}
              error={errors.contract_type_id}
              label={t("common.contract_template")}
              options={contractTypeOptions}
              name="contract_type_id"
              isDisabled={isShow}
            />
          </Col>
        </Row>
      )}

      <div className="d-flex justify-content-end gap-2 mt-3">
        <Button color="light" onClick={handelClose} className="btn-sm">
          {t("common.cancel")}
        </Button>
        {!isShow && (
          <Button
            color="primary"
            onClick={handleSubmit(selectId ? UpdateFun : addFun)}
            disabled={isSubmitting}
            className="btn-sm"
          >
            {isSubmitting ? (
              <span className="spinner-border spinner-border-sm" />
            ) : selectId ? (
              t("common.update")
            ) : (
              t("common.add")
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default ContractTemplateActions;
